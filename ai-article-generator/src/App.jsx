import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { NotificationProvider } from './contexts/NotificationContext';
import ProtectedRoute from './components/ProtectedRoute';
import AdminRoute from './components/AdminRoute';
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import Dashboard from './components/Dashboard';
import TaskList from './components/tasks/TaskList';
import StartArticle from './components/tasks/StartArticle';
import TaskEditor from './components/tasks/TaskEditor';
import Homepage from './components/pages/Homepage';
import Features from './components/pages/Features';
import Pricing from './components/pages/Pricing';
import TermsOfService from './components/pages/TermsOfService';
import PrivacyPolicy from './components/pages/PrivacyPolicy';
import RefundPolicy from './components/pages/RefundPolicy';
import Settings from './components/pages/Settings';
import Profile from './components/pages/Profile';
import Blog from './components/pages/Blog';
import BlogTest from './components/pages/BlogTest';
import BlogPost from './components/pages/BlogPost';
import PillarPage from './components/pages/PillarPage';
import BlogAdmin from './components/blog/BlogAdmin';
import BlogPostEditor from './components/blog/BlogPostEditor';
import AdminDashboard from './components/admin/AdminDashboard';
import UserManagement from './components/admin/UserManagement';

import AdminTest from './components/AdminTest';
import AdminDashboardSimple from './components/AdminDashboardSimple';
import AdvancedBatchGenerator from './components/AdvancedBatchGenerator';
import Layout from './components/Layout';
import AuthenticatedRedirect from './components/AuthenticatedRedirect';
import ResourcesPage from './components/management/ResourcesPage';
import AuthorsPage from './components/management/AuthorsPage';
import ProductsPage from './components/management/ProductsPage';

function App() {
  return (
    <AuthProvider>
      <NotificationProvider>
        <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
        <Routes>
          {/* Blog routes - put these first to avoid conflicts */}
          <Route path="/blog" element={<Blog />} />
          <Route path="/blog/post/:slug" element={<BlogPost />} />
          <Route path="/pillar/:slug" element={<PillarPage />} />
          
          {/* Public routes */}
          <Route path="/home" element={<AuthenticatedRedirect><Homepage /></AuthenticatedRedirect>} />
          <Route path="/features" element={<Features />} />
          <Route path="/pricing" element={<Pricing />} />
          <Route path="/terms" element={<TermsOfService />} />
          <Route path="/privacy" element={<PrivacyPolicy />} />
          <Route path="/refund" element={<RefundPolicy />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />

          {/* Protected routes with navigation */}
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <Layout>
                <Dashboard />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/settings" element={
            <ProtectedRoute>
              <Layout>
                <Settings />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/profile" element={
            <ProtectedRoute>
              <Layout>
                <Profile />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/tasks" element={
            <ProtectedRoute>
              <Layout>
                <TaskList />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/tasks/new" element={
            <ProtectedRoute>
              <Layout>
                <StartArticle />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/tasks/:taskId/edit" element={
            <ProtectedRoute>
              <Layout>
                <TaskEditor />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/tasks/:taskId" element={
            <ProtectedRoute>
              <Layout>
                <TaskEditor />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/advanced-batch-generator" element={
            <ProtectedRoute>
              <Layout>
                <AdvancedBatchGenerator />
              </Layout>
            </ProtectedRoute>
          } />

          {/* Management routes */}
          <Route path="/manage/resources" element={
            <ProtectedRoute>
              <Layout>
                <ResourcesPage />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/manage/authors" element={
            <ProtectedRoute>
              <Layout>
                <AuthorsPage />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/manage/products" element={
            <ProtectedRoute>
              <Layout>
                <ProductsPage />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/admin" element={
            <AdminRoute>
              <Layout>
                <AdminDashboard />
              </Layout>
            </AdminRoute>
          } />

          <Route path="/admin/users" element={
            <AdminRoute>
              <Layout>
                <UserManagement />
              </Layout>
            </AdminRoute>
          } />

          <Route path="/admin/blog" element={
            <AdminRoute>
              <Layout>
                <BlogAdmin />
              </Layout>
            </AdminRoute>
          } />

          <Route path="/admin/blog/new" element={
            <AdminRoute>
              <Layout>
                <BlogPostEditor />
              </Layout>
            </AdminRoute>
          } />

          <Route path="/admin/blog/edit/:id" element={
            <AdminRoute>
              <Layout>
                <BlogPostEditor />
              </Layout>
            </AdminRoute>
          } />

          <Route path="/admin-test" element={
            <ProtectedRoute>
              <Layout>
                <AdminTest />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/admin-simple" element={
            <ProtectedRoute>
              <Layout>
                <AdminDashboardSimple />
              </Layout>
            </ProtectedRoute>
          } />

          {/* Default redirect */}
          <Route path="/" element={<AuthenticatedRedirect><Navigate to="/home" replace /></AuthenticatedRedirect>} />

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/home" replace />} />
        </Routes>
        </Router>
      </NotificationProvider>
    </AuthProvider>
  );
}

export default App;
