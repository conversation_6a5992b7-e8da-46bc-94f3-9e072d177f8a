import React, { useState, useEffect } from 'react';
import { 
  ClockI<PERSON>, 
  CheckCircleIcon, 
  XCircleIcon, 
  ExclamationTriangleIcon,
  EyeIcon,
  TrashIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { apiCall } from '../utils/api';

const BatchTaskManager = () => {
  const [batchTasks, setBatchTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedTask, setSelectedTask] = useState(null);
  const [taskDetails, setTaskDetails] = useState(null);
  const [loadingDetails, setLoadingDetails] = useState(false);

  useEffect(() => {
    fetchBatchTasks();
    
    // Set up polling for active tasks
    const interval = setInterval(() => {
      fetchBatchTasks();
    }, 5000); // Poll every 5 seconds

    return () => clearInterval(interval);
  }, []);

  const fetchBatchTasks = async () => {
    try {
      const response = await apiCall('/api/batch-tasks', {
        method: 'GET'
      });
      setBatchTasks(response.batchTasks || []);
    } catch (error) {
      console.error('Error fetching batch tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTaskDetails = async (taskId) => {
    setLoadingDetails(true);
    try {
      const response = await apiCall(`/api/batch-tasks/${taskId}`, {
        method: 'GET'
      });
      setTaskDetails(response);
    } catch (error) {
      console.error('Error fetching task details:', error);
    } finally {
      setLoadingDetails(false);
    }
  };

  const deleteBatchTask = async (taskId) => {
    if (!confirm('Are you sure you want to delete this batch task? This action cannot be undone.')) {
      return;
    }

    try {
      await apiCall(`/api/batch-tasks/${taskId}`, {
        method: 'DELETE'
      });
      setBatchTasks(prev => prev.filter(task => task.id !== taskId));
      if (selectedTask === taskId) {
        setSelectedTask(null);
        setTaskDetails(null);
      }
    } catch (error) {
      console.error('Error deleting batch task:', error);
      alert('Failed to delete batch task');
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'processing':
        return <ArrowPathIcon className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'cancelled':
        return <ExclamationTriangleIcon className="h-5 w-5 text-gray-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const handleViewTask = (taskId) => {
    setSelectedTask(taskId);
    fetchTaskDetails(taskId);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <ArrowPathIcon className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600">Loading batch tasks...</span>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Batch Task Manager</h1>
        <p className="text-gray-600">Monitor and manage your batch article generation tasks</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Task List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Batch Tasks</h2>
          </div>
          
          <div className="divide-y divide-gray-200">
            {batchTasks.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                No batch tasks found. Create a batch generation task to get started.
              </div>
            ) : (
              batchTasks.map((task) => (
                <div key={task.id} className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        {getStatusIcon(task.status)}
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                          {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                        </span>
                      </div>
                      
                      <h3 className="font-medium text-gray-900 mb-1">{task.name}</h3>
                      
                      <div className="text-sm text-gray-500 space-y-1">
                        <div>Mode: {task.input_mode}</div>
                        <div>Articles: {task.completed_articles}/{task.total_articles}</div>
                        {task.status === 'processing' && (
                          <div className="flex items-center space-x-2">
                            <div className="w-32 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${task.progress}%` }}
                              ></div>
                            </div>
                            <span className="text-xs">{task.progress}%</span>
                          </div>
                        )}
                        <div>Created: {formatDate(task.created_at)}</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => handleViewTask(task.id)}
                        className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                        title="View Details"
                      >
                        <EyeIcon className="h-5 w-5" />
                      </button>
                      
                      {task.status !== 'processing' && (
                        <button
                          onClick={() => deleteBatchTask(task.id)}
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                          title="Delete Task"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Task Details */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Task Details</h2>
          </div>
          
          <div className="p-6">
            {!selectedTask ? (
              <div className="text-center text-gray-500 py-12">
                Select a batch task to view details
              </div>
            ) : loadingDetails ? (
              <div className="flex justify-center items-center py-12">
                <ArrowPathIcon className="h-6 w-6 animate-spin text-blue-500" />
                <span className="ml-2 text-gray-600">Loading details...</span>
              </div>
            ) : taskDetails ? (
              <div className="space-y-6">
                {/* Task Info */}
                <div>
                  <h3 className="font-medium text-gray-900 mb-3">Task Information</h3>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-2 text-sm">
                    <div><span className="font-medium">Status:</span> {taskDetails.batchTask.status}</div>
                    <div><span className="font-medium">Input Mode:</span> {taskDetails.batchTask.input_mode}</div>
                    <div><span className="font-medium">Total Articles:</span> {taskDetails.batchTask.total_articles}</div>
                    <div><span className="font-medium">Completed:</span> {taskDetails.batchTask.completed_articles}</div>
                    <div><span className="font-medium">Failed:</span> {taskDetails.batchTask.failed_articles}</div>
                    {taskDetails.batchTask.started_at && (
                      <div><span className="font-medium">Started:</span> {formatDate(taskDetails.batchTask.started_at)}</div>
                    )}
                    {taskDetails.batchTask.completed_at && (
                      <div><span className="font-medium">Completed:</span> {formatDate(taskDetails.batchTask.completed_at)}</div>
                    )}
                  </div>
                </div>

                {/* Generated Articles */}
                <div>
                  <h3 className="font-medium text-gray-900 mb-3">Generated Articles ({taskDetails.articles.length})</h3>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {taskDetails.articles.length === 0 ? (
                      <div className="text-gray-500 text-sm">No articles generated yet</div>
                    ) : (
                      taskDetails.articles.map((article) => (
                        <div key={article.id} className="border border-gray-200 rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-gray-900 text-sm">{article.name}</h4>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              article.status === 'Completed' ? 'bg-green-100 text-green-800' : 
                              article.status === 'Failed' ? 'bg-red-100 text-red-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {article.status}
                            </span>
                          </div>
                          
                          {article.generated_article && (
                            <div className="text-xs text-gray-500 mb-2">
                              Article generated • {new Date(article.updated_at).toLocaleString()}
                            </div>
                          )}
                          
                          {article.status === 'Completed' && article.generated_article && (
                            <button
                              onClick={() => {
                                // Navigate to article view or open in new tab
                                window.open(`/tasks/${article.id}`, '_blank');
                              }}
                              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                            >
                              View Article →
                            </button>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                </div>

                {/* Error Message */}
                {taskDetails.batchTask.error_message && (
                  <div>
                    <h3 className="font-medium text-gray-900 mb-3">Error Details</h3>
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <p className="text-red-800 text-sm">{taskDetails.batchTask.error_message}</p>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center text-gray-500 py-12">
                Failed to load task details
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BatchTaskManager;
