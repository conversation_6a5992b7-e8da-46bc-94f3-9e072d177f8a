import React from 'react';
import { 
  XMarkIcon, 
  LinkIcon, 
  DocumentTextIcon,
  GlobeAltIcon,
  ClockIcon,
  EyeIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/react/24/outline';

const ResourceDetailModal = ({ resource, isOpen, onClose }) => {
  if (!isOpen || !resource) return null;

  const handleOpenOriginal = () => {
    const url = resource.resource_url || resource.url;
    if (url) {
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  const hasUrl = () => {
    return resource.resource_url || resource.url || (resource.resource_type === 'reddit' && resource.resource_content?.includes('http'));
  };

  const getResourceTypeIcon = (type) => {
    switch (type) {
      case 'url':
        return <LinkIcon className="h-5 w-5" />;
      case 'reddit':
        return <GlobeAltIcon className="h-5 w-5" />;
      default:
        return <DocumentTextIcon className="h-5 w-5" />;
    }
  };

  const getResourceTypeLabel = (type) => {
    switch (type) {
      case 'url':
        return 'URL Resource';
      case 'reddit':
        return 'Reddit Post';
      case 'manual':
        return 'Manual Entry';
      case 'document':
        return 'Document';
      default:
        return type;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
              {getResourceTypeIcon(resource.resource_type)}
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 line-clamp-1">
                {resource.resource_name}
              </h2>
              <p className="text-sm text-gray-500">
                {getResourceTypeLabel(resource.resource_type)}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Info */}
            <div className="lg:col-span-2 space-y-6">
              {/* Description */}
              {resource.description && (
                <div>
                  <h3 className="text-sm font-medium text-gray-900 mb-2">Description</h3>
                  <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">
                    {resource.description}
                  </p>
                </div>
              )}

              {/* Content */}
              {resource.resource_content && (
                <div>
                  <h3 className="text-sm font-medium text-gray-900 mb-2">Content</h3>
                  <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
                    <pre className="whitespace-pre-wrap text-sm text-gray-700 font-normal">
                      {resource.resource_content}
                    </pre>
                  </div>
                </div>
              )}

              {/* URL */}
              {(resource.resource_url || resource.url) && (
                <div>
                  <h3 className="text-sm font-medium text-gray-900 mb-2">
                    {resource.resource_type === 'reddit' ? 'Reddit Post URL' : 'Original URL'}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 bg-gray-50 p-3 rounded-lg">
                      <p className="text-sm text-gray-700 break-all">
                        {resource.resource_url || resource.url}
                      </p>
                    </div>
                    <button
                      onClick={handleOpenOriginal}
                      className="flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      title={resource.resource_type === 'reddit' ? 'Open Reddit Post' : 'Open original URL'}
                    >
                      <ArrowTopRightOnSquareIcon className="h-4 w-4" />
                      <span className="text-sm">
                        {resource.resource_type === 'reddit' ? 'View Post' : 'Open'}
                      </span>
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar Info */}
            <div className="space-y-6">
              {/* Meta Info */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Resource Details</h3>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Type:</span>
                    <span className="font-medium">{getResourceTypeLabel(resource.resource_type)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Usage Count:</span>
                    <span className="font-medium flex items-center">
                      <EyeIcon className="h-3 w-3 mr-1" />
                      {resource.usage_count || 0}
                    </span>
                  </div>
                  {resource.is_favorite && (
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Status:</span>
                      <span className="text-yellow-600 font-medium">★ Favorite</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Tags */}
              {resource.tags && resource.tags.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-900 mb-2">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {resource.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Timestamps */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Timeline</h3>
                <div className="space-y-2 text-xs text-gray-600">
                  <div className="flex items-center">
                    <ClockIcon className="h-3 w-3 mr-1" />
                    <span>Created: {formatDate(resource.created_at)}</span>
                  </div>
                  {resource.updated_at && (
                    <div className="flex items-center">
                      <ClockIcon className="h-3 w-3 mr-1" />
                      <span>Updated: {formatDate(resource.updated_at)}</span>
                    </div>
                  )}
                  {resource.last_used_at && (
                    <div className="flex items-center">
                      <EyeIcon className="h-3 w-3 mr-1" />
                      <span>Last used: {formatDate(resource.last_used_at)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end items-center space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          {(resource.resource_url || resource.url) && (
            <button
              onClick={handleOpenOriginal}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ArrowTopRightOnSquareIcon className="h-4 w-4" />
              <span>
                {resource.resource_type === 'reddit' ? 'View Reddit Post' : 'Open Original'}
              </span>
            </button>
          )}
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ResourceDetailModal;