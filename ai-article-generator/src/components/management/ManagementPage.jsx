import React, { useState, useEffect } from 'react';
import {
  LinkIcon,
  UserIcon,
  DocumentTextIcon, 
  BuildingStorefrontIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  StarIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';
import { useAuth } from '../../contexts/AuthContext';
import { API_CONFIG, presetService } from '../../config/api';
import ResourceForm from './ResourceForm';
import AuthorForm from './AuthorForm';
import ProductForm from './ProductForm';
import ResourceDetailModal from './ResourceDetailModal';

const ManagementPage = ({ type }) => {
  const { authenticatedFetch } = useAuth();
  const [loading, setLoading] = useState(false);
  const [items, setItems] = useState([]);
  const [resources, setResources] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [formData, setFormData] = useState({});
  const [showResourceDetail, setShowResourceDetail] = useState(false);
  const [selectedResource, setSelectedResource] = useState(null);

  const getConfig = () => {
    switch (type) {
      case 'resources':
        return {
          title: 'Resources Management',
          subtitle: 'Manage your research sources and content references',
          icon: LinkIcon,
          color: 'blue',
          addText: 'Add Resource',
          emptyText: 'No resources found',
          emptySubtext: 'Add your first research resource to get started'
        };
      case 'authors':
        return {
          title: 'Authors Management',
          subtitle: 'Manage your author profiles and expertise areas',
          icon: UserIcon,
          color: 'green',
          addText: 'Add Author',
          emptyText: 'No authors found',
          emptySubtext: 'Add your first author profile to get started'
        };
      case 'products':
        return {
          title: 'Products Management',
          subtitle: 'Manage your product information and details',
          icon: BuildingStorefrontIcon,
          color: 'purple',
          addText: 'Add Product',
          emptyText: 'No products found',
          emptySubtext: 'Add your first product to get started'
        };
      default:
        return {};
    }
  };

  const config = getConfig();

  useEffect(() => {
    loadData();
  }, [type]);

  const loadData = async () => {
    setLoading(true);
    try {
      if (type === 'resources') {
        const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.RESOURCES.LIST);
        if (response.ok) {
          const data = await response.json();
          setResources(data.resources || []);
        }
      } else {
        const presets = await presetService.getPresets(type === 'authors' ? 'author' : 'product');
        setItems(presets);
      }
    } catch (error) {
      console.error(`Failed to load ${type}:`, error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (data) => {
    try {
      if (type === 'resources') {
        const url = editingItem 
          ? API_CONFIG.ENDPOINTS.RESOURCES.UPDATE(editingItem.id)
          : API_CONFIG.ENDPOINTS.RESOURCES.CREATE;
        
        const response = await authenticatedFetch(url, {
          method: editingItem ? 'PUT' : 'POST',
          body: JSON.stringify(data)
        });

        if (response.ok) {
          await loadData();
          setShowForm(false);
          setEditingItem(null);
          setFormData({});
        }
      } else {
        const presetType = type === 'authors' ? 'author' : 'product';
        if (editingItem) {
          await presetService.updatePreset(editingItem.id, data.presetName, data.presetData);
        } else {
          await presetService.createPreset(presetType, data.presetName, data.presetData);
        }
        await loadData();
        setShowForm(false);
        setEditingItem(null);
        setFormData({});
      }
    } catch (error) {
      console.error(`Failed to save ${type}:`, error);
      alert(`Failed to save ${type.slice(0, -1)}. Please try again.`);
    }
  };

  const handleDelete = async (item) => {
    if (!confirm(`Are you sure you want to delete this ${type.slice(0, -1)}?`)) return;

    try {
      if (type === 'resources') {
        const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.RESOURCES.DELETE(item.id), {
          method: 'DELETE'
        });
        if (response.ok) {
          await loadData();
        }
      } else {
        await presetService.deletePreset(item.id);
        await loadData();
      }
    } catch (error) {
      console.error(`Failed to delete ${type}:`, error);
      alert(`Failed to delete ${type.slice(0, -1)}. Please try again.`);
    }
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    if (type === 'resources') {
      setFormData({
        resource_name: item.resource_name,
        resource_type: item.resource_type,
        resource_url: item.resource_url || '',
        resource_content: item.resource_content || '',
        description: item.description || '',
        tags: item.tags || ''
      });
    } else {
      setFormData({
        presetName: item.presetName,
        presetData: item.presetData
      });
    }
    setShowForm(true);
  };

  const handleAdd = () => {
    setEditingItem(null);
    if (type === 'resources') {
      setFormData({
        resource_name: '',
        resource_type: 'url',
        resource_url: '',
        resource_content: '',
        description: '',
        tags: ''
      });
    } else if (type === 'authors') {
      setFormData({
        presetName: '',
        presetData: {
          authorName: '',
          authorBio: '',
          targetAudience: '',
          writingStyle: '',
          expertise: []
        }
      });
    } else {
      setFormData({
        presetName: '',
        presetData: {
          name: '',
          description: '',
          link: '',
          features: []
        }
      });
    }
    setShowForm(true);
  };

  const handleView = (item) => {
    setSelectedResource(item);
    setShowResourceDetail(true);
  };

  const renderItems = () => {
    const displayItems = type === 'resources' ? resources : items;
    
    return displayItems.map((item, index) => (
      <div key={item.id || index} className={`bg-white p-6 rounded-lg border border-gray-200 hover:border-${config.color}-300 transition-all duration-200 hover:shadow-md`}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <config.icon className={`h-5 w-5 text-${config.color}-600`} />
              <h3 className="text-lg font-semibold text-gray-900">
                {type === 'resources' ? item.resource_name : item.presetName}
              </h3>
              {item.is_favorite && (
                <StarSolidIcon className="h-4 w-4 text-yellow-500" />
              )}
            </div>
            
            {type === 'resources' && (
              <div className="text-sm text-gray-600 mb-2">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${config.color}-100 text-${config.color}-800`}>
                  {item.resource_type}
                </span>
                {item.usage_count > 0 && (
                  <span className="ml-2 text-gray-500">Used {item.usage_count} times</span>
                )}
              </div>
            )}
            
            <p className="text-gray-600 mb-3">
              {type === 'resources' 
                ? item.description 
                : type === 'authors' 
                  ? item.presetData?.authorBio || 'No bio available'
                  : item.presetData?.description || 'No description available'
              }
            </p>
            
            {type === 'authors' && item.presetData?.authorName && (
              <div className="text-sm text-gray-500 mb-2">
                Author: {item.presetData.authorName}
              </div>
            )}
            
            {type === 'products' && item.presetData?.name && (
              <div className="text-sm text-gray-500 mb-2">
                Product: {item.presetData.name}
              </div>
            )}
            
            {item.tags && (
              <div className="flex flex-wrap gap-1 mb-2">
                {(() => {
                  try {
                    const tags = typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags;
                    return Array.isArray(tags) ? tags : [];
                  } catch (e) {
                    console.error('Failed to parse tags:', item.tags, e);
                    return [];
                  }
                })().map((tag, tagIndex) => (
                  <span key={tagIndex} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </div>
          
          <div className="flex space-x-2">
            {type === 'resources' && (
              <button
                onClick={() => handleView(item)}
                className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
                title="View resource details"
              >
                <EyeIcon className="h-4 w-4" />
              </button>
            )}
            <button
              onClick={() => handleEdit(item)}
              className={`p-2 text-${config.color}-600 hover:bg-${config.color}-50 rounded-lg transition-colors`}
            >
              <PencilIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => handleDelete(item)}
              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    ));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading {type}...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className={`w-12 h-12 bg-${config.color}-600 rounded-xl flex items-center justify-center`}>
                <config.icon className="h-7 w-7 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{config.title}</h1>
                <p className="text-gray-600">{config.subtitle}</p>
              </div>
            </div>
            <button
              onClick={handleAdd}
              className={`flex items-center space-x-2 px-4 py-2 bg-${config.color}-600 text-white rounded-lg hover:bg-${config.color}-700 transition-colors`}
            >
              <PlusIcon className="h-5 w-5" />
              <span>{config.addText}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {(type === 'resources' ? resources : items).length === 0 ? (
          <div className="text-center py-12">
            <config.icon className={`mx-auto h-12 w-12 text-gray-400`} />
            <h3 className="mt-2 text-sm font-medium text-gray-900">{config.emptyText}</h3>
            <p className="mt-1 text-sm text-gray-500">{config.emptySubtext}</p>
            <div className="mt-6">
              <button
                onClick={handleAdd}
                className={`inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-${config.color}-600 hover:bg-${config.color}-700`}
              >
                <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
                {config.addText}
              </button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {renderItems()}
          </div>
        )}
      </div>

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/5 xl:w-1/2 shadow-lg rounded-md bg-white max-h-[90vh] overflow-y-auto">
            <div className="mt-3">
              {type === 'resources' ? (
                <ResourceForm
                  formData={formData}
                  setFormData={setFormData}
                  editingItem={editingItem}
                  onCancel={() => {
                    setShowForm(false);
                    setEditingItem(null);
                    setFormData({});
                  }}
                  onSave={handleSave}
                />
              ) : type === 'authors' ? (
                <AuthorForm
                  formData={formData}
                  setFormData={setFormData}
                  editingItem={editingItem}
                  onCancel={() => {
                    setShowForm(false);
                    setEditingItem(null);
                    setFormData({});
                  }}
                  onSave={handleSave}
                />
              ) : (
                <ProductForm
                  formData={formData}
                  setFormData={setFormData}
                  editingItem={editingItem}
                  onCancel={() => {
                    setShowForm(false);
                    setEditingItem(null);
                    setFormData({});
                  }}
                  onSave={handleSave}
                />
              )}
            </div>
          </div>
        </div>
      )}

      {/* Resource Detail Modal */}
      <ResourceDetailModal
        resource={selectedResource}
        isOpen={showResourceDetail}
        onClose={() => {
          setShowResourceDetail(false);
          setSelectedResource(null);
        }}
      />
    </div>
  );
};

export default ManagementPage;