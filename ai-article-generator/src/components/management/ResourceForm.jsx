import React, { useState } from 'react';
import { 
  PlusIcon, 
  XMarkIcon, 
  LinkIcon, 
  DocumentTextIcon, 
  GlobeAltIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { apiCall, API_CONFIG } from '../../config/api';
import RedditInsightAssistant from '../RedditInsightAssistant';

const ResourceForm = ({ 
  formData, 
  setFormData, 
  onSubmit, 
  onCancel, 
  editingItem,
  onSave 
}) => {
  const [activeTab, setActiveTab] = useState('url');
  const [isExtracting, setIsExtracting] = useState(false);
  const [urlInput, setUrlInput] = useState('');
  const [textInput, setTextInput] = useState('');
  const [redditSources, setRedditSources] = useState([]);

  // Extract URL content
  const extractUrlContent = async () => {
    if (!urlInput.trim()) return;

    setIsExtracting(true);
    try {
      const extractedData = await apiCall(API_CONFIG.ENDPOINTS.SOURCES_EXTRACT, {
        method: 'POST',
        body: JSON.stringify({ url: urlInput.trim() }),
      });

      // Create resource with extracted data
      const resourceData = {
        resource_name: extractedData.title || 'Extracted Content',
        resource_type: 'url',
        resource_url: urlInput.trim(),
        resource_content: extractedData.content || '',
        description: extractedData.description || '',
        tags: []
      };

      console.log('🎉 URL内容提取成功:', extractedData.title);
      
      if (onSave) {
        await onSave(resourceData);
      }
      
      setUrlInput('');
    } catch (error) {
      console.error('Error extracting URL content:', error);
      alert('Failed to extract content from URL. Please check the URL and try again.');
    } finally {
      setIsExtracting(false);
    }
  };

  // Add text block
  const addTextBlock = async () => {
    if (!textInput.trim()) return;

    const wordCount = textInput.trim().split(/\s+/).length;
    const resourceData = {
      resource_name: 'Custom Text Block',
      resource_type: 'manual',
      resource_url: null,
      resource_content: textInput.trim(),
      description: `Text block with ${wordCount} words`,
      tags: []
    };

    if (onSave) {
      await onSave(resourceData);
    }
    
    setTextInput('');
  };

  // Handle Reddit post use
  const handleRedditPostUse = async (postData) => {
    try {
      const resourceData = {
        resource_name: postData.source.title || 'Reddit Post',
        resource_type: 'reddit',
        resource_url: postData.source.url,
        resource_content: postData.source.content || '',
        description: `Reddit post with ${postData.source.upvotes || 0} upvotes`,
        tags: ['reddit']
      };

      if (onSave) {
        await onSave(resourceData);
      }

      // Update local Reddit sources state
      const currentSources = redditSources || [];
      const existingIndex = currentSources.findIndex(source => 
        source.url === postData.source.url
      );
      
      if (existingIndex !== -1) {
        // Remove existing
        setRedditSources(currentSources.filter((_, index) => index !== existingIndex));
      } else {
        // Add new (limit to 3)
        if (currentSources.length < 3) {
          setRedditSources([...currentSources, postData.source]);
        } else {
          alert('You can only select up to 3 Reddit sources');
        }
      }
    } catch (error) {
      console.error('Failed to save Reddit resource:', error);
      alert('Failed to save Reddit resource. Please try again.');
    }
  };

  const tabs = [
    { id: 'url', name: 'URL Resource', icon: LinkIcon },
    { id: 'text', name: 'Text Block', icon: DocumentTextIcon },
    { id: 'reddit', name: 'Reddit Posts', icon: GlobeAltIcon }
  ];

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Add Research Resources
        </h3>
        <p className="text-sm text-gray-600">
          Add various types of resources to enrich your article content
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-4">
        {activeTab === 'url' && (
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <LinkIcon className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900">URL Content Extraction</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    Enter a URL to automatically extract and analyze its content
                  </p>
                </div>
              </div>
            </div>

            <div className="flex space-x-3">
              <input
                type="url"
                value={urlInput}
                onChange={(e) => setUrlInput(e.target.value)}
                placeholder="https://example.com/article"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isExtracting}
              />
              <button
                onClick={extractUrlContent}
                disabled={!urlInput.trim() || isExtracting}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {isExtracting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Extracting...</span>
                  </>
                ) : (
                  <>
                    <PlusIcon className="h-4 w-4" />
                    <span>Extract & Add</span>
                  </>
                )}
              </button>
            </div>

            <div className="text-xs text-gray-500">
              Supported: Articles, blog posts, research papers, documentation, and more
            </div>
          </div>
        )}

        {activeTab === 'text' && (
          <div className="space-y-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <DocumentTextIcon className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-green-900">Custom Text Block</h4>
                  <p className="text-sm text-green-700 mt-1">
                    Add your own text content, notes, or information
                  </p>
                </div>
              </div>
            </div>

            <div>
              <textarea
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                placeholder="Paste or type your text content here..."
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
              <div className="flex justify-between items-center mt-2">
                <div className="text-xs text-gray-500">
                  Word count: {textInput.trim() ? textInput.trim().split(/\s+/).length : 0}
                </div>
                <button
                  onClick={addTextBlock}
                  disabled={!textInput.trim()}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  <PlusIcon className="h-4 w-4" />
                  <span>Add Text Block</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'reddit' && (
          <div className="space-y-4">
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <GlobeAltIcon className="h-5 w-5 text-orange-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-orange-900">Reddit Community Insights</h4>
                  <p className="text-sm text-orange-700 mt-1">
                    Search and select relevant Reddit discussions (max 3 posts)
                  </p>
                </div>
              </div>
            </div>

            {redditSources.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h5 className="font-medium text-gray-900 mb-2">
                  Selected Reddit Sources ({redditSources.length}/3)
                </h5>
                <div className="space-y-2">
                  {redditSources.map((source, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-white rounded border">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm truncate">{source.title}</div>
                        <div className="text-xs text-gray-500">
                          {source.upvotes} upvotes • {source.numComments} comments
                        </div>
                      </div>
                      <button
                        onClick={() => {
                          setRedditSources(redditSources.filter((_, i) => i !== index));
                        }}
                        className="ml-2 text-red-600 hover:text-red-800"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <RedditInsightAssistant
              selectedTopics={[]} // Pass relevant topics if available
              onRedditPostUse={handleRedditPostUse}
              selectedSources={redditSources}
            />

            {redditSources.length >= 3 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <ExclamationTriangleIcon className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm text-yellow-800">
                    Maximum of 3 Reddit sources reached. Remove one to add another.
                  </span>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* AI Integration Notice */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">🤖 AI Content Integration</h4>
        <p className="text-sm text-blue-700">
          The AI will automatically analyze and integrate your resources into the article, 
          ensuring relevance and quality. Low-quality or irrelevant resources may be filtered out.
        </p>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
        <button
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default ResourceForm;