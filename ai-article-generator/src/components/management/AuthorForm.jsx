import React, { useState, useEffect } from 'react';
import { 
  UserIcon,
  InformationCircleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const AuthorForm = ({ 
  formData, 
  setFormData, 
  editingItem,
  onCancel,
  onSave 
}) => {
  const [localFormData, setLocalFormData] = useState({
    presetName: '',
    presetData: {
      authorName: '',
      authorBio: '',
      targetAudience: '',
      articleGoal: ''
    }
  });

  useEffect(() => {
    if (editingItem) {
      setLocalFormData({
        presetName: editingItem.presetName || '',
        presetData: {
          authorName: editingItem.presetData?.authorName || '',
          authorBio: editingItem.presetData?.authorBio || '',
          targetAudience: editingItem.presetData?.targetAudience || '',
          articleGoal: editingItem.presetData?.articleGoal || ''
        }
      });
    }
  }, [editingItem]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!localFormData.presetName.trim()) {
      alert('Profile name is required');
      return;
    }

    try {
      await onSave(localFormData);
      onCancel(); // Close form after successful save
    } catch (error) {
      console.error('Failed to save author profile:', error);
      alert('Failed to save author profile. Please try again.');
    }
  };

  const updateField = (field, value) => {
    if (field === 'presetName') {
      setLocalFormData(prev => ({
        ...prev,
        presetName: value
      }));
    } else {
      setLocalFormData(prev => ({
        ...prev,
        presetData: {
          ...prev.presetData,
          [field]: value
        }
      }));
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {editingItem ? 'Edit Author Profile' : 'Create Author Profile'}
        </h3>
        <p className="text-sm text-gray-600">
          Create an E-E-A-T (Experience, Expertise, Authoritativeness, Trustworthiness) profile
        </p>
      </div>

      {/* E-E-A-T Explanation */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <InformationCircleIcon className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-blue-900 mb-2">What is E-E-A-T?</h4>
            <div className="text-sm text-blue-700 space-y-1">
              <p><strong>Experience:</strong> First-hand experience with the topic</p>
              <p><strong>Expertise:</strong> Knowledge and skill in the subject area</p>
              <p><strong>Authoritativeness:</strong> Recognition as a credible source</p>
              <p><strong>Trustworthiness:</strong> Honesty and transparency in content</p>
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Profile Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Profile Name *
          </label>
          <input
            type="text"
            value={localFormData.presetName}
            onChange={(e) => updateField('presetName', e.target.value)}
            placeholder="e.g., Tech Expert Profile, Marketing Specialist"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
          <p className="text-xs text-gray-500 mt-1">
            A descriptive name for this author profile
          </p>
        </div>

        {/* Author Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Author Name
          </label>
          <input
            type="text"
            value={localFormData.presetData.authorName}
            onChange={(e) => updateField('authorName', e.target.value)}
            placeholder="John Smith"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <p className="text-xs text-gray-500 mt-1">
            The name that will appear as the article author
          </p>
        </div>

        {/* Author Bio */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Author Bio & Expertise
          </label>
          <textarea
            value={localFormData.presetData.authorBio}
            onChange={(e) => updateField('authorBio', e.target.value)}
            rows={4}
            placeholder="Describe the author's background, experience, credentials, and expertise in the subject area..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <p className="text-xs text-gray-500 mt-1">
            Include qualifications, years of experience, relevant achievements, and areas of expertise
          </p>
        </div>

        {/* Target Audience */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Target Audience
          </label>
          <input
            type="text"
            value={localFormData.presetData.targetAudience}
            onChange={(e) => updateField('targetAudience', e.target.value)}
            placeholder="e.g., Small business owners, Tech enthusiasts, Marketing professionals"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <p className="text-xs text-gray-500 mt-1">
            Who is the intended reader of articles written with this profile?
          </p>
        </div>

        {/* Article Goal */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Article Goal & Purpose
          </label>
          <textarea
            value={localFormData.presetData.articleGoal}
            onChange={(e) => updateField('articleGoal', e.target.value)}
            rows={3}
            placeholder="What should the article achieve? e.g., Educate readers about best practices, Help solve specific problems, Provide actionable insights..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <p className="text-xs text-gray-500 mt-1">
            Define the main objective and value the article should provide to readers
          </p>
        </div>

        {/* SEO & Trust Notice */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-medium text-green-900 mb-1">SEO & Trust Benefits</h4>
              <p className="text-sm text-green-700">
                A well-defined E-E-A-T profile helps establish credibility with both readers and search engines, 
                potentially improving your content's ranking and trustworthiness.
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
          >
            <UserIcon className="h-4 w-4" />
            <span>{editingItem ? 'Update Profile' : 'Create Profile'}</span>
          </button>
        </div>
      </form>
    </div>
  );
};

export default AuthorForm;