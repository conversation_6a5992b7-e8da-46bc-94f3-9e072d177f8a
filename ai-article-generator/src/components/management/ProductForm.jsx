import React, { useState, useEffect } from 'react';
import { 
  BuildingStorefrontIcon,
  PlusIcon,
  TrashIcon,
  LinkIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

const ProductForm = ({ 
  formData, 
  setFormData, 
  editingItem,
  onCancel,
  onSave 
}) => {
  const [localFormData, setLocalFormData] = useState({
    presetName: '',
    presetData: {
      name: '',
      link: '',
      description: '',
      features: []
    }
  });

  const [featureInput, setFeatureInput] = useState('');

  useEffect(() => {
    if (editingItem) {
      setLocalFormData({
        presetName: editingItem.presetName || '',
        presetData: {
          name: editingItem.presetData?.name || '',
          link: editingItem.presetData?.link || '',
          description: editingItem.presetData?.description || '',
          features: editingItem.presetData?.features || []
        }
      });
    }
  }, [editingItem]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!localFormData.presetName.trim()) {
      alert('Profile name is required');
      return;
    }

    if (!localFormData.presetData.name.trim()) {
      alert('Product name is required');
      return;
    }

    try {
      await onSave(localFormData);
      onCancel(); // Close form after successful save
    } catch (error) {
      console.error('Failed to save product:', error);
      alert('Failed to save product. Please try again.');
    }
  };

  const updateField = (field, value) => {
    if (field === 'presetName') {
      setLocalFormData(prev => ({
        ...prev,
        presetName: value
      }));
    } else {
      setLocalFormData(prev => ({
        ...prev,
        presetData: {
          ...prev.presetData,
          [field]: value
        }
      }));
    }
  };

  const addFeature = () => {
    if (featureInput.trim() && localFormData.presetData.features.length < 6) {
      setLocalFormData(prev => ({
        ...prev,
        presetData: {
          ...prev.presetData,
          features: [...prev.presetData.features, featureInput.trim()]
        }
      }));
      setFeatureInput('');
    }
  };

  const removeFeature = (index) => {
    setLocalFormData(prev => ({
      ...prev,
      presetData: {
        ...prev.presetData,
        features: prev.presetData.features.filter((_, i) => i !== index)
      }
    }));
  };

  const updateFeature = (index, value) => {
    setLocalFormData(prev => ({
      ...prev,
      presetData: {
        ...prev.presetData,
        features: prev.presetData.features.map((feature, i) => 
          i === index ? value : feature
        )
      }
    }));
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addFeature();
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {editingItem ? 'Edit Product Information' : 'Add Product Information'}
        </h3>
        <p className="text-sm text-gray-600">
          Add product or service details for natural integration into articles
        </p>
      </div>

      {/* Product Integration Explanation */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <InformationCircleIcon className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-purple-900 mb-1">Smart Product Integration</h4>
            <p className="text-sm text-purple-700">
              The AI will naturally weave your product information into the article where relevant and appropriate, 
              ensuring it adds value rather than appearing as forced promotion.
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Profile Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Profile Name *
          </label>
          <input
            type="text"
            value={localFormData.presetName}
            onChange={(e) => updateField('presetName', e.target.value)}
            placeholder="e.g., Main SaaS Product, Marketing Tool, Course Offering"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            required
          />
          <p className="text-xs text-gray-500 mt-1">
            A descriptive name for this product profile
          </p>
        </div>

        {/* Product Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Product/Service Name *
          </label>
          <input
            type="text"
            value={localFormData.presetData.name}
            onChange={(e) => updateField('name', e.target.value)}
            placeholder="e.g., WriteFlow Pro, Marketing Analytics Suite"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            required
          />
          <p className="text-xs text-gray-500 mt-1">
            The official name of your product or service
          </p>
        </div>

        {/* Product Link */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Product Link (Optional)
          </label>
          <div className="relative">
            <input
              type="url"
              value={localFormData.presetData.link}
              onChange={(e) => updateField('link', e.target.value)}
              placeholder="https://yourproduct.com"
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
            <LinkIcon className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            URL where readers can learn more or purchase your product
          </p>
        </div>

        {/* Product Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Product Description
          </label>
          <textarea
            value={localFormData.presetData.description}
            onChange={(e) => updateField('description', e.target.value)}
            rows={4}
            placeholder="Describe what your product does, who it's for, and what problems it solves..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          />
          <p className="text-xs text-gray-500 mt-1">
            A clear explanation of your product's purpose and benefits
          </p>
        </div>

        {/* Product Features */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <label className="block text-sm font-medium text-gray-700">
              Key Features/Benefits
            </label>
            <span className="text-xs text-gray-500">
              {localFormData.presetData.features.length}/6 features
            </span>
          </div>

          {/* Add Feature Input */}
          <div className="flex space-x-2 mb-3">
            <input
              type="text"
              value={featureInput}
              onChange={(e) => setFeatureInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Enter a key feature or benefit"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              disabled={localFormData.presetData.features.length >= 6}
            />
            <button
              type="button"
              onClick={addFeature}
              disabled={!featureInput.trim() || localFormData.presetData.features.length >= 6}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <PlusIcon className="h-4 w-4" />
              <span>Add</span>
            </button>
          </div>

          {/* Features List */}
          {localFormData.presetData.features.length > 0 && (
            <div className="space-y-2">
              {localFormData.presetData.features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-600 font-medium">
                    {index + 1}.
                  </span>
                  <input
                    type="text"
                    value={feature}
                    onChange={(e) => updateFeature(index, e.target.value)}
                    className="flex-1 px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-transparent text-sm"
                  />
                  <button
                    type="button"
                    onClick={() => removeFeature(index)}
                    className="text-red-600 hover:text-red-800 p-1"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          )}

          {localFormData.presetData.features.length >= 6 && (
            <p className="text-xs text-amber-600 mt-2">
              Maximum of 6 features reached. Remove a feature to add another.
            </p>
          )}

          <p className="text-xs text-gray-500 mt-2">
            List the most compelling features, benefits, or unique selling points
          </p>
        </div>

        {/* Preview Section */}
        {(localFormData.presetData.name || localFormData.presetData.description) && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Preview</h4>
            <div className="text-sm text-gray-700 space-y-1">
              {localFormData.presetData.name && (
                <div><strong>Product:</strong> {localFormData.presetData.name}</div>
              )}
              {localFormData.presetData.description && (
                <div><strong>Description:</strong> {localFormData.presetData.description}</div>
              )}
              {localFormData.presetData.features.length > 0 && (
                <div>
                  <strong>Features:</strong>
                  <ul className="list-disc list-inside ml-2 mt-1">
                    {localFormData.presetData.features.map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
          >
            <BuildingStorefrontIcon className="h-4 w-4" />
            <span>{editingItem ? 'Update Product' : 'Save Product'}</span>
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProductForm;