import React, { useState, useEffect } from 'react';
import {
  Cog6ToothIcon,
  DocumentTextIcon,
  UserIcon,
  LinkIcon,
  ChevronDownIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';
import { useAuth } from '../../contexts/AuthContext';
import { API_CONFIG, apiCall, presetService } from '../../config/api';

const ManagementHeader = ({
  onResourceSelect,
  onAuthorSelect,
  onProductSelect,
  selectedResources = [],
  selectedAuthor = null,
  selectedProduct = null,
  className = ""
}) => {
  const { authenticatedFetch } = useAuth();
  const [showManagement, setShowManagement] = useState(false);
  const [activeTab, setActiveTab] = useState('resources');
  const [loading, setLoading] = useState(false);

  // Data states
  const [resources, setResources] = useState([]);
  const [authors, setAuthors] = useState([]);
  const [products, setProducts] = useState([]);

  // Form states
  const [showResourceForm, setShowResourceForm] = useState(false);
  const [showAuthorForm, setShowAuthorForm] = useState(false);
  const [showProductForm, setShowProductForm] = useState(false);
  const [editingItem, setEditingItem] = useState(null);

  // Form data states
  const [resourceForm, setResourceForm] = useState({
    resource_name: '',
    resource_type: 'url',
    resource_url: '',
    resource_content: '',
    description: '',
    tags: []
  });

  const [authorForm, setAuthorForm] = useState({
    presetName: '',
    presetData: {
      authorName: '',
      authorBio: '',
      targetAudience: '',
      articleGoal: ''
    }
  });

  const [productForm, setProductForm] = useState({
    presetName: '',
    presetData: {
      name: '',
      link: '',
      description: '',
      features: []
    }
  });

  useEffect(() => {
    if (showManagement) {
      loadData();
    }
  }, [showManagement, activeTab]);

  const loadData = async () => {
    setLoading(true);
    try {
      if (activeTab === 'resources') {
        const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.RESOURCES.LIST);
        if (response.ok) {
          const data = await response.json();
          setResources(data.resources || []);
        }
      } else if (activeTab === 'authors') {
        const authorsData = await presetService.getPresets('author');
        setAuthors(authorsData);
      } else if (activeTab === 'products') {
        const productsData = await presetService.getPresets('product');
        setProducts(productsData);
      }
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleResourceSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingItem) {
        const response = await authenticatedFetch(
          API_CONFIG.ENDPOINTS.RESOURCES.UPDATE(editingItem.id),
          {
            method: 'PUT',
            body: JSON.stringify(resourceForm)
          }
        );
        if (response.ok) {
          await loadData();
          resetResourceForm();
        }
      } else {
        const response = await authenticatedFetch(
          API_CONFIG.ENDPOINTS.RESOURCES.CREATE,
          {
            method: 'POST',
            body: JSON.stringify(resourceForm)
          }
        );
        if (response.ok) {
          await loadData();
          resetResourceForm();
        }
      }
    } catch (error) {
      console.error('Failed to save resource:', error);
    }
  };

  const handleAuthorSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingItem) {
        await presetService.updatePreset(editingItem.id, authorForm.presetName, authorForm.presetData);
      } else {
        await presetService.createPreset('author', authorForm.presetName, authorForm.presetData);
      }
      await loadData();
      resetAuthorForm();
    } catch (error) {
      console.error('Failed to save author:', error);
    }
  };

  const handleProductSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingItem) {
        await presetService.updatePreset(editingItem.id, productForm.presetName, productForm.presetData);
      } else {
        await presetService.createPreset('product', productForm.presetName, productForm.presetData);
      }
      await loadData();
      resetProductForm();
    } catch (error) {
      console.error('Failed to save product:', error);
    }
  };

  const handleDelete = async (type, id) => {
    if (!confirm('Are you sure you want to delete this item?')) return;
    
    try {
      if (type === 'resource') {
        await authenticatedFetch(API_CONFIG.ENDPOINTS.RESOURCES.DELETE(id), { method: 'DELETE' });
      } else {
        await presetService.deletePreset(id);
      }
      await loadData();
    } catch (error) {
      console.error('Failed to delete item:', error);
    }
  };

  const resetResourceForm = () => {
    setResourceForm({
      resource_name: '',
      resource_type: 'url',
      resource_url: '',
      resource_content: '',
      description: '',
      tags: []
    });
    setShowResourceForm(false);
    setEditingItem(null);
  };

  const resetAuthorForm = () => {
    setAuthorForm({
      presetName: '',
      presetData: {
        authorName: '',
        authorBio: '',
        targetAudience: '',
        articleGoal: ''
      }
    });
    setShowAuthorForm(false);
    setEditingItem(null);
  };

  const resetProductForm = () => {
    setProductForm({
      presetName: '',
      presetData: {
        name: '',
        link: '',
        description: '',
        features: []
      }
    });
    setShowProductForm(false);
    setEditingItem(null);
  };

  const editResource = (resource) => {
    setResourceForm({
      resource_name: resource.resource_name,
      resource_type: resource.resource_type,
      resource_url: resource.resource_url || '',
      resource_content: resource.resource_content || '',
      description: resource.description || '',
      tags: resource.tags || []
    });
    setEditingItem(resource);
    setShowResourceForm(true);
  };

  const editAuthor = (author) => {
    setAuthorForm({
      presetName: author.presetName,
      presetData: author.presetData
    });
    setEditingItem(author);
    setShowAuthorForm(true);
  };

  const editProduct = (product) => {
    setProductForm({
      presetName: product.presetName,
      presetData: product.presetData
    });
    setEditingItem(product);
    setShowProductForm(true);
  };

  const toggleResourceSelection = (resource) => {
    const isSelected = selectedResources.some(r => r.id === resource.id);
    if (isSelected) {
      onResourceSelect(selectedResources.filter(r => r.id !== resource.id));
    } else {
      onResourceSelect([...selectedResources, resource]);
    }
  };

  const tabs = [
    { id: 'resources', label: 'Resources', icon: LinkIcon },
    { id: 'authors', label: 'Authors', icon: UserIcon },
    { id: 'products', label: 'Products', icon: DocumentTextIcon }
  ];

  return (
    <div className={`relative ${className}`}>
      {/* Management Toggle Button */}
      <button
        onClick={() => setShowManagement(!showManagement)}
        className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors shadow-lg"
      >
        <Cog6ToothIcon className="h-5 w-5" />
        <span>Manage Resources</span>
        <ChevronDownIcon className={`h-4 w-4 transform transition-transform ${showManagement ? 'rotate-180' : ''}`} />
      </button>

      {/* Management Modal */}
      {showManagement && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
            {/* Header */}
            <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-900">Resource Management</h2>
                <button
                  onClick={() => setShowManagement(false)}
                  className="text-gray-500 hover:text-gray-700 text-2xl font-bold"
                >
                  ×
                </button>
              </div>
            </div>

            {/* Tabs */}
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex space-x-1">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                      activeTab === tab.id
                        ? 'bg-indigo-100 text-indigo-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <tab.icon className="h-5 w-5" />
                    <span>{tab.label}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[60vh]">
              {activeTab === 'resources' && (
                <ResourceManagement
                  resources={resources}
                  selectedResources={selectedResources}
                  onToggleSelection={toggleResourceSelection}
                  onEdit={editResource}
                  onDelete={(id) => handleDelete('resource', id)}
                  showForm={showResourceForm}
                  setShowForm={setShowResourceForm}
                  formData={resourceForm}
                  setFormData={setResourceForm}
                  onSubmit={handleResourceSubmit}
                  editingItem={editingItem}
                  onCancel={resetResourceForm}
                  loading={loading}
                />
              )}

              {activeTab === 'authors' && (
                <AuthorManagement
                  authors={authors}
                  selectedAuthor={selectedAuthor}
                  onSelect={onAuthorSelect}
                  onEdit={editAuthor}
                  onDelete={(id) => handleDelete('author', id)}
                  showForm={showAuthorForm}
                  setShowForm={setShowAuthorForm}
                  formData={authorForm}
                  setFormData={setAuthorForm}
                  onSubmit={handleAuthorSubmit}
                  editingItem={editingItem}
                  onCancel={resetAuthorForm}
                  loading={loading}
                />
              )}

              {activeTab === 'products' && (
                <ProductManagement
                  products={products}
                  selectedProduct={selectedProduct}
                  onSelect={onProductSelect}
                  onEdit={editProduct}
                  onDelete={(id) => handleDelete('product', id)}
                  showForm={showProductForm}
                  setShowForm={setShowProductForm}
                  formData={productForm}
                  setFormData={setProductForm}
                  onSubmit={handleProductSubmit}
                  editingItem={editingItem}
                  onCancel={resetProductForm}
                  loading={loading}
                />
              )}
            </div>

            {/* Footer */}
            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  {activeTab === 'resources' && `${selectedResources.length} resources selected`}
                  {activeTab === 'authors' && (selectedAuthor ? `Selected: ${selectedAuthor.presetName}` : 'No author selected')}
                  {activeTab === 'products' && (selectedProduct ? `Selected: ${selectedProduct.presetName}` : 'No product selected')}
                </div>
                <button
                  onClick={() => setShowManagement(false)}
                  className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  Done
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Resource Management Component
const ResourceManagement = ({
  resources,
  selectedResources,
  onToggleSelection,
  onEdit,
  onDelete,
  showForm,
  setShowForm,
  formData,
  setFormData,
  onSubmit,
  editingItem,
  onCancel,
  loading
}) => {
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Resources</h3>
        <button
          onClick={() => setShowForm(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Resource</span>
        </button>
      </div>

      {showForm && (
        <ResourceForm
          formData={formData}
          setFormData={setFormData}
          onSubmit={onSubmit}
          onCancel={onCancel}
          editingItem={editingItem}
        />
      )}

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading resources...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {resources.map((resource) => (
            <ResourceCard
              key={resource.id}
              resource={resource}
              isSelected={selectedResources.some(r => r.id === resource.id)}
              onToggleSelection={() => onToggleSelection(resource)}
              onEdit={() => onEdit(resource)}
              onDelete={() => onDelete(resource.id)}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Author Management Component  
const AuthorManagement = ({
  authors,
  selectedAuthor,
  onSelect,
  onEdit,
  onDelete,
  showForm,
  setShowForm,
  formData,
  setFormData,
  onSubmit,
  editingItem,
  onCancel,
  loading
}) => {
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Author Profiles</h3>
        <button
          onClick={() => setShowForm(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Author</span>
        </button>
      </div>

      {showForm && (
        <AuthorForm
          formData={formData}
          setFormData={setFormData}
          onSubmit={onSubmit}
          onCancel={onCancel}
          editingItem={editingItem}
        />
      )}

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading authors...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {authors.map((author) => (
            <AuthorCard
              key={author.id}
              author={author}
              isSelected={selectedAuthor?.id === author.id}
              onSelect={() => onSelect(author)}
              onEdit={() => onEdit(author)}
              onDelete={() => onDelete(author.id)}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Product Management Component
const ProductManagement = ({
  products,
  selectedProduct,
  onSelect,
  onEdit,
  onDelete,
  showForm,
  setShowForm,
  formData,
  setFormData,
  onSubmit,
  editingItem,
  onCancel,
  loading
}) => {
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Product Information</h3>
        <button
          onClick={() => setShowForm(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Product</span>
        </button>
      </div>

      {showForm && (
        <ProductForm
          formData={formData}
          setFormData={setFormData}
          onSubmit={onSubmit}
          onCancel={onCancel}
          editingItem={editingItem}
        />
      )}

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading products...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {products.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              isSelected={selectedProduct?.id === product.id}
              onSelect={() => onSelect(product)}
              onEdit={() => onEdit(product)}
              onDelete={() => onDelete(product.id)}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Individual Card Components
const ResourceCard = ({ resource, isSelected, onToggleSelection, onEdit, onDelete }) => (
  <div className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
    isSelected ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:border-gray-300'
  }`}>
    <div className="flex items-start justify-between mb-2">
      <h4 className="font-medium text-gray-900 truncate">{resource.resource_name}</h4>
      <div className="flex items-center space-x-1">
        <button onClick={onToggleSelection} className="text-indigo-600 hover:text-indigo-800">
          {isSelected ? <StarSolidIcon className="h-4 w-4" /> : <StarIcon className="h-4 w-4" />}
        </button>
        <button onClick={onEdit} className="text-gray-600 hover:text-gray-800">
          <PencilIcon className="h-4 w-4" />
        </button>
        <button onClick={onDelete} className="text-red-600 hover:text-red-800">
          <TrashIcon className="h-4 w-4" />
        </button>
      </div>
    </div>
    <p className="text-sm text-gray-600 mb-2">{resource.description}</p>
    <div className="flex items-center justify-between text-xs text-gray-500">
      <span className="bg-gray-100 px-2 py-1 rounded">{resource.resource_type}</span>
      <span>Used {resource.usage_count} times</span>
    </div>
  </div>
);

const AuthorCard = ({ author, isSelected, onSelect, onEdit, onDelete }) => (
  <div className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
    isSelected ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:border-gray-300'
  }`}>
    <div className="flex items-start justify-between mb-2">
      <h4 className="font-medium text-gray-900">{author.presetName}</h4>
      <div className="flex items-center space-x-1">
        <button onClick={onSelect} className="text-indigo-600 hover:text-indigo-800">
          {isSelected ? <StarSolidIcon className="h-4 w-4" /> : <StarIcon className="h-4 w-4" />}
        </button>
        <button onClick={onEdit} className="text-gray-600 hover:text-gray-800">
          <PencilIcon className="h-4 w-4" />
        </button>
        <button onClick={onDelete} className="text-red-600 hover:text-red-800">
          <TrashIcon className="h-4 w-4" />
        </button>
      </div>
    </div>
    <p className="text-sm text-gray-600 mb-1">{author.presetData.authorName}</p>
    <p className="text-xs text-gray-500 truncate">{author.presetData.authorBio}</p>
  </div>
);

const ProductCard = ({ product, isSelected, onSelect, onEdit, onDelete }) => (
  <div className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
    isSelected ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:border-gray-300'
  }`}>
    <div className="flex items-start justify-between mb-2">
      <h4 className="font-medium text-gray-900">{product.presetName}</h4>
      <div className="flex items-center space-x-1">
        <button onClick={onSelect} className="text-indigo-600 hover:text-indigo-800">
          {isSelected ? <StarSolidIcon className="h-4 w-4" /> : <StarIcon className="h-4 w-4" />}
        </button>
        <button onClick={onEdit} className="text-gray-600 hover:text-gray-800">
          <PencilIcon className="h-4 w-4" />
        </button>
        <button onClick={onDelete} className="text-red-600 hover:text-red-800">
          <TrashIcon className="h-4 w-4" />
        </button>
      </div>
    </div>
    <p className="text-sm text-gray-600 mb-1">{product.presetData.name}</p>
    <p className="text-xs text-gray-500 truncate">{product.presetData.description}</p>
  </div>
);

// Form Components
const ResourceForm = ({ formData, setFormData, onSubmit, onCancel, editingItem }) => (
  <div className="bg-gray-50 rounded-lg p-6 mb-6">
    <h4 className="font-semibold text-gray-900 mb-4">
      {editingItem ? 'Edit Resource' : 'Add New Resource'}
    </h4>
    <form onSubmit={onSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Resource Name</label>
          <input
            type="text"
            value={formData.resource_name}
            onChange={(e) => setFormData({...formData, resource_name: e.target.value})}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
          <select
            value={formData.resource_type}
            onChange={(e) => setFormData({...formData, resource_type: e.target.value})}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          >
            <option value="url">URL</option>
            <option value="reddit">Reddit</option>
            <option value="document">Document</option>
            <option value="manual">Manual</option>
          </select>
        </div>
      </div>
      
      {(formData.resource_type === 'url' || formData.resource_type === 'reddit') && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">URL</label>
          <input
            type="url"
            value={formData.resource_url}
            onChange={(e) => setFormData({...formData, resource_url: e.target.value})}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            required
          />
        </div>
      )}
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({...formData, description: e.target.value})}
          rows={3}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
        />
      </div>
      
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
        >
          {editingItem ? 'Update' : 'Create'}
        </button>
      </div>
    </form>
  </div>
);

const AuthorForm = ({ formData, setFormData, onSubmit, onCancel, editingItem }) => (
  <div className="bg-gray-50 rounded-lg p-6 mb-6">
    <h4 className="font-semibold text-gray-900 mb-4">
      {editingItem ? 'Edit Author' : 'Add New Author'}
    </h4>
    <form onSubmit={onSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Profile Name</label>
        <input
          type="text"
          value={formData.presetName}
          onChange={(e) => setFormData({...formData, presetName: e.target.value})}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          required
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Author Name</label>
          <input
            type="text"
            value={formData.presetData.authorName}
            onChange={(e) => setFormData({
              ...formData,
              presetData: {...formData.presetData, authorName: e.target.value}
            })}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Target Audience</label>
          <input
            type="text"
            value={formData.presetData.targetAudience}
            onChange={(e) => setFormData({
              ...formData,
              presetData: {...formData.presetData, targetAudience: e.target.value}
            })}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          />
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Author Bio</label>
        <textarea
          value={formData.presetData.authorBio}
          onChange={(e) => setFormData({
            ...formData,
            presetData: {...formData.presetData, authorBio: e.target.value}
          })}
          rows={3}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Article Goal</label>
        <textarea
          value={formData.presetData.articleGoal}
          onChange={(e) => setFormData({
            ...formData,
            presetData: {...formData.presetData, articleGoal: e.target.value}
          })}
          rows={2}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
        />
      </div>
      
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
        >
          {editingItem ? 'Update' : 'Create'}
        </button>
      </div>
    </form>
  </div>
);

const ProductForm = ({ formData, setFormData, onSubmit, onCancel, editingItem }) => {
  const addFeature = () => {
    setFormData({
      ...formData,
      presetData: {
        ...formData.presetData,
        features: [...formData.presetData.features, '']
      }
    });
  };

  const updateFeature = (index, value) => {
    const newFeatures = [...formData.presetData.features];
    newFeatures[index] = value;
    setFormData({
      ...formData,
      presetData: {
        ...formData.presetData,
        features: newFeatures
      }
    });
  };

  const removeFeature = (index) => {
    setFormData({
      ...formData,
      presetData: {
        ...formData.presetData,
        features: formData.presetData.features.filter((_, i) => i !== index)
      }
    });
  };

  return (
    <div className="bg-gray-50 rounded-lg p-6 mb-6">
      <h4 className="font-semibold text-gray-900 mb-4">
        {editingItem ? 'Edit Product' : 'Add New Product'}
      </h4>
      <form onSubmit={onSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Profile Name</label>
          <input
            type="text"
            value={formData.presetName}
            onChange={(e) => setFormData({...formData, presetName: e.target.value})}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            required
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Product Name</label>
            <input
              type="text"
              value={formData.presetData.name}
              onChange={(e) => setFormData({
                ...formData,
                presetData: {...formData.presetData, name: e.target.value}
              })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Product Link</label>
            <input
              type="url"
              value={formData.presetData.link}
              onChange={(e) => setFormData({
                ...formData,
                presetData: {...formData.presetData, link: e.target.value}
              })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea
            value={formData.presetData.description}
            onChange={(e) => setFormData({
              ...formData,
              presetData: {...formData.presetData, description: e.target.value}
            })}
            rows={3}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          />
        </div>
        
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium text-gray-700">Features</label>
            <button
              type="button"
              onClick={addFeature}
              className="text-sm text-indigo-600 hover:text-indigo-800"
            >
              + Add Feature
            </button>
          </div>
          {formData.presetData.features.map((feature, index) => (
            <div key={index} className="flex items-center space-x-2 mb-2">
              <input
                type="text"
                value={feature}
                onChange={(e) => updateFeature(index, e.target.value)}
                className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                placeholder="Feature description"
              />
              <button
                type="button"
                onClick={() => removeFeature(index)}
                className="text-red-600 hover:text-red-800"
              >
                <TrashIcon className="h-4 w-4" />
              </button>
            </div>
          ))}
        </div>
        
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
          >
            {editingItem ? 'Update' : 'Create'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ManagementHeader;