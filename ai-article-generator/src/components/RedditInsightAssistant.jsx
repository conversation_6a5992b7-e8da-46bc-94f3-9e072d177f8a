import React, { useState } from 'react';
import { 
  MagnifyingGlassIcon, 
  ArrowUpIcon, 
  ChatBubbleLeftIcon,
  FireIcon,
  SparklesIcon,
  ClockIcon,
  ArrowTopRightOnSquareIcon,
  LinkIcon,
  XMarkIcon,
  EyeIcon,
  DocumentTextIcon,
  ChevronRightIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import { apiCall, API_CONFIG } from '../config/api';

const RedditInsightAssistant = ({ onUsePost, currentKeyword, selectedPosts = [] }) => {
  const [subreddit, setSubreddit] = useState('');
  const [keyword, setKeyword] = useState(currentKeyword || '');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [redditAuth, setRedditAuth] = useState({
    connected: false,
    username: null,
    loading: false
  });
  const [selectedPost, setSelectedPost] = useState(null);
  const [postDetails, setPostDetails] = useState(null);
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [expandedComments, setExpandedComments] = useState(new Set());
  const [expandedTexts, setExpandedTexts] = useState(new Set());
  const [subredditSuggestions, setSubredditSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Check Reddit OAuth status on component mount
  React.useEffect(() => {
    checkRedditAuthStatus();
  }, []);

  // Check Reddit OAuth authorization status
  const checkRedditAuthStatus = async () => {
    setRedditAuth(prev => ({ ...prev, loading: true }));
    try {
      const response = await apiCall('/api/reddit-oauth/status');
      setRedditAuth({
        connected: response.connected,
        username: response.username,
        loading: false
      });
    } catch (err) {
      console.error('Error checking Reddit auth status:', err);
      setRedditAuth({
        connected: false,
        username: null,
        loading: false
      });
    }
  };

  // Update keyword when currentKeyword prop changes
  React.useEffect(() => {
    if (currentKeyword) {
      setKeyword(currentKeyword);
    }
  }, [currentKeyword]);

  // Handle URL parameters for OAuth callback
  React.useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const redditAuthStatus = urlParams.get('reddit_auth');
    const username = urlParams.get('username');
    const message = urlParams.get('message');

    if (redditAuthStatus === 'success') {
      setRedditAuth({
        connected: true,
        username: decodeURIComponent(username || ''),
        loading: false
      });
      setError(null);
      // Clean up OAuth parameters but preserve other query params
      const newUrl = new URL(window.location);
      newUrl.searchParams.delete('reddit_auth');
      newUrl.searchParams.delete('username');
      window.history.replaceState({}, document.title, newUrl.toString());
    } else if (redditAuthStatus === 'error') {
      setError('Reddit authorization failed: ' + decodeURIComponent(message || 'Unknown error'));
      // Clean up OAuth parameters but preserve other query params
      const newUrl = new URL(window.location);
      newUrl.searchParams.delete('reddit_auth');
      newUrl.searchParams.delete('message');
      window.history.replaceState({}, document.title, newUrl.toString());
    }
  }, []);

  // Initiate Reddit OAuth flow
  const connectReddit = async () => {
    setRedditAuth(prev => ({ ...prev, loading: true }));
    try {
      // Get current page path to return to after OAuth
      const currentPath = window.location.pathname + window.location.search;
      const response = await apiCall(`/api/reddit-oauth/auth?returnUrl=${encodeURIComponent(currentPath)}`);
      // Redirect to Reddit authorization page
      window.location.href = response.authUrl;
    } catch (err) {
      console.error('Error initiating Reddit OAuth:', err);
      setError('Failed to initiate Reddit authorization. Please try again.');
      setRedditAuth(prev => ({ ...prev, loading: false }));
    }
  };

  // Disconnect Reddit account
  const disconnectReddit = async () => {
    try {
      await apiCall('/api/reddit-oauth/disconnect', { method: 'DELETE' });
      setRedditAuth({
        connected: false,
        username: null,
        loading: false
      });
      setError(null);
    } catch (err) {
      console.error('Error disconnecting Reddit:', err);
      setError('Failed to disconnect Reddit account. Please try again.');
    }
  };

  // Search subreddits based on input
  const searchSubreddits = async (query) => {
    if (!redditAuth.connected || !query || query.length < 2) {
      setSubredditSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    try {
      const response = await apiCall(`/api/reddit/subreddits/search?q=${encodeURIComponent(query)}`);
      setSubredditSuggestions(response.subreddits || []);
      setShowSuggestions(true);
    } catch (err) {
      console.error('Error searching subreddits:', err);
      setSubredditSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Handle subreddit input change with debounce
  const handleSubredditChange = (value) => {
    setSubreddit(value);
    
    // Clear suggestions if input is too short
    if (!value || value.length < 2) {
      setShowSuggestions(false);
      setSubredditSuggestions([]);
    }
    
    // Debounce the search
    const timer = setTimeout(() => {
      searchSubreddits(value);
    }, 300);

    return () => clearTimeout(timer);
  };

  // Select a suggested subreddit
  const selectSubreddit = (subredditName) => {
    setSubreddit(subredditName);
    setShowSuggestions(false);
    setSubredditSuggestions([]);
  };

  const searchReddit = async () => {
    if (!redditAuth.connected) {
      setError('Please connect your Reddit account first');
      return;
    }

    if (!subreddit || !keyword) {
      setError('Please enter both subreddit and keyword');
      return;
    }

    setIsSearching(true);
    setError(null);
    
    try {
      const response = await apiCall('/api/reddit/search', {
        method: 'POST',
        body: JSON.stringify({
          subreddit: subreddit.replace(/^r\//, ''), // Remove r/ prefix if present
          keyword,
          limit: 20
        })
      });

      if (response.error) {
        throw new Error(response.error);
      }

      setSearchResults(response.posts || []);
    } catch (err) {
      console.error('Reddit search error:', err);
      if (err.message.includes('401') || err.message.includes('authorization')) {
        setError('Reddit authorization expired. Please reconnect your account.');
        setRedditAuth({ connected: false, username: null, loading: false });
      } else {
        setError('Failed to search Reddit. Please try again.');
      }
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Fetch detailed post with comments
  const fetchPostDetails = async (post) => {
    setLoadingDetails(true);
    setSelectedPost(post);
    try {
      const response = await apiCall(`/api/reddit/post/${post.id}?subreddit=${post.subreddit}`);
      setPostDetails(response.post);
    } catch (err) {
      console.error('Error fetching post details:', err);
      setError('Failed to fetch post details');
    } finally {
      setLoadingDetails(false);
    }
  };

  // Toggle comment expansion
  const toggleComment = (commentId) => {
    const newExpanded = new Set(expandedComments);
    if (newExpanded.has(commentId)) {
      newExpanded.delete(commentId);
    } else {
      newExpanded.add(commentId);
    }
    setExpandedComments(newExpanded);
  };

  // Toggle text expansion
  const toggleText = (textId) => {
    const newExpanded = new Set(expandedTexts);
    if (newExpanded.has(textId)) {
      newExpanded.delete(textId);
    } else {
      newExpanded.add(textId);
    }
    setExpandedTexts(newExpanded);
  };

  // Text display component with expand/collapse
  const ExpandableText = ({ text, maxLength = 400, id }) => {
    const isExpanded = expandedTexts.has(id);
    const shouldTruncate = text.length > maxLength;
    
    return (
      <div>
        <div className="text-sm text-gray-800 leading-relaxed whitespace-pre-wrap">
          {shouldTruncate && !isExpanded ? text.substring(0, maxLength) + '...' : text}
        </div>
        {shouldTruncate && (
          <button
            onClick={() => toggleText(id)}
            className="mt-2 text-xs text-blue-600 hover:text-blue-800"
          >
            {isExpanded ? 'Show less' : 'Show more'}
          </button>
        )}
      </div>
    );
  };

  const handleUsePost = (post) => {
    console.log('🔥 handleUsePost called with post:', post);
    console.log('🔥 onUsePost callback exists:', !!onUsePost);
    
    if (onUsePost) {
      // Extract keywords from title and subreddit
      const titleWords = post.title.toLowerCase()
        .split(/\s+/)
        .filter(word => word.length > 3 && !['this', 'that', 'with', 'from', 'have'].includes(word))
        .slice(0, 5);

      const postData = {
        topic: post.title,
        keywords: [keyword, ...titleWords],
        source: {
          type: 'reddit',
          url: post.url,
          subreddit: post.subreddit,
          upvotes: post.upvotes || post.score || 0,
          comments: post.num_comments || 0
        }
      };
      
      console.log('🔥 Calling onUsePost with data:', postData);
      onUsePost(postData);
    } else {
      console.error('🔥 onUsePost callback is not provided');
    }
  };

  // Check if a post is already selected
  const isPostSelected = (post) => {
    return selectedPosts.some(selectedPost => selectedPost.url === post.url);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-6">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Reddit Insight Assistant
            </h3>
            <p className="text-sm text-gray-600">
              Discover trending topics and real user discussions from Reddit communities
            </p>
          </div>
          <div className="flex items-center gap-2">
            {redditAuth.connected ? (
              <div className="flex items-center gap-2">
                <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                  Connected as u/{redditAuth.username}
                </span>
                <button
                  onClick={disconnectReddit}
                  className="flex items-center gap-1 text-sm text-red-600 hover:text-red-800 px-2 py-1 rounded"
                >
                  <XMarkIcon className="w-4 h-4" />
                  Disconnect
                </button>
              </div>
            ) : (
              <button
                onClick={connectReddit}
                disabled={redditAuth.loading}
                className="flex items-center gap-1 text-sm bg-orange-600 text-white px-3 py-1 rounded hover:bg-orange-700 disabled:bg-gray-400"
              >
                <LinkIcon className="w-4 h-4" />
                {redditAuth.loading ? 'Connecting...' : 'Connect Reddit'}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* OAuth Information Panel */}
      {!redditAuth.connected && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
            <LinkIcon className="w-5 h-5 text-blue-600" />
            Reddit Account Required
          </h4>
          <p className="text-sm text-gray-700 mb-3">
            Connect your Reddit account to search subreddits and discover trending topics. 
            This uses Reddit's official OAuth2 authentication for secure access.
          </p>
          <div className="text-xs text-gray-600 bg-white p-3 rounded border">
            <p className="font-medium mb-1">✅ Secure & Private:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>Uses Reddit's official OAuth2 authentication</li>
              <li>No passwords or API keys stored</li>
              <li>Read-only access to public posts</li>
              <li>You can disconnect anytime</li>
            </ul>
          </div>
        </div>
      )}

      {/* Search Form */}
      <div className="space-y-4 mb-6">
        <div className="grid grid-cols-2 gap-4">
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Target Subreddit
            </label>
            <input
              type="text"
              value={subreddit}
              onChange={(e) => handleSubredditChange(e.target.value)}
              onFocus={() => {
                if (subredditSuggestions.length > 0) setShowSuggestions(true);
              }}
              onBlur={() => {
                // Delay hiding to allow clicking on suggestions
                setTimeout(() => setShowSuggestions(false), 200);
              }}
              placeholder="e.g., productivity, technology, fitness"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            
            {/* Subreddit Suggestions Dropdown */}
            {showSuggestions && subredditSuggestions.length > 0 && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                {subredditSuggestions.map((sub) => (
                  <div
                    key={sub.name}
                    onClick={() => selectSubreddit(sub.name)}
                    className="px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">r/{sub.name}</div>
                        <div className="text-sm text-gray-600 truncate">{sub.title}</div>
                        {sub.description && (
                          <div className="text-xs text-gray-500 truncate mt-1">
                            {sub.description.length > 80 ? sub.description.substring(0, 80) + '...' : sub.description}
                          </div>
                        )}
                      </div>
                      {sub.subscribers && (
                        <div className="text-xs text-gray-400 ml-2">
                          {sub.subscribers > 1000000 
                            ? `${(sub.subscribers / 1000000).toFixed(1)}M` 
                            : sub.subscribers > 1000 
                            ? `${(sub.subscribers / 1000).toFixed(0)}k` 
                            : sub.subscribers} members
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Core Keyword
            </label>
            <input
              type="text"
              value={keyword}
              onChange={(e) => setKeyword(e.target.value)}
              placeholder="e.g., pomodoro"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <div className="flex items-center gap-4">
          <button
            onClick={searchReddit}
            disabled={isSearching}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 flex items-center gap-2"
          >
            <MagnifyingGlassIcon className="w-4 h-4" />
            {isSearching ? 'Searching...' : 'Search All Posts (Hot, Top & New)'}
          </button>
          
          <div className="text-sm text-gray-600">
            <span className="flex items-center gap-1">
              <FireIcon className="w-4 h-4 text-orange-500" />
              <SparklesIcon className="w-4 h-4 text-yellow-500" />
              <ClockIcon className="w-4 h-4 text-blue-500" />
              All sort types included
            </span>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-700 text-sm">
          {error}
        </div>
      )}

      {/* Search Results */}
      {searchResults.length > 0 && (
        <div className="border-t pt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            Found {searchResults.length} relevant posts
          </h4>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {searchResults.map((post, index) => (
              <div
                key={post.id || index}
                className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex justify-between items-start gap-4">
                  <div className="flex-1">
                    <h5 className="font-medium text-gray-900 line-clamp-2 mb-2">
                      {post.title}
                    </h5>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span className="flex items-center gap-1">
                        <ArrowUpIcon className="w-4 h-4 text-orange-500" />
                        {post.upvotes || post.score || 0}
                      </span>
                      <span className="flex items-center gap-1">
                        <ChatBubbleLeftIcon className="w-4 h-4 text-blue-500" />
                        {post.num_comments || 0} comments
                      </span>
                      <a
                        href={post.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
                      >
                        View
                        <ArrowTopRightOnSquareIcon className="w-3 h-3" />
                      </a>
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <button
                      onClick={() => fetchPostDetails(post)}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 whitespace-nowrap flex items-center gap-1"
                    >
                      <EyeIcon className="w-3 h-3" />
                      View Details
                    </button>
                    <button
                      onClick={() => handleUsePost(post)}
                      className={`px-3 py-1 text-white text-sm rounded-md whitespace-nowrap flex items-center gap-1 ${
                        isPostSelected(post) 
                          ? 'bg-red-600 hover:bg-red-700' 
                          : 'bg-green-600 hover:bg-green-700'
                      }`}
                    >
                      {isPostSelected(post) ? (
                        <>
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                          Remove
                        </>
                      ) : (
                        'Use this Topic'
                      )}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Post Details Modal */}
      {selectedPost && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex justify-between items-center p-4 border-b">
              <h3 className="text-lg font-semibold text-gray-900">Reddit Post Details</h3>
              <button
                onClick={() => {
                  setSelectedPost(null);
                  setPostDetails(null);
                  setExpandedComments(new Set());
                  setExpandedTexts(new Set());
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            </div>
            
            <div className="p-4 overflow-y-auto max-h-[80vh]">
              {loadingDetails ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-600">Loading post details...</p>
                </div>
              ) : postDetails ? (
                <div className="space-y-6">
                  {/* Post Header */}
                  <div className="border-b pb-4">
                    <h4 className="text-xl font-semibold text-gray-900 mb-2">{postDetails.title}</h4>
                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                      <span>r/{postDetails.subreddit}</span>
                      <span>by u/{postDetails.author}</span>
                      <span className="flex items-center gap-1">
                        <ArrowUpIcon className="w-4 h-4 text-orange-500" />
                        {postDetails.score} ({Math.round(postDetails.upvote_ratio * 100)}% upvoted)
                      </span>
                      <span>{postDetails.num_comments} comments</span>
                    </div>
                    {postDetails.selftext && (
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h6 className="font-medium text-gray-700 mb-2">Post Content:</h6>
                        <ExpandableText 
                          text={postDetails.selftext} 
                          maxLength={600} 
                          id={`post-${postDetails.id}`} 
                        />
                      </div>
                    )}
                  </div>

                  {/* Insights */}
                  {postDetails.insights && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Common Themes */}
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h5 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                          <DocumentTextIcon className="w-4 h-4" />
                          Common Themes
                        </h5>
                        <div className="space-y-1">
                          {postDetails.insights.common_themes.slice(0, 5).map((theme, index) => (
                            <div key={index} className="flex justify-between text-sm">
                              <span className="text-blue-800">{theme.word}</span>
                              <span className="text-blue-600">({theme.count})</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Pain Points */}
                      <div className="bg-red-50 p-4 rounded-lg">
                        <h5 className="font-medium text-red-900 mb-2">User Pain Points</h5>
                        <div className="space-y-2">
                          {postDetails.insights.user_pain_points.map((pain, index) => (
                            <div key={index} className="text-sm text-red-800 bg-red-100 p-2 rounded">
                              {pain.length > 100 ? pain.substring(0, 100) + '...' : pain}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Comments */}
                  <div>
                    <h5 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                      <ChatBubbleLeftIcon className="w-4 h-4" />
                      Top Comments ({postDetails.comments?.length || 0})
                    </h5>
                    <div className="space-y-3">
                      {postDetails.comments?.slice(0, 10).map((comment) => (
                        <div key={comment.id} className="border rounded p-3" style={{ marginLeft: comment.depth * 16 }}>
                          <div className="flex justify-between items-start mb-2">
                            <span className="text-sm font-medium text-gray-700">u/{comment.author}</span>
                            <div className="flex items-center gap-2 text-xs text-gray-500">
                              <span>{comment.score} points</span>
                              {comment.replies?.length > 0 && (
                                <button
                                  onClick={() => toggleComment(comment.id)}
                                  className="flex items-center gap-1 hover:text-gray-700"
                                >
                                  {expandedComments.has(comment.id) ? (
                                    <ChevronDownIcon className="w-3 h-3" />
                                  ) : (
                                    <ChevronRightIcon className="w-3 h-3" />
                                  )}
                                  {comment.replies.length} replies
                                </button>
                              )}
                            </div>
                          </div>
                          <ExpandableText 
                            text={comment.body} 
                            maxLength={400} 
                            id={`comment-${comment.id}`} 
                          />
                          
                          {/* Nested replies */}
                          {expandedComments.has(comment.id) && comment.replies?.map((reply) => (
                            <div key={reply.id} className="mt-2 ml-4 border-l-2 border-gray-200 pl-3">
                              <div className="flex justify-between items-start mb-1">
                                <span className="text-xs font-medium text-gray-600">u/{reply.author}</span>
                                <span className="text-xs text-gray-400">{reply.score} points</span>
                              </div>
                              <div className="text-xs text-gray-700">
                                <ExpandableText 
                                  text={reply.body} 
                                  maxLength={250} 
                                  id={`reply-${reply.id}`} 
                                />
                              </div>
                            </div>
                          ))}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <p>Failed to load post details</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {!isSearching && searchResults.length === 0 && !error && (
        <div className="text-center py-8 text-gray-500">
          <MagnifyingGlassIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p className="text-sm">
            Search Reddit to discover trending topics and discussions
          </p>
        </div>
      )}
    </div>
  );
};

export default RedditInsightAssistant;