import React, { useState } from 'react';
import PresetSelector from './PresetSelector';
import PresetSaveModal from './PresetSaveModal';

const Step4EEAT = ({ data, updateData }) => {
  const [showSaveModal, setShowSaveModal] = useState(false);

  const updateParameters = (field, value) => {
    updateData({
      eeatProfile: {
        ...data.eeatProfile,
        [field]: value
      }
    });
  };

  const handlePresetSelect = (presetData) => {
    updateData({
      eeatProfile: {
        authorName: presetData.authorName || '',
        authorBio: presetData.authorBio || '',
        targetAudience: presetData.targetAudience || '',
        articleGoal: presetData.articleGoal || ''
      }
    });
  };

  const handleSavePreset = () => {
    setShowSaveModal(true);
  };

  const handlePresetSaved = () => {
    // Optionally refresh presets or show success message
    setShowSaveModal(false);
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Establish Your Authority</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          <span className="font-semibold text-blue-600">Step 5 of 7:</span> Build credibility and trust by providing your expertise information.
          This E-E-A-T (Experience, Expertise, Authoritativeness, Trustworthiness) profile helps establish your article's authority
          and enhances reader confidence in your content.
        </p>
      </div>

      {/* E-E-A-T Explanation */}
      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-6 border border-purple-200">
        <h3 className="text-xl font-bold text-purple-900 mb-4">🏆 Why E-E-A-T Matters</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="bg-white rounded-lg p-4 border border-purple-100">
            <h4 className="font-semibold text-purple-800 mb-2">📚 Experience & Expertise</h4>
            <p className="text-purple-700">Demonstrates your knowledge and background in the topic area</p>
          </div>
          <div className="bg-white rounded-lg p-4 border border-purple-100">
            <h4 className="font-semibold text-purple-800 mb-2">⭐ Authoritativeness</h4>
            <p className="text-purple-700">Establishes your credibility and recognition in your field</p>
          </div>
          <div className="bg-white rounded-lg p-4 border border-purple-100">
            <h4 className="font-semibold text-purple-800 mb-2">🛡️ Trustworthiness</h4>
            <p className="text-purple-700">Builds reader confidence through transparency and accuracy</p>
          </div>
          <div className="bg-white rounded-lg p-4 border border-purple-100">
            <h4 className="font-semibold text-purple-800 mb-2">🎯 Better Rankings</h4>
            <p className="text-purple-700">Helps search engines understand your content quality</p>
          </div>
        </div>
      </div>

      {/* Preset Selector */}
      <PresetSelector
        presetType="author"
        onPresetSelect={handlePresetSelect}
        onSavePreset={handleSavePreset}
        currentData={data.eeatProfile}
      />

      {/* E-E-A-T Profile Form */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h4 className="text-lg font-medium text-blue-900 mb-4">👤 Your Authority Profile</h4>
        <p className="text-sm text-blue-700 mb-6">
          This information will be subtly integrated into your article to establish credibility. All fields are optional but recommended.
        </p>

        {/* Author Name */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-blue-800 mb-2">
            Author Name / Brand Name
          </label>
          <input
            type="text"
            value={data.eeatProfile?.authorName || ''}
            onChange={(e) => updateParameters('authorName', e.target.value)}
            placeholder="Enter your name or brand name"
            className="w-full px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
          />
          <p className="text-xs text-blue-600 mt-1">
            This will be used to attribute the content and establish authorship
          </p>
        </div>

        {/* Author Bio */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-blue-800 mb-2">
            Author Bio / Brand Description
          </label>
          <textarea
            value={data.eeatProfile?.authorBio || ''}
            onChange={(e) => updateParameters('authorBio', e.target.value)}
            placeholder="Brief description of your credentials, experience, or brand expertise (1-2 sentences)"
            rows={3}
            className="w-full px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
          />
          <p className="text-xs text-blue-600 mt-1">
            Highlight your relevant experience, qualifications, or achievements in this topic area
          </p>
        </div>

        {/* Target Audience */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-blue-800 mb-2">
            Target Audience
          </label>
          <input
            type="text"
            value={data.eeatProfile?.targetAudience || ''}
            onChange={(e) => updateParameters('targetAudience', e.target.value)}
            placeholder="e.g., Beginners, IT Managers, Small Business Owners, Marketing Professionals"
            className="w-full px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
          />
          <p className="text-xs text-blue-600 mt-1">
            Helps tailor the content complexity and tone to your intended readers
          </p>
        </div>

        {/* Article Goal */}
        <div>
          <label className="block text-sm font-medium text-blue-800 mb-2">
            Article Goal
          </label>
          <input
            type="text"
            value={data.eeatProfile?.articleGoal || ''}
            onChange={(e) => updateParameters('articleGoal', e.target.value)}
            placeholder="e.g., Educate, Drive Sales, Get Signups, Build Awareness, Solve Problems"
            className="w-full px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
          />
          <p className="text-xs text-blue-600 mt-1">
            Defines the primary objective and helps shape the content strategy
          </p>
        </div>
      </div>

      {/* Profile Preview */}
      {(data.eeatProfile?.authorName || data.eeatProfile?.authorBio || data.eeatProfile?.targetAudience || data.eeatProfile?.articleGoal) && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="text-md font-medium text-green-800 mb-3">📋 Authority Profile Preview</h4>

          {data.eeatProfile?.authorName && (
            <div className="mb-2">
              <span className="text-sm font-medium text-green-700">Author: </span>
              <span className="text-sm text-green-600">{data.eeatProfile.authorName}</span>
            </div>
          )}

          {data.eeatProfile?.authorBio && (
            <div className="mb-2">
              <span className="text-sm font-medium text-green-700">Bio: </span>
              <span className="text-sm text-green-600">{data.eeatProfile.authorBio}</span>
            </div>
          )}

          {data.eeatProfile?.targetAudience && (
            <div className="mb-2">
              <span className="text-sm font-medium text-green-700">Target Audience: </span>
              <span className="text-sm text-green-600">{data.eeatProfile.targetAudience}</span>
            </div>
          )}

          {data.eeatProfile?.articleGoal && (
            <div className="mb-2">
              <span className="text-sm font-medium text-green-700">Goal: </span>
              <span className="text-sm text-green-600">{data.eeatProfile.articleGoal}</span>
            </div>
          )}
        </div>
      )}

      {/* Skip Option */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <p className="text-sm text-gray-600 mb-2">
          <strong>Optional Step:</strong> You can skip this step if you prefer to create content without author attribution.
        </p>
        <p className="text-xs text-gray-500">
          However, providing E-E-A-T information typically results in more credible and authoritative content.
        </p>
      </div>

      {/* Save Preset Modal */}
      <PresetSaveModal
        isOpen={showSaveModal}
        onClose={() => setShowSaveModal(false)}
        presetType="author"
        presetData={data.eeatProfile}
        onPresetSaved={handlePresetSaved}
      />
    </div>
  );
};

export default Step4EEAT;
