import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  SparklesIcon,
  DocumentDuplicateIcon,
  ArrowDownTrayIcon,
  EyeIcon,
  PencilIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon,
  ClipboardDocumentIcon,
  DocumentTextIcon,
  CloudArrowUpIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { API_CONFIG } from '../config/api';
import { useAuth } from '../contexts/AuthContext';

const Step5Generation = ({ data, updateData, taskId }) => {
  const { authenticatedFetch } = useAuth();
  const [isGenerating, setIsGenerating] = useState(false);
  const [editableContent, setEditableContent] = useState(data.generated_article || '');
  const [viewMode, setViewMode] = useState('split'); // 'editor', 'preview', 'split'
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [parsedArticle, setParsedArticle] = useState(null);

  // Auto-save state
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState('saved'); // 'saved', 'saving', 'error', 'unsaved'
  const [lastSaved, setLastSaved] = useState(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const editorRef = useRef(null);
  const previewRef = useRef(null);
  const saveTimeoutRef = useRef(null);

  // Parse article content to extract metadata and body
  const parseArticleContent = (content) => {
    if (!content) return null;

    const lines = content.split('\n');
    const metadata = {};
    let bodyStartIndex = 0;

    // Look for metadata sections
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line.startsWith('**SEO Title:**')) {
        metadata.seoTitle = line.replace('**SEO Title:**', '').trim();
      } else if (line.startsWith('**Meta Description:**')) {
        metadata.metaDescription = line.replace('**Meta Description:**', '').trim();
      } else if (line.startsWith('**Focus Keywords:**')) {
        metadata.focusKeywords = line.replace('**Focus Keywords:**', '').trim();
      } else if (line.startsWith('**Tags:**')) {
        metadata.tags = line.replace('**Tags:**', '').trim();
      } else if (line.includes('----------')) {
        bodyStartIndex = i + 1;
        break;
      }
    }

    const body = lines.slice(bodyStartIndex).join('\n').trim();
    return { metadata, body };
  };

  // Enhanced markdown to HTML conversion
  const markdownToHtml = (markdown) => {
    if (!markdown) return '';

    let html = markdown
      // Headers
      .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold text-gray-900 mb-6 border-b-2 border-gray-200 pb-2">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-semibold text-gray-800 mb-4 mt-8 border-b border-gray-200 pb-1">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 class="text-xl font-medium text-gray-700 mb-3 mt-6">$1</h3>')
      .replace(/^#### (.*$)/gim, '<h4 class="text-lg font-medium text-gray-700 mb-2 mt-4">$1</h4>')

      // Bold and italic
      .replace(/\*\*(.*?)\*\*/gim, '<strong class="font-semibold text-gray-900">$1</strong>')
      .replace(/\*(.*?)\*/gim, '<em class="italic text-gray-700">$1</em>')

      // Code blocks and inline code
      .replace(/```([\s\S]*?)```/gim, '<pre class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto mb-4"><code>$1</code></pre>')
      .replace(/`([^`]+)`/gim, '<code class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm">$1</code>')

      // Blockquotes
      .replace(/^> (.*$)/gim, '<blockquote class="border-l-4 border-blue-500 bg-blue-50 pl-4 py-2 mb-4 italic text-gray-700">$1</blockquote>')

      // Links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" class="text-blue-600 hover:text-blue-800 underline" target="_blank" rel="noopener noreferrer">$1</a>')

      // Lists - handle multiple lines
      .replace(/^[\*\-] (.+)$/gim, '<li class="mb-1">$1</li>')
      .replace(/^(\d+)\. (.+)$/gim, '<li class="mb-1">$2</li>');

    // Wrap consecutive list items in ul/ol tags
    html = html.replace(/(<li class="mb-1">.*<\/li>)/gims, (match) => {
      return `<ul class="list-disc list-inside mb-4 space-y-1 ml-4">${match}</ul>`;
    });

    // Handle paragraphs
    html = html
      .split('\n\n')
      .map(paragraph => {
        paragraph = paragraph.trim();
        if (!paragraph) return '';
        if (paragraph.startsWith('<h') || paragraph.startsWith('<ul') ||
            paragraph.startsWith('<ol') || paragraph.startsWith('<pre') ||
            paragraph.startsWith('<blockquote')) {
          return paragraph;
        }
        return `<p class="mb-4 text-gray-700 leading-relaxed">${paragraph}</p>`;
      })
      .join('\n');

    return html;
  };

  // Auto-save functionality with debouncing
  const debouncedSave = useCallback(
    (content) => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }

      setSaveStatus('unsaved');
      setHasUnsavedChanges(true);

      saveTimeoutRef.current = setTimeout(() => {
        saveArticle(content);
      }, 2000); // Auto-save after 2 seconds of inactivity
    },
    [updateData]
  );

  // Manual save function
  const saveArticle = useCallback(
    async (content = editableContent) => {
      // Don't save if content is empty
      if (!content || content.trim().length === 0) {
        console.log('Skipping save - content is empty');
        return;
      }

      // Always save if there are changes, even if content equals original generated_article
      // This allows users to make modifications and save them
      console.log('Saving article content, length:', content.length);

      setIsSaving(true);
      setSaveStatus('saving');

      try {
        // Update the data through the parent component (TaskEditor)
        updateData({ generated_article: content });

        setSaveStatus('saved');
        setHasUnsavedChanges(false);
        setLastSaved(new Date());
        console.log('Article saved successfully');
      } catch (error) {
        console.error('Error saving article:', error);
        setSaveStatus('error');
      } finally {
        setIsSaving(false);
      }
    },
    [editableContent, updateData]
  );

  // Update parsed article when content changes
  useEffect(() => {
    if (editableContent) {
      const parsed = parseArticleContent(editableContent);
      setParsedArticle(parsed);
    }
  }, [editableContent]);

  // Sync editableContent with data.generated_article when it changes
  // Only sync on initial load or when article is first generated
  useEffect(() => {
    console.log('useEffect triggered - data.generated_article:', data.generated_article ? 'exists' : 'null');
    console.log('Current editableContent length:', editableContent.length);
    console.log('Has unsaved changes:', hasUnsavedChanges);
    console.log('Last saved:', lastSaved);

    // Only sync if:
    // 1. editableContent is empty (initial load)
    // 2. AND we don't have unsaved changes (to prevent overwriting user edits)
    // 3. AND this is not a result of our own save operation (check lastSaved)
    const isInitialLoad = !editableContent || editableContent.length === 0;
    const isNotOurSave = !lastSaved || (Date.now() - lastSaved.getTime()) > 5000; // More than 5 seconds ago

    if (data.generated_article && isInitialLoad && !hasUnsavedChanges && isNotOurSave) {
      console.log('Syncing editableContent with data.generated_article (initial load)');
      setEditableContent(data.generated_article);
    } else {
      console.log('Skipping sync to preserve user edits');
    }
  }, [data.generated_article]);

  // Initialize save status when article is first generated
  useEffect(() => {
    if (data.generated_article && !lastSaved) {
      setLastSaved(new Date());
      setSaveStatus('saved');
    }
  }, [data.generated_article, lastSaved]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  // Keyboard shortcut for saving (Ctrl+S / Cmd+S)
  useEffect(() => {
    const handleKeyDown = (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        if (hasUnsavedChanges || saveStatus === 'error') {
          saveArticle();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [hasUnsavedChanges, saveStatus, saveArticle]);

  const finishTask = async () => {
    try {
      console.log('Finishing task:', taskId);
      const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.TASKS.FINISH(taskId), {
        method: 'POST'
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('Task finish failed:', response.status, errorData);
        throw new Error(`Failed to finish task: ${response.status} ${errorData}`);
      }

      const result = await response.json();
      console.log('Task finished successfully:', result);

      // Update with the complete finished task data
      updateData(result.task);

      // Show success message
      alert('Task completed successfully! The article has been finalized.');

    } catch (error) {
      console.error('Error finishing task:', error);
      alert('Failed to finish task. Please try again.');
    }
  };

  const generateArticle = async () => {
    setIsGenerating(true);
    try {
      if (!taskId) {
        throw new Error('Task ID is required for article generation');
      }

      console.log('Making authenticated API call to generate article for task:', taskId);
      const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.TASKS.GENERATE_ARTICLE(taskId), {
        method: 'POST'
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('Article generation failed:', response.status, errorData);
        throw new Error(`Failed to generate article: ${response.status} ${errorData}`);
      }

      const result = await response.json();
      console.log('Article generation response:', result);

      console.log('Article generated:', result.article.substring(0, 100) + '...');
      setEditableContent(result.article);

      console.log('Calling updateData with generated_article');
      updateData({ generated_article: result.article });

      // Mark as saved since we just generated it
      setSaveStatus('saved');
      setHasUnsavedChanges(false);
      setLastSaved(new Date());
    } catch (error) {
      console.error('Error generating article:', error);

      // Handle specific error types
      let errorMessage = 'Failed to generate article. Please try again.';
      if (error.message.includes('429') || error.message.includes('Too many requests')) {
        errorMessage = 'Rate limit exceeded. Please wait a few minutes before trying again.';
      } else if (error.message.includes('401') || error.message.includes('Unauthorized')) {
        errorMessage = 'Authentication failed. Please refresh the page and try again.';
      } else if (error.message.includes('500')) {
        errorMessage = 'Server error. Please try again in a few moments.';
      } else if (error.message) {
        errorMessage = `Failed to generate article: ${error.message}`;
      }

      alert(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(editableContent);
      alert('Article copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      alert('Failed to copy to clipboard. Please select and copy manually.');
    }
  };

  const downloadAsFile = () => {
    const element = document.createElement('a');
    const file = new Blob([editableContent], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = `article-${Date.now()}.${(data.outputParameters?.format || 'markdown') === 'markdown' ? 'md' : 'txt'}`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const handleContentChange = (e) => {
    const newContent = e.target.value;
    setEditableContent(newContent);

    // Trigger auto-save with debouncing
    debouncedSave(newContent);
  };

  // Enhanced editor operations
  const insertText = (text) => {
    const textarea = editorRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const currentContent = editableContent;
    const newContent = currentContent.substring(0, start) + text + currentContent.substring(end);

    setEditableContent(newContent);
    debouncedSave(newContent);

    // Restore cursor position
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + text.length, start + text.length);
    }, 0);
  };

  const formatText = (format) => {
    const textarea = editorRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = editableContent.substring(start, end);

    let formattedText = '';
    switch (format) {
      case 'bold':
        formattedText = `**${selectedText}**`;
        break;
      case 'italic':
        formattedText = `*${selectedText}*`;
        break;
      case 'h2':
        formattedText = `## ${selectedText}`;
        break;
      case 'h3':
        formattedText = `### ${selectedText}`;
        break;
      case 'list':
        formattedText = selectedText.split('\n').map(line => `- ${line}`).join('\n');
        break;
      default:
        formattedText = selectedText;
    }

    const newContent = editableContent.substring(0, start) + formattedText + editableContent.substring(end);
    setEditableContent(newContent);
    debouncedSave(newContent);
  };

  // Word and character count
  const getStats = () => {
    const words = editableContent.trim() ? editableContent.trim().split(/\s+/).length : 0;
    const characters = editableContent.length;
    const charactersNoSpaces = editableContent.replace(/\s/g, '').length;
    const paragraphs = editableContent.split('\n\n').filter(p => p.trim()).length;

    return { words, characters, charactersNoSpaces, paragraphs };
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Generate Your Article</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          <span className="font-semibold text-blue-600">Step 7 of 7:</span> You're ready to create your article!
          The AI will combine your selected topics, supporting sources, authority profile, and style preferences
          to generate a comprehensive, well-structured article tailored to your specifications.
        </p>
      </div>

      {/* Generation Summary */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-xl p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <SparklesIcon className="w-5 h-5 text-white" />
          </div>
          <h4 className="text-lg font-bold text-blue-800">Article Generation Summary</h4>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Topics Section */}
          <div className="lg:col-span-3">
            <div className="bg-white/70 rounded-lg p-4 border border-blue-200">
              <div className="flex items-center space-x-2 mb-3">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">✓</span>
                </div>
                <span className="font-bold text-blue-800">Selected Topics ({(data.selectedTopics || []).length})</span>
              </div>
              {(data.selectedTopics || []).length > 0 ? (
                <div className="space-y-2">
                  {data.selectedTopics.map((topicObj, index) => (
                    <div key={index} className="bg-white rounded-lg px-4 py-3 border border-blue-200 shadow-sm">
                      <div className="flex items-start space-x-3">
                        <span className="bg-blue-600 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center mt-0.5">
                          {index + 1}
                        </span>
                        <div className="flex-1">
                          <p className="text-blue-800 font-medium">{topicObj.edited}</p>
                          {topicObj.original !== topicObj.edited && (
                            <p className="text-xs text-gray-500 mt-1">
                              Original: {topicObj.original}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-600 font-medium">⚠️ No topics selected - Please go back to Step 2</p>
                </div>
              )}
            </div>
          </div>

          {/* Sources Section */}
          <div>
            <div className="bg-white/70 rounded-lg p-4 border border-blue-200 h-full">
              <div className="flex items-center space-x-2 mb-3">
                <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">📚</span>
                </div>
                <span className="font-bold text-blue-800">Sources & References</span>
              </div>
              {(() => {
                const comprehensiveResources = data.comprehensiveResources || [];
                const currentSources = data.sources || [];
                const redditSourcesFromCurrent = currentSources.filter(s => s.type === 'reddit');
                const nonRedditSourcesFromCurrent = currentSources.filter(s => s.type !== 'reddit');
                const legacyRedditSources = data.redditSources || [];
                
                const totalNonRedditSources = comprehensiveResources.length + nonRedditSourcesFromCurrent.length;
                const totalRedditSources = redditSourcesFromCurrent.length + legacyRedditSources.length;
                const totalSources = totalNonRedditSources + totalRedditSources;

                if (totalSources > 0) {
                  return (
                    <div className="space-y-2">
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                        <p className="text-green-800 font-medium">
                          ✅ {totalSources} source(s) ready
                        </p>
                        {totalNonRedditSources > 0 && (
                          <p className="text-green-600 text-sm mt-1">
                            {totalNonRedditSources} research sources
                          </p>
                        )}
                        {totalRedditSources > 0 && (
                          <p className="text-green-600 text-sm mt-1">
                            {totalRedditSources} Reddit sources
                          </p>
                        )}
                      </div>
                    </div>
                  );
                } else {
                  return (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <p className="text-yellow-800 font-medium">
                        ⚠️ No sources added
                      </p>
                      <p className="text-yellow-600 text-sm mt-1">
                        Article will be generated without external references
                      </p>
                    </div>
                  );
                }
              })()}
            </div>
          </div>

          {/* Reddit Insights Section */}
          <div>
            <div className="bg-white/70 rounded-lg p-4 border border-blue-200 h-full">
              <div className="flex items-center space-x-2 mb-3">
                <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v2H5a3 3 0 000 6h1v3a1 1 0 001.804.596l10-2A1 1 0 0018 14V3z" />
                  </svg>
                </div>
                <span className="font-bold text-blue-800">Reddit Insights</span>
              </div>
              {(() => {
                // Get Reddit sources from both old and new data structures for compatibility
                const currentSources = data.sources || [];
                
                // Debug: Log the actual source objects to see their structure
                console.log('All sources:', currentSources);
                currentSources.forEach((source, index) => {
                  console.log(`Source ${index}:`, {
                    type: source.type,
                    url: source.url,
                    title: source.title,
                    subreddit: source.subreddit,
                    fullSource: source
                  });
                });
                
                const redditSourcesFromCurrent = currentSources.filter(s => s.type === 'reddit');
                const legacyRedditSources = data.redditSources || [];
                const allRedditSources = redditSourcesFromCurrent.length > 0 ? redditSourcesFromCurrent : legacyRedditSources;

                // First check all possible Reddit sources - fix the logic completely
                const comprehensiveResources = data.comprehensiveResources || [];
                const redditFromComprehensive = comprehensiveResources.filter(r => r.type === 'reddit' || r.resource_type === 'reddit');
                const totalRedditSources = redditSourcesFromCurrent.length + legacyRedditSources.length + redditFromComprehensive.length;
                
                console.log('Reddit Debug:', {
                  currentSources: data.sources,
                  redditSourcesFromCurrent,
                  legacyRedditSources,
                  comprehensiveResources,
                  redditFromComprehensive,
                  totalRedditSources
                });

                if (totalRedditSources > 0) {
                  return (
                    <div className="space-y-2">
                      <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                        <p className="text-orange-800 font-medium">
                          ✅ {totalRedditSources} Reddit post{totalRedditSources !== 1 ? 's' : ''} analyzed
                        </p>
                        {totalRedditSources > 0 && (
                          <div className="mt-2 space-y-1">
                            {[...redditSourcesFromCurrent, ...legacyRedditSources, ...redditFromComprehensive].slice(0, 2).map((source, index) => (
                              <p key={index} className="text-orange-600 text-xs">
                                • r/{source.subreddit} ({source.upvotes || source.score || 0} upvotes)
                              </p>
                            ))}
                            {totalRedditSources > 2 && (
                              <p className="text-orange-600 text-xs">
                                • and {totalRedditSources - 2} more...
                              </p>
                            )}
                          </div>
                        )}
                        <p className="text-orange-600 text-xs mt-2">
                          AI will use real user discussions to enhance article authenticity
                        </p>
                      </div>
                    </div>
                  );
                } else {
                  return (
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                      <p className="text-gray-600 font-medium">
                        💡 No Reddit insights
                      </p>
                      <p className="text-gray-500 text-sm mt-1">
                        Add Reddit discussions in Step 3 for more authentic content
                      </p>
                    </div>
                  );
                }
              })()}
            </div>
          </div>

          {/* Configuration Section */}
          <div>
            <div className="bg-white/70 rounded-lg p-4 border border-blue-200 h-full">
              <div className="flex items-center space-x-2 mb-3">
                <div className="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">⚙️</span>
                </div>
                <span className="font-bold text-blue-800">Configuration</span>
              </div>
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-700">Product Integration:</p>
                  <p className="text-blue-600 font-medium">
                    {data.productInfo.name || '❌ No product specified'}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Writing Style:</p>
                  <div className="flex flex-wrap gap-2 mt-1">
                    <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                      {data.outputParameters?.tonality || 'informative'}
                    </span>
                    <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                      {data.outputParameters?.length || 'medium'}
                    </span>
                    <span className="bg-purple-100 text-purple-800 text-xs font-medium px-2 py-1 rounded-full">
                      {data.outputParameters?.format || 'markdown'}
                    </span>
                  </div>
                </div>
                {data.eeatProfile && (data.eeatProfile.authorName || data.eeatProfile.authorBio) && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Authority Profile:</p>
                    <p className="text-blue-600 text-sm">✅ E-E-A-T profile configured</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Generate Button */}
      {!data.generated_article && (
        <div className="text-center">
          <button
            onClick={generateArticle}
            disabled={isGenerating || !(data.selectedTopics && data.selectedTopics.length > 0)}
            className="inline-flex items-center px-6 py-3 bg-green-600 text-white text-lg font-medium rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          >
            <SparklesIcon className="w-6 h-6 mr-2" />
            {isGenerating ? 'Generating Article...' : 'Generate Article'}
          </button>
          {!(data.selectedTopics && data.selectedTopics.length > 0) && (
            <p className="text-sm text-red-600 mt-2">
              Please complete Step 1 (select at least one topic) before generating.
            </p>
          )}
        </div>
      )}

      {/* Enhanced Article Editor and Preview */}
      {(data.generated_article || isGenerating) && (
        <div className={`${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''}`}>
          {/* Enhanced Header with Controls */}
          <div className="flex justify-between items-center mb-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
            <div className="flex items-center space-x-4">
              <h4 className="text-lg font-medium text-gray-900">Article Editor & Preview</h4>
              {data.generated_article && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <span>{getStats().words} words</span>
                  <span>•</span>
                  <span>{getStats().characters} characters</span>
                  <span>•</span>
                  <span>{getStats().paragraphs} paragraphs</span>
                </div>
              )}

              {/* Save Status Indicator */}
              {data.generated_article && (
                <div className="flex items-center space-x-2">
                  {saveStatus === 'saving' && (
                    <div className="flex items-center space-x-1 text-xs text-blue-600">
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                      <span>Saving...</span>
                    </div>
                  )}
                  {saveStatus === 'saved' && !hasUnsavedChanges && (
                    <div className="flex items-center space-x-1 text-xs text-green-600">
                      <CheckCircleIcon className="w-3 h-3" />
                      <span>Saved</span>
                      {lastSaved && (
                        <span className="text-gray-500">
                          {lastSaved.toLocaleTimeString()}
                        </span>
                      )}
                    </div>
                  )}
                  {saveStatus === 'unsaved' && hasUnsavedChanges && (
                    <div className="flex items-center space-x-1 text-xs text-orange-600">
                      <ExclamationTriangleIcon className="w-3 h-3" />
                      <span>Unsaved changes</span>
                    </div>
                  )}
                  {saveStatus === 'error' && (
                    <div className="flex items-center space-x-1 text-xs text-red-600">
                      <ExclamationTriangleIcon className="w-3 h-3" />
                      <span>Save failed</span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {data.generated_article && (
              <div className="flex items-center space-x-2">
                {/* View Mode Toggle */}
                <div className="flex bg-white rounded-lg border border-gray-300 p-1">
                  <button
                    onClick={() => setViewMode('editor')}
                    className={`px-3 py-1 text-xs font-medium rounded ${
                      viewMode === 'editor'
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <PencilIcon className="w-4 h-4 inline mr-1" />
                    Editor
                  </button>
                  <button
                    onClick={() => setViewMode('split')}
                    className={`px-3 py-1 text-xs font-medium rounded ${
                      viewMode === 'split'
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <DocumentTextIcon className="w-4 h-4 inline mr-1" />
                    Split
                  </button>
                  <button
                    onClick={() => setViewMode('preview')}
                    className={`px-3 py-1 text-xs font-medium rounded ${
                      viewMode === 'preview'
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <EyeIcon className="w-4 h-4 inline mr-1" />
                    Preview
                  </button>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                  {/* Manual Save Button */}
                  <button
                    onClick={() => saveArticle()}
                    disabled={isSaving || (!hasUnsavedChanges && saveStatus === 'saved')}
                    className={`inline-flex items-center px-3 py-2 text-sm rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                      hasUnsavedChanges || saveStatus === 'error'
                        ? 'bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500'
                        : 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500'
                    } disabled:bg-gray-400 disabled:cursor-not-allowed`}
                    title={hasUnsavedChanges ? 'Save changes' : 'Article saved'}
                  >
                    {isSaving ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
                    ) : (
                      <CloudArrowUpIcon className="w-4 h-4 mr-1" />
                    )}
                    {isSaving ? 'Saving...' : 'Save'}
                  </button>

                  <button
                    onClick={() => setIsFullscreen(!isFullscreen)}
                    className="inline-flex items-center px-3 py-2 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                  >
                    {isFullscreen ? (
                      <ArrowsPointingInIcon className="w-4 h-4 mr-1" />
                    ) : (
                      <ArrowsPointingOutIcon className="w-4 h-4 mr-1" />
                    )}
                    {isFullscreen ? 'Exit' : 'Fullscreen'}
                  </button>
                  <button
                    onClick={copyToClipboard}
                    className="inline-flex items-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    <ClipboardDocumentIcon className="w-4 h-4 mr-1" />
                    Copy
                  </button>
                  <button
                    onClick={downloadAsFile}
                    className="inline-flex items-center px-3 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                  >
                    <ArrowDownTrayIcon className="w-4 h-4 mr-1" />
                    Download
                  </button>
                  <button
                    onClick={generateArticle}
                    disabled={isGenerating}
                    className="inline-flex items-center px-3 py-2 bg-orange-600 text-white text-sm rounded-md hover:bg-orange-700 disabled:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
                  >
                    <SparklesIcon className="w-4 h-4 mr-1" />
                    Regenerate
                  </button>
                </div>
              </div>
            )}
          </div>

          {isGenerating ? (
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-xl p-8">
              {/* Main Loading Animation */}
              <div className="text-center mb-6">
                <div className="relative mx-auto mb-4">
                  <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <SparklesIcon className="w-6 h-6 text-blue-600 animate-pulse" />
                  </div>
                </div>
                <h3 className="text-xl font-bold text-blue-800 mb-2">🤖 AI is crafting your article...</h3>
                <p className="text-blue-600 font-medium">This process typically takes 30-90 seconds</p>
              </div>

              {/* Progress Steps */}
              <div className="space-y-4 mb-6">
                <div className="bg-white/70 rounded-lg p-4 border border-blue-200">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">1</span>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-blue-800">Analyzing your selected topics</p>
                      <p className="text-sm text-blue-600">Processing {(data.selectedTopics || []).length} topic(s) and keyword research</p>
                    </div>
                    <div className="text-green-500">✓</div>
                  </div>
                </div>

                <div className="bg-white/70 rounded-lg p-4 border border-blue-200">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center animate-pulse">
                      <span className="text-white text-sm font-bold">2</span>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-blue-800">Integrating sources and references</p>
                      <p className="text-sm text-blue-600">Incorporating your provided sources for authority</p>
                    </div>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-200 border-t-blue-600"></div>
                  </div>
                </div>

                <div className="bg-white/50 rounded-lg p-4 border border-gray-200">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                      <span className="text-gray-600 text-sm font-bold">3</span>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-600">Applying style and formatting</p>
                      <p className="text-sm text-gray-500">Crafting content in {data.outputParameters?.tonality || 'informative'} tone, {data.outputParameters?.length || 'medium'} length</p>
                    </div>
                    <div className="w-4 h-4 rounded-full border-2 border-gray-300"></div>
                  </div>
                </div>

                <div className="bg-white/50 rounded-lg p-4 border border-gray-200">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                      <span className="text-gray-600 text-sm font-bold">4</span>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-600">Finalizing and optimizing</p>
                      <p className="text-sm text-gray-500">SEO optimization and final quality checks</p>
                    </div>
                    <div className="w-4 h-4 rounded-full border-2 border-gray-300"></div>
                  </div>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="bg-white/70 rounded-lg p-4 border border-blue-200">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-blue-800">Generation Progress</span>
                  <span className="text-sm text-blue-600">Processing...</span>
                </div>
                <div className="w-full bg-blue-100 rounded-full h-3">
                  <div className="bg-gradient-to-r from-blue-500 to-indigo-600 h-3 rounded-full animate-pulse" style={{ width: '45%' }}></div>
                </div>
              </div>

              {/* Tips while waiting */}
              <div className="mt-6 bg-white/70 rounded-lg p-4 border border-blue-200">
                <h4 className="font-medium text-blue-800 mb-2">💡 While you wait:</h4>
                <ul className="text-sm text-blue-600 space-y-1">
                  <li>• The AI is analyzing your topics for the best content structure</li>
                  <li>• Sources are being integrated naturally throughout the article</li>
                  <li>• SEO optimization is being applied based on your keywords</li>
                  <li>• Content is being tailored to your specified tone and length</li>
                </ul>
              </div>
            </div>
          ) : (
            <div className={`${isFullscreen ? 'h-screen p-4' : ''}`}>
              {/* SEO Metadata Display */}
              {parsedArticle && parsedArticle.metadata && (
                <div className="mb-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
                  <h5 className="font-semibold text-green-800 mb-3">📊 SEO Metadata</h5>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    {parsedArticle.metadata.seoTitle && (
                      <div>
                        <span className="font-medium text-gray-700">SEO Title:</span>
                        <p className="text-green-700 mt-1">{parsedArticle.metadata.seoTitle}</p>
                      </div>
                    )}
                    {parsedArticle.metadata.metaDescription && (
                      <div>
                        <span className="font-medium text-gray-700">Meta Description:</span>
                        <p className="text-green-700 mt-1">{parsedArticle.metadata.metaDescription}</p>
                      </div>
                    )}
                    {parsedArticle.metadata.focusKeywords && (
                      <div>
                        <span className="font-medium text-gray-700">Focus Keywords:</span>
                        <p className="text-green-700 mt-1">{parsedArticle.metadata.focusKeywords}</p>
                      </div>
                    )}
                    {parsedArticle.metadata.tags && (
                      <div>
                        <span className="font-medium text-gray-700">Tags:</span>
                        <p className="text-green-700 mt-1">{parsedArticle.metadata.tags}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Editor and Preview Layout */}
              <div className={`${viewMode === 'split' ? 'grid grid-cols-2 gap-4' : ''} ${isFullscreen ? 'h-full' : 'min-h-96'}`}>
                {/* Editor Panel */}
                {(viewMode === 'editor' || viewMode === 'split') && (
                  <div className="flex flex-col">
                    {/* Editor Toolbar */}
                    <div className="flex items-center space-x-2 p-2 bg-gray-50 border border-gray-300 rounded-t-lg">
                      <button
                        onClick={() => formatText('bold')}
                        className="px-2 py-1 text-xs font-bold bg-white border border-gray-300 rounded hover:bg-gray-100"
                        title="Bold"
                      >
                        B
                      </button>
                      <button
                        onClick={() => formatText('italic')}
                        className="px-2 py-1 text-xs italic bg-white border border-gray-300 rounded hover:bg-gray-100"
                        title="Italic"
                      >
                        I
                      </button>
                      <button
                        onClick={() => formatText('h2')}
                        className="px-2 py-1 text-xs font-semibold bg-white border border-gray-300 rounded hover:bg-gray-100"
                        title="Heading 2"
                      >
                        H2
                      </button>
                      <button
                        onClick={() => formatText('h3')}
                        className="px-2 py-1 text-xs font-medium bg-white border border-gray-300 rounded hover:bg-gray-100"
                        title="Heading 3"
                      >
                        H3
                      </button>
                      <button
                        onClick={() => formatText('list')}
                        className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-100"
                        title="Bullet List"
                      >
                        •
                      </button>
                    </div>

                    {/* Enhanced Editor */}
                    <textarea
                      ref={editorRef}
                      value={editableContent}
                      onChange={handleContentChange}
                      className={`article-editor flex-1 px-4 py-3 border-l border-r border-b border-gray-300 rounded-b-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm resize-none view-transition ${
                        isFullscreen ? 'h-full' : 'h-96'
                      }`}
                      placeholder="Your generated article will appear here..."
                    />

                    {/* Editor Stats */}
                    <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
                      <div className="flex space-x-4">
                        <span>{getStats().words} words</span>
                        <span>{getStats().characters} chars</span>
                        <span>{getStats().paragraphs} paragraphs</span>
                      </div>
                      <span>Markdown supported</span>
                    </div>
                  </div>
                )}

                {/* Preview Panel */}
                {(viewMode === 'preview' || viewMode === 'split') && (
                  <div className="flex flex-col">
                    <div className="p-2 bg-gray-50 border border-gray-300 rounded-t-lg">
                      <h5 className="text-sm font-medium text-gray-700">Live Preview</h5>
                    </div>
                    <div
                      ref={previewRef}
                      className={`article-preview flex-1 p-6 bg-white border-l border-r border-b border-gray-300 rounded-b-lg overflow-y-auto view-transition ${
                        isFullscreen ? 'h-full' : 'h-96'
                      }`}
                    >
                      {parsedArticle && parsedArticle.body ? (
                        <div
                          className="prose prose-sm max-w-none"
                          dangerouslySetInnerHTML={{
                            __html: markdownToHtml(parsedArticle.body)
                          }}
                        />
                      ) : (
                        <div
                          className="prose prose-sm max-w-none"
                          dangerouslySetInnerHTML={{
                            __html: markdownToHtml(editableContent)
                          }}
                        />
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Success Message */}
      {data.generated_article && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="text-md font-medium text-green-800 mb-2">Article Generated Successfully!</h4>
          <p className="text-sm text-green-700 mb-2">
            Your article has been generated and is ready for use. You can copy it, download it, or continue editing it above.
            Feel free to regenerate if you want a different version.
          </p>
          <p className="text-xs text-green-600">
            💡 <strong>Auto-save enabled:</strong> Your changes are automatically saved every 2 seconds.
            You can also save manually with <kbd className="px-1 py-0.5 bg-green-100 rounded text-xs">Ctrl+S</kbd> or the Save button.
          </p>
        </div>
      )}
    </div>
  );
};

export default Step5Generation;
