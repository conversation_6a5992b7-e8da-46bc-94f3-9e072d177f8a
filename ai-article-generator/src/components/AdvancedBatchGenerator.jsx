import React, { useState } from 'react';
import { 
  SparklesIcon,
  DocumentDuplicateIcon, 
  PlayIcon, 
  ClockIcon, 
  CheckCircleIcon,
  XCircleIcon,
  ArrowDownTrayIcon,
  Cog6ToothIcon,
  LightBulbIcon,
  TagIcon,
  DocumentTextIcon,
  LinkIcon,
  UserIcon,
  ChatBubbleBottomCenterTextIcon,
  PlusIcon,
  BuildingStorefrontIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline';
import { apiCall, API_CONFIG, presetService } from '../config/api';
import { useAuth } from '../contexts/AuthContext';
import ResourceForm from './management/ResourceForm';
import AuthorForm from './management/AuthorForm';
import ProductForm from './management/ProductForm';
import ResourceSelectionModal from './ResourceSelectionModal';

const AdvancedBatchGenerator = () => {
  const { authenticatedFetch } = useAuth();
  const [inputMode, setInputMode] = useState('KEYWORDS');
  const [content, setContent] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [results, setResults] = useState(null);
  const [showSettings, setShowSettings] = useState(true);
  
  // Management states
  const [selectedResources, setSelectedResources] = useState([]);
  const [selectedAuthor, setSelectedAuthor] = useState(null);
  const [selectedProduct, setSelectedProduct] = useState(null);
  
  // Data states for selection
  const [resources, setResources] = useState([]);
  const [authors, setAuthors] = useState([]);
  const [products, setProducts] = useState([]);
  const [loadingData, setLoadingData] = useState(false);
  
  // Form states for creating new items
  const [showResourceForm, setShowResourceForm] = useState(false);
  const [showAuthorForm, setShowAuthorForm] = useState(false);
  const [showProductForm, setShowProductForm] = useState(false);
  
  // Resource selection modal
  const [showResourceSelection, setShowResourceSelection] = useState(false);
  
  // Example collapse state
  const [showExample, setShowExample] = useState(false);
  const [globalSettings, setGlobalSettings] = useState({
    tonality: 'informative',
    length: 'medium_article',
    format: 'markdown',
    targetCount: 10
  });

  const inputModeOptions = [
    { 
      value: 'KEYWORDS', 
      label: 'Keywords', 
      icon: TagIcon,
      description: 'Provide a list of keywords or key phrases, one per line. AI will create articles targeting these terms.',
      example: 'sustainable living tips\nzero waste lifestyle\nrenewable energy homes\neco-friendly products\ngreen transportation\nsustainable fashion\norganic gardening\ncarbon footprint reduction\nenvironmentally friendly cleaning\nsustainable food choices',
      defaultContent: ''
    },
    { 
      value: 'TITLES', 
      label: 'Article Titles', 
      icon: DocumentTextIcon,
      description: 'Provide specific article titles, one per line. AI will create full articles based on these exact titles.',
      example: '10 Simple Zero Waste Swaps That Will Transform Your Home\nThe Complete Guide to Installing Solar Panels: Costs, Benefits & Process\nSustainable Fashion on a Budget: Building an Eco-Friendly Wardrobe\nGreen Transportation: Comparing Electric Cars, Bikes, and Public Transit\nFrom Garden to Table: Growing Your Own Organic Vegetables',
      defaultContent: ''
    },
    { 
      value: 'IDEAS', 
      label: 'Ideas/Goals', 
      icon: LightBulbIcon,
      description: 'Describe your content ideas and goals. AI will generate related articles based on your vision.',
      example: 'I want to create comprehensive content about sustainable living and eco-friendly lifestyle choices. My focus areas include zero-waste practices, renewable energy solutions for homes, sustainable fashion alternatives, green transportation options, and environmentally conscious consumer choices. The content should help readers transition to more sustainable habits while saving money and reducing their environmental impact.',
      defaultContent: ''
    }
  ];

  const tonalityOptions = [
    { 
      value: 'informative', 
      label: 'Informative', 
      description: 'Educational and fact-based' 
    },
    { 
      value: 'persuasive', 
      label: 'Persuasive', 
      description: 'Convincing and compelling' 
    },
    { 
      value: 'casual', 
      label: 'Casual', 
      description: 'Friendly and conversational' 
    },
    { 
      value: 'formal', 
      label: 'Formal', 
      description: 'Professional and structured' 
    },
    { 
      value: 'technical', 
      label: 'Technical', 
      description: 'Detailed and expert-level' 
    }
  ];

  const lengthOptions = [
    {
      value: 'snippet',
      label: 'Snippet (~500 words)',
      description: 'Very short, focused piece'
    },
    {
      value: 'short_post',
      label: 'Short Post (~800 words)',
      description: 'Concise content, good for specific concepts'
    },
    {
      value: 'medium_article',
      label: 'Medium Article (~1500 words)',
      description: 'Balanced depth and readability'
    },
    {
      value: 'long_guide',
      label: 'Long-Form Guide (~2500 words)',
      description: 'Comprehensive and detailed coverage'
    },
    {
      value: 'pillar_module',
      label: 'Pillar Page Module (~700 words)',
      description: 'Focused chapter for larger pillar pages'
    }
  ];

  const formatOptions = [
    { 
      value: 'markdown', 
      label: 'Markdown', 
      description: 'With headings and formatting' 
    },
    { 
      value: 'plain', 
      label: 'Plain Text', 
      description: 'Simple text without formatting' 
    }
  ];

  const currentMode = inputModeOptions.find(mode => mode.value === inputMode);

  // Load data on component mount
  React.useEffect(() => {
    loadManagementData();
  }, []);

  const loadManagementData = async () => {
    setLoadingData(true);
    try {
      // Load resources
      const resourcesResponse = await authenticatedFetch(API_CONFIG.ENDPOINTS.RESOURCES.LIST);
      if (resourcesResponse.ok) {
        const resourcesData = await resourcesResponse.json();
        setResources(resourcesData.resources || []);
      }

      // Load authors and products
      const authorsData = await presetService.getPresets('author');
      const productsData = await presetService.getPresets('product');
      setAuthors(authorsData);
      setProducts(productsData);
    } catch (error) {
      console.error('Failed to load management data:', error);
    } finally {
      setLoadingData(false);
    }
  };

  const handleModeChange = (newMode) => {
    setInputMode(newMode);
    const modeConfig = inputModeOptions.find(mode => mode.value === newMode);
    setContent(modeConfig.defaultContent);
    setShowExample(false); // 重置 example 折叠状态
  };

  const handleSettingsChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setGlobalSettings(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setGlobalSettings(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Extract keywords from content for resource recommendations
  const getCurrentKeywords = () => {
    if (!content.trim()) return [];
    
    const words = content
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3)
      .filter(word => !['this', 'that', 'with', 'from', 'they', 'have', 'will', 'been', 'were', 'said', 'each', 'which', 'their', 'what', 'about', 'would', 'there', 'could', 'other'].includes(word));
    
    // Get most frequent words
    const wordCount = {};
    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });
    
    return Object.entries(wordCount)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word);
  };

  // Calculate actual article count to be generated (matches backend logic)
  const getActualArticleCount = () => {
    const inputLines = content.split('\n').filter(line => line.trim()).length;

    if (inputMode === 'TITLES') {
      // TITLES mode: 1 article per title, no additional limiting
      return inputLines;
    } else if (inputMode === 'KEYWORDS') {
      // KEYWORDS mode: limit to targetCount or input count, whichever is smaller
      return inputLines > 0 ? Math.min(inputLines, globalSettings.targetCount) : 0;
    } else {
      // IDEAS mode: generate up to targetCount keywords, but max 5
      return Math.min(globalSettings.targetCount, 5);
    }
  };


  const handleGeneration = async () => {
    if (!content.trim()) {
      alert('Please enter content based on the selected mode');
      return;
    }

    setIsGenerating(true);
    setResults(null);

    try {
      console.log('Starting advanced batch generation:', {
        inputMode,
        content: content.substring(0, 100) + '...',
        globalSettings
      });

      // Process selected resources
      const sources = [];
      const redditPosts = [];
      selectedResources.forEach(resource => {
        if (resource.resource_type === 'url') {
          sources.push(resource.resource_url);
        } else if (resource.resource_type === 'reddit') {
          redditPosts.push(resource.resource_url);
        }
      });

      const requestData = {
        inputMode,
        content,
        globalSettings: {
          ...globalSettings,
          targetCount: getActualArticleCount(), // Send actual count based on mode
          // Author information from selected author
          authorName: selectedAuthor?.presetData?.authorName || '',
          authorBio: selectedAuthor?.presetData?.authorBio || '',
          targetAudience: selectedAuthor?.presetData?.targetAudience || '',
          // Product information from selected product
          productInfo: selectedProduct?.presetData || {
            name: '',
            link: '',
            description: '',
            features: []
          },
          // Resources from management system
          sources: sources,
          redditPosts: redditPosts
        }
      };

      const response = await apiCall(API_CONFIG.ENDPOINTS.TASKS_ADVANCED_BATCH_GENERATE, {
        method: 'POST',
        body: JSON.stringify(requestData)
      });

      console.log('Advanced batch generation response:', response);

      // Validate response structure
      if (!response || typeof response !== 'object') {
        throw new Error('Invalid response format from server');
      }

      // Ensure results and errors arrays exist
      const processedResponse = {
        ...response,
        results: response.results || [],
        errors: response.errors || [],
        statistics: response.statistics || {
          totalRequested: 0,
          totalSuccess: 0,
          totalErrors: 0,
          averageWordCount: 0,
          averageQualityScore: 0
        }
      };

      setResults(processedResponse);

      // Show summary notification
      const { totalSuccess = 0, totalErrors = 0 } = processedResponse.statistics || {};
      if (totalSuccess > 0 || totalErrors > 0) {
        const message = totalErrors === 0
          ? `✅ Successfully generated ${totalSuccess} articles!`
          : `⚠️ Generated ${totalSuccess} articles, ${totalErrors} failed. Check results below.`;

        // Use a more user-friendly notification instead of alert
        console.log(message);
      }
    } catch (error) {
      console.error('Advanced batch generation error:', error);

      // More detailed error handling
      let errorMessage = 'Failed to generate articles';
      if (error.message) {
        if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'Network error: Please check your connection and try again';
        } else if (error.message.includes('timeout')) {
          errorMessage = 'Request timeout: The generation is taking too long, please try with fewer articles';
        } else {
          errorMessage = `Generation failed: ${error.message}`;
        }
      }

      alert(errorMessage);

      // Set error state for UI feedback
      setResults({
        error: true,
        message: errorMessage,
        results: [],
        errors: [],
        statistics: { totalRequested: 0, totalSuccess: 0, totalErrors: 0 }
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadResults = () => {
    if (!results || !results.results) return;

    const content = results.results.map(result => {
      return `# ${result.title}\n\nKeyword: ${result.keyword}\nQuality Score: ${result.qualityMetrics?.overallScore || 'N/A'}\nWord Count: ${result.wordCount}\n\n${result.article}\n\n${'='.repeat(80)}\n\n`;
    }).join('');

    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `advanced-batch-articles-${new Date().toISOString().split('T')[0]}.md`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Handle saving new items
  const handleSaveResource = async (resourceData) => {
    try {
      const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.RESOURCES.CREATE, {
        method: 'POST',
        body: JSON.stringify(resourceData)
      });
      
      if (response.ok) {
        const responseData = await response.json();
        const newResource = responseData.resource;
        
        await loadManagementData(); // Reload data
        
        // 自动将新资源添加到选中列表
        if (newResource) {
          console.log('🔍 新创建的资源:', newResource);
          console.log('🔍 当前selectedResources:', selectedResources);
          
          setSelectedResources(prev => {
            console.log('🔍 setSelectedResources prev:', prev);
            // 检查是否已经存在，避免重复添加
            const exists = prev.some(r => r.id === newResource.id);
            console.log('🔍 资源是否已存在:', exists, 'newResource.id:', newResource.id);
            
            if (!exists) {
              const newSelection = [...prev, newResource];
              console.log('🎯 自动选中新添加的资源:', newResource.resource_name);
              console.log('🎯 新的selectedResources:', newSelection);
              return newSelection;
            }
            return prev;
          });
        } else {
          console.log('❌ 未获取到新创建的资源数据');
        }
        
        setShowResourceForm(false);
        // 添加成功提示
        alert('✅ 资源添加成功！URL内容已提取并保存到您的资源库中，已自动添加到选中列表。');
      } else {
        throw new Error('Failed to create resource');
      }
    } catch (error) {
      console.error('Failed to save resource:', error);
      alert('❌ 资源保存失败：' + error.message);
      throw error;
    }
  };

  const handleSaveAuthor = async (authorData) => {
    try {
      await presetService.createPreset('author', authorData.presetName, authorData.presetData);
      await loadManagementData(); // Reload data
      setShowAuthorForm(false);
    } catch (error) {
      console.error('Failed to save author:', error);
      throw error;
    }
  };

  const handleSaveProduct = async (productData) => {
    try {
      await presetService.createPreset('product', productData.presetName, productData.presetData);
      await loadManagementData(); // Reload data
      setShowProductForm(false);
    } catch (error) {
      console.error('Failed to save product:', error);
      throw error;
    }
  };

  // Initialize with default content when component mounts
  React.useEffect(() => {
    if (!content) {
      setContent(currentMode.defaultContent);
    }
  }, []);

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg">
        {/* Header */}
        <div className="border-b border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <SparklesIcon className="h-8 w-8 text-purple-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Advanced Batch Generator</h1>
                <p className="text-gray-600 mt-1">AI-powered batch article generation with intelligent research and optimization</p>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Input Mode Selection */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Input Mode</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {inputModeOptions.map((mode) => (
                <div
                  key={mode.value}
                  className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                    inputMode === mode.value
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleModeChange(mode.value)}
                >
                  <div className="flex items-center space-x-3 mb-2">
                    <mode.icon className={`h-6 w-6 ${inputMode === mode.value ? 'text-purple-600' : 'text-gray-500'}`} />
                    <h4 className={`font-semibold ${inputMode === mode.value ? 'text-purple-900' : 'text-gray-900'}`}>
                      {mode.label}
                    </h4>
                  </div>
                  <p className="text-sm text-gray-600">{mode.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Content Input */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-3">
              <label className="block text-lg font-semibold text-gray-900">
                {currentMode.label} Content
              </label>
              <div className="text-sm text-gray-500">
                Mode: {currentMode.label}
              </div>
            </div>
            
            {/* Description */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
              <p className="text-sm text-blue-800 flex items-center mb-2">
                <currentMode.icon className="h-4 w-4 mr-2" />
                {currentMode.description}
              </p>
              <div className="text-xs text-blue-700 bg-blue-100 rounded p-2">
                <strong>Generation Logic:</strong> {
                  inputMode === 'KEYWORDS' 
                    ? 'Each keyword becomes 1 article (max ' + globalSettings.targetCount + ' articles)'
                    : inputMode === 'TITLES' 
                      ? 'Each title becomes 1 article (exact count)'
                      : 'AI creates ' + Math.min(globalSettings.targetCount, 5) + ' related articles from your description (max 5)'
                }
              </div>
            </div>

            {/* Example */}
            <div className="mb-3">
              <button
                onClick={() => setShowExample(!showExample)}
                className="flex items-center space-x-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
              >
                <span>Example</span>
                {showExample ? (
                  <ChevronUpIcon className="h-4 w-4" />
                ) : (
                  <ChevronDownIcon className="h-4 w-4" />
                )}
              </button>
              {showExample && (
                <div className="mt-2 bg-gray-50 border border-gray-200 rounded-lg p-3">
                  <pre className="text-sm text-gray-600 whitespace-pre-wrap font-normal leading-relaxed">
                    {currentMode.example}
                  </pre>
                </div>
              )}
            </div>

            {/* Input Textarea */}
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              rows={inputMode === 'IDEAS' ? 6 : 12}
              className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent font-mono text-sm"
            />
            <div className="flex justify-between items-center mt-2 text-sm text-gray-600">
              <span>
                {inputMode === 'IDEAS' 
                  ? `Content length: ${content.length} characters`
                  : `${content.split('\n').filter(line => line.trim()).length} items entered`
                }
              </span>
              <span>
                {(() => {
                  const actualCount = getActualArticleCount();
                  const inputLines = content.split('\n').filter(line => line.trim()).length;

                  if (inputMode === 'TITLES') {
                    return `Will generate: ${actualCount} articles (1 per title)`;
                  } else if (inputMode === 'KEYWORDS') {
                    if (inputLines === 0) {
                      return `Will generate: 0 articles (enter keywords above)`;
                    } else if (inputLines > globalSettings.targetCount) {
                      return `Will generate: ${actualCount} articles (limited from ${inputLines} keywords)`;
                    } else {
                      return `Will generate: ${actualCount} articles`;
                    }
                  } else {
                    return `Will generate: ${actualCount} articles (max 5 for IDEAS mode)`;
                  }
                })()}
              </span>
            </div>
          </div>

          {/* Resource Selection */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <LinkIcon className="h-5 w-5 mr-2 text-blue-600" />
                Resources ({selectedResources.length} selected)
              </h3>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowResourceSelection(true)}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <LinkIcon className="h-4 w-4" />
                  <span>Select Resources</span>
                </button>
                <button
                  onClick={() => setShowResourceForm(true)}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <PlusIcon className="h-4 w-4" />
                  <span>Add New</span>
                </button>
              </div>
            </div>
            
            {/* Selected Resources Preview */}
            {console.log('🎨 渲染时的selectedResources:', selectedResources, 'length:', selectedResources.length)}
            {selectedResources.length > 0 ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {selectedResources.slice(0, 6).map((resource) => (
                    <div
                      key={resource.id}
                      className="bg-white border border-blue-200 rounded-lg p-3 flex items-center justify-between"
                    >
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 truncate text-sm">
                          {resource.resource_name}
                        </h4>
                        <p className="text-xs text-gray-500">
                          {resource.resource_type}
                        </p>
                      </div>
                      <button
                        onClick={() => {
                          setSelectedResources(selectedResources.filter(r => r.id !== resource.id));
                        }}
                        className="ml-2 p-1 text-gray-400 hover:text-red-500 transition-colors"
                      >
                        <XCircleIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                  {selectedResources.length > 6 && (
                    <div className="bg-gray-100 border border-gray-200 rounded-lg p-3 flex items-center justify-center">
                      <span className="text-sm text-gray-600">
                        +{selectedResources.length - 6} more
                      </span>
                    </div>
                  )}
                </div>
                <button
                  onClick={() => setShowResourceSelection(true)}
                  className="mt-3 text-sm text-blue-600 hover:text-blue-700 font-medium"
                >
                  Manage selection
                </button>
              </div>
            ) : (
              <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                <LinkIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No resources selected</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Select resources to enhance your article generation
                </p>
                <button
                  onClick={() => setShowResourceSelection(true)}
                  className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <LinkIcon className="h-4 w-4 mr-2" />
                  Select Resources
                </button>
              </div>
            )}
          </div>

          {/* Author Selection */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <UserIcon className="h-5 w-5 mr-2 text-green-600" />
                Select Author Profile {selectedAuthor ? '(1 selected)' : '(optional)'}
              </h3>
              <button
                onClick={() => setShowAuthorForm(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <PlusIcon className="h-4 w-4" />
                <span>Add New Author</span>
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {authors.map((author) => (
                <div
                  key={author.id}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    selectedAuthor?.id === author.id
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-green-300 bg-white'
                  }`}
                  onClick={() => {
                    setSelectedAuthor(selectedAuthor?.id === author.id ? null : author);
                  }}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">{author.presetName}</h4>
                      <p className="text-sm text-gray-600 mb-2">{author.presetData?.authorName}</p>
                      <p className="text-xs text-gray-500">{author.presetData?.authorBio}</p>
                    </div>
                    {selectedAuthor?.id === author.id && (
                      <CheckCircleIcon className="h-5 w-5 text-green-600 flex-shrink-0 ml-2" />
                    )}
                  </div>
                </div>
              ))}
              {authors.length === 0 && (
                <div className="col-span-full text-center py-8">
                  <UserIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No authors available</h3>
                  <p className="mt-1 text-sm text-gray-500">Create some author profiles first in the Manage section.</p>
                </div>
              )}
            </div>
          </div>

          {/* Product Selection */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <BuildingStorefrontIcon className="h-5 w-5 mr-2 text-purple-600" />
                Select Product Info {selectedProduct ? '(1 selected)' : '(optional)'}
              </h3>
              <button
                onClick={() => setShowProductForm(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                <PlusIcon className="h-4 w-4" />
                <span>Add New Product</span>
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {products.map((product) => (
                <div
                  key={product.id}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    selectedProduct?.id === product.id
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-purple-300 bg-white'
                  }`}
                  onClick={() => {
                    setSelectedProduct(selectedProduct?.id === product.id ? null : product);
                  }}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">{product.presetName}</h4>
                      <p className="text-sm text-gray-600 mb-2">{product.presetData?.name}</p>
                      <p className="text-xs text-gray-500">{product.presetData?.description}</p>
                    </div>
                    {selectedProduct?.id === product.id && (
                      <CheckCircleIcon className="h-5 w-5 text-purple-600 flex-shrink-0 ml-2" />
                    )}
                  </div>
                </div>
              ))}
              {products.length === 0 && (
                <div className="col-span-full text-center py-8">
                  <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No products available</h3>
                  <p className="mt-1 text-sm text-gray-500">Create some product profiles first in the Manage section.</p>
                </div>
              )}
            </div>
          </div>

          {/* Generation Settings */}
          <div className="mb-8 p-6 bg-gray-50 rounded-lg border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Generation Settings</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Tonality */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tonality
                </label>
                <select
                  value={globalSettings.tonality}
                  onChange={(e) => handleSettingsChange('tonality', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  {tonalityOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  {tonalityOptions.find(opt => opt.value === globalSettings.tonality)?.description}
                </p>
              </div>

              {/* Article Length */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Article Length
                </label>
                <select
                  value={globalSettings.length}
                  onChange={(e) => handleSettingsChange('length', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  {lengthOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  {lengthOptions.find(opt => opt.value === globalSettings.length)?.description}
                </p>
              </div>

              {/* Output Format */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Output Format
                </label>
                <select
                  value={globalSettings.format}
                  onChange={(e) => handleSettingsChange('format', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  {formatOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  {formatOptions.find(opt => opt.value === globalSettings.format)?.description}
                </p>
              </div>

            </div>

          </div>


          {/* Generate Button */}
          <div className="flex justify-center mb-8">
            <button
              onClick={handleGeneration}
              disabled={isGenerating || !content.trim()}
              className="flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-all duration-200 text-lg font-semibold shadow-lg"
            >
              {isGenerating ? (
                <>
                  <ClockIcon className="h-6 w-6 animate-spin" />
                  <span>Generating Articles...</span>
                </>
              ) : (
                <>
                  <SparklesIcon className="h-6 w-6" />
                  <span>Generate Articles ({getActualArticleCount()})</span>
                </>
              )}
            </button>
          </div>

          {/* Results Section */}
          {results && (
            <div className="border-t border-gray-200 pt-8">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">Generation Results</h3>
                {results.results && results.results.length > 0 && (
                  <button
                    onClick={downloadResults}
                    className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <ArrowDownTrayIcon className="h-5 w-5" />
                    <span>Download All Articles</span>
                  </button>
                )}
              </div>

              {/* Error State */}
              {results.error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
                  <div className="flex items-center mb-3">
                    <XCircleIcon className="h-6 w-6 text-red-600 mr-2" />
                    <h4 className="text-lg font-semibold text-red-800">Generation Failed</h4>
                  </div>
                  <p className="text-red-700">{results.message}</p>
                  <div className="mt-4">
                    <button
                      onClick={() => setResults(null)}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              )}

              {/* Generation Summary */}
              <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-6 mb-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Generation Summary</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{results.statistics?.totalRequested || 0}</div>
                    <div className="text-sm text-gray-600">Requested</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{results.statistics?.totalSuccess || 0}</div>
                    <div className="text-sm text-gray-600">Generated</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{results.statistics?.averageWordCount || 0}</div>
                    <div className="text-sm text-gray-600">Avg Words</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-indigo-600">{results.statistics?.averageQualityScore || 0}</div>
                    <div className="text-sm text-gray-600">Avg Quality</div>
                  </div>
                </div>
                
                {results.inputMode && (
                  <div className="mt-4 text-sm text-gray-600">
                    <span className="font-medium">Input Mode:</span> {results.inputMode} | 
                    <span className="font-medium ml-2">Keywords Generated:</span> {results.generatedKeywords?.join(', ') || 'N/A'}
                  </div>
                )}
              </div>

              {/* Success Results */}
              {results.results && results.results.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-green-700 mb-3 flex items-center">
                    <CheckCircleIcon className="h-5 w-5 mr-2" />
                    Successfully Generated Articles ({results.results.length})
                  </h4>
                  <div className="space-y-4">
                    {results.results.map((result, index) => (
                      <div key={index} className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h5 className="font-semibold text-gray-900 mb-1">{result.title}</h5>
                            <p className="text-sm text-gray-600 mb-2">Keyword: <span className="font-medium">{result.keyword}</span></p>
                            
                            {result.qualityMetrics && (
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
                                <div>
                                  <span className="text-gray-500">Word Count:</span>
                                  <span className="ml-1 font-medium">{result.wordCount}</span>
                                </div>
                                <div>
                                  <span className="text-gray-500">Quality:</span>
                                  <span className="ml-1 font-medium">{result.qualityMetrics.overallScore}%</span>
                                </div>
                                <div>
                                  <span className="text-gray-500">SEO:</span>
                                  <span className="ml-1 font-medium">{result.qualityMetrics.seoScore}%</span>
                                </div>
                                <div>
                                  <span className="text-gray-500">Readability:</span>
                                  <span className="ml-1 font-medium">{result.qualityMetrics.readabilityScore}%</span>
                                </div>
                              </div>
                            )}
                          </div>
                          <div className="text-sm text-green-600 ml-4">
                            ✓ Ready
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Error Results */}
              {results.errors && results.errors.length > 0 && !results.error && (
                <div>
                  <h4 className="text-lg font-semibold text-red-700 mb-3 flex items-center">
                    <XCircleIcon className="h-5 w-5 mr-2" />
                    Failed Articles ({results.errors.length})
                  </h4>
                  <div className="space-y-3">
                    {results.errors.map((error, index) => (
                      <div key={index} className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h5 className="font-semibold text-gray-900 mb-1">
                              {error.title || `Article ${error.index + 1}` || 'Unknown Article'}
                            </h5>
                            <p className="text-sm text-gray-600 mb-2">
                              Keyword: <span className="font-medium">{error.keyword || 'Unknown'}</span>
                            </p>
                            <p className="text-sm text-red-600">{error.error || 'Unknown error occurred'}</p>
                          </div>
                          <div className="text-sm text-red-600 ml-4 flex-shrink-0">
                            ✗ Failed
                          </div>
                        </div>

                        {/* Retry button for individual failed articles */}
                        <div className="mt-3 pt-3 border-t border-red-200">
                          <button
                            onClick={() => {
                              // Add the failed keyword back to content for retry
                              if (error.keyword && inputMode === 'KEYWORDS') {
                                const currentLines = content.split('\n').filter(line => line.trim());
                                if (!currentLines.includes(error.keyword)) {
                                  setContent(prev => prev + '\n' + error.keyword);
                                }
                              }
                            }}
                            className="text-xs px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
                          >
                            Add to retry list
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Resource Form Modal */}
      {showResourceForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/5 xl:w-1/2 shadow-lg rounded-md bg-white max-h-[90vh] overflow-y-auto">
            <ResourceForm
              onCancel={() => setShowResourceForm(false)}
              onSave={handleSaveResource}
            />
          </div>
        </div>
      )}

      {/* Author Form Modal */}
      {showAuthorForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/5 xl:w-1/2 shadow-lg rounded-md bg-white max-h-[90vh] overflow-y-auto">
            <AuthorForm
              onCancel={() => setShowAuthorForm(false)}
              onSave={handleSaveAuthor}
            />
          </div>
        </div>
      )}

      {/* Product Form Modal */}
      {showProductForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/5 xl:w-1/2 shadow-lg rounded-md bg-white max-h-[90vh] overflow-y-auto">
            <ProductForm
              onCancel={() => setShowProductForm(false)}
              onSave={handleSaveProduct}
            />
          </div>
        </div>
      )}

      {/* Resource Selection Modal */}
      <ResourceSelectionModal
        isOpen={showResourceSelection}
        onClose={() => setShowResourceSelection(false)}
        resources={resources}
        selectedResources={selectedResources}
        onSelectionChange={setSelectedResources}
        loading={loadingData}
        currentKeywords={getCurrentKeywords()}
      />
    </div>
  );
};

export default AdvancedBatchGenerator;