import React, { useState, useEffect } from 'react';
import { 
  XMarkIcon, 
  LinkIcon, 
  DocumentTextIcon,
  GlobeAltIcon,
  CheckCircleIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  SparklesIcon,
  ClockIcon,
  EyeIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/react/24/outline';
import { CheckCircleIcon as CheckCircleIconSolid } from '@heroicons/react/24/solid';

const ResourceSelectionModal = ({ 
  isOpen, 
  onClose, 
  resources, 
  selectedResources, 
  onSelectionChange, 
  loading = false,
  currentKeywords = [] // 用于推荐算法
}) => {
  const [activeTab, setActiveTab] = useState('recommended');
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [sortBy, setSortBy] = useState('relevance');
  const [currentPage, setCurrentPage] = useState(1);
  
  const ITEMS_PER_PAGE = 6;

  // 获取资源类型图标
  const getResourceTypeIcon = (type) => {
    switch (type) {
      case 'url':
        return <LinkIcon className="h-4 w-4" />;
      case 'reddit':
        return <GlobeAltIcon className="h-4 w-4" />;
      default:
        return <DocumentTextIcon className="h-4 w-4" />;
    }
  };

  // 推荐算法：基于关键词和使用频率
  const getRecommendedResources = () => {
    if (!currentKeywords.length) {
      // 如果没有关键词，返回空数组，因为无法进行个性化推荐
      return [];
    }

    return resources
      .map(resource => {
        let score = 0;
        const keywords = currentKeywords.map(k => k.toLowerCase());
        
        // 检查标题匹配
        const titleLower = resource.resource_name.toLowerCase();
        keywords.forEach(keyword => {
          if (titleLower.includes(keyword)) score += 10;
        });

        // 检查描述匹配
        const descriptionLower = (resource.description || '').toLowerCase();
        keywords.forEach(keyword => {
          if (descriptionLower.includes(keyword)) score += 5;
        });

        // 检查标签匹配
        if (resource.tags) {
          const tagsLower = resource.tags.map(tag => tag.toLowerCase());
          keywords.forEach(keyword => {
            tagsLower.forEach(tag => {
              if (tag.includes(keyword)) score += 8;
            });
          });
        }

        // 使用频率加权
        score += (resource.usage_count || 0) * 0.5;

        // 最近使用加权
        if (resource.last_used_at) {
          const daysSinceLastUse = (Date.now() - new Date(resource.last_used_at)) / (1000 * 60 * 60 * 24);
          if (daysSinceLastUse < 7) score += 3;
          else if (daysSinceLastUse < 30) score += 1;
        }

        return { ...resource, score };
      })
      .filter(resource => resource.score > 0)
      .sort((a, b) => b.score - a.score)
      .slice(0, 12);
  };

  // 过滤和排序所有资源
  const getFilteredResources = () => {
    let filtered = resources;

    // 搜索过滤
    if (searchTerm) {
      filtered = filtered.filter(resource => 
        resource.resource_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (resource.description || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (resource.tags || []).some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // 类型过滤
    if (typeFilter) {
      filtered = filtered.filter(resource => resource.resource_type === typeFilter);
    }

    // 排序
    switch (sortBy) {
      case 'name':
        filtered.sort((a, b) => a.resource_name.localeCompare(b.resource_name));
        break;
      case 'type':
        filtered.sort((a, b) => a.resource_type.localeCompare(b.resource_type));
        break;
      case 'usage':
        filtered.sort((a, b) => (b.usage_count || 0) - (a.usage_count || 0));
        break;
      case 'recent':
        filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        break;
      default: // relevance
        if (activeTab === 'recommended') {
          // 在推荐标签中，保持推荐排序
          return getRecommendedResources();
        }
        filtered.sort((a, b) => (b.usage_count || 0) - (a.usage_count || 0));
    }

    return filtered;
  };

  // 获取当前显示的资源
  const getCurrentResources = () => {
    if (activeTab === 'recommended') {
      return getRecommendedResources();
    }
    
    const filtered = getFilteredResources();
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    return filtered.slice(startIndex, startIndex + ITEMS_PER_PAGE);
  };

  // 获取总页数
  const getTotalPages = () => {
    if (activeTab === 'recommended') {
      return 1; // 推荐页面不分页
    }
    return Math.ceil(getFilteredResources().length / ITEMS_PER_PAGE);
  };

  // 处理资源选择
  const handleResourceToggle = (resource) => {
    const isSelected = selectedResources.some(r => r.id === resource.id);
    let newSelection;
    
    if (isSelected) {
      newSelection = selectedResources.filter(r => r.id !== resource.id);
    } else {
      newSelection = [...selectedResources, resource];
    }
    
    onSelectionChange(newSelection);
  };

  // 获取资源类型选项
  const getResourceTypes = () => {
    const types = [...new Set(resources.map(r => r.resource_type))];
    return types.map(type => ({
      value: type,
      label: type.charAt(0).toUpperCase() + type.slice(1)
    }));
  };

  // 重置分页当切换标签或过滤器时
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTab, searchTerm, typeFilter, sortBy]);

  if (!isOpen) return null;

  const currentResources = getCurrentResources();
  const totalPages = getTotalPages();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Select Resources</h2>
            <p className="text-sm text-gray-500 mt-1">
              {selectedResources.length} resource{selectedResources.length !== 1 ? 's' : ''} selected
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="px-6 flex space-x-8">
            <button
              onClick={() => setActiveTab('recommended')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'recommended'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-2">
                <SparklesIcon className="h-4 w-4" />
                <span>Recommended</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('all')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'all'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-2">
                <LinkIcon className="h-4 w-4" />
                <span>All Resources ({resources.length})</span>
              </div>
            </button>
          </nav>
        </div>

        {/* Filters (只在 All Resources 标签显示) */}
        {activeTab === 'all' && (
          <div className="p-6 border-b border-gray-200 bg-gray-50">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search resources..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Type Filter */}
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Types</option>
                {getResourceTypes().map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>

              {/* Sort */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="relevance">Relevance</option>
                <option value="name">Name</option>
                <option value="type">Type</option>
                <option value="usage">Most Used</option>
                <option value="recent">Recently Added</option>
              </select>

              {/* Results info */}
              <div className="flex items-center justify-end text-sm text-gray-500">
                Showing {currentResources.length} of {getFilteredResources().length} resources
              </div>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 280px)' }}>
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading resources...</p>
            </div>
          ) : currentResources.length === 0 ? (
            <div className="text-center py-12">
              <LinkIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                {activeTab === 'recommended' ? 'No recommended resources' : 'No resources found'}
              </h3>
              <p className="mt-2 text-gray-500">
                {activeTab === 'recommended' 
                  ? 'Enter some content in the main input box to get personalized resource recommendations based on your keywords'
                  : 'Try adjusting your search criteria or create new resources'
                }
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {currentResources.map((resource) => {
                const isSelected = selectedResources.some(r => r.id === resource.id);
                return (
                  <div
                    key={resource.id}
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-all hover:shadow-md ${
                      isSelected
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-blue-300 bg-white'
                    }`}
                    onClick={() => handleResourceToggle(resource)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        {/* Header */}
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2 min-w-0 flex-1">
                            <div className={`flex items-center justify-center w-8 h-8 rounded-lg ${
                              isSelected ? 'bg-blue-100' : 'bg-gray-100'
                            }`}>
                              {getResourceTypeIcon(resource.resource_type)}
                            </div>
                            <h4 className="font-semibold text-gray-900 truncate">
                              {resource.resource_name}
                            </h4>
                          </div>
                          {/* URL Link button */}
                          {(resource.resource_url || resource.url) && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                const url = resource.resource_url || resource.url;
                                window.open(url, '_blank', 'noopener,noreferrer');
                              }}
                              className="p-1 text-gray-400 hover:text-blue-600 transition-colors flex-shrink-0"
                              title={resource.resource_type === 'reddit' ? 'View Reddit Post' : 'Open Original URL'}
                            >
                              <ArrowTopRightOnSquareIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>

                        {/* Description */}
                        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                          {resource.description || 'No description available'}
                        </p>

                        {/* Meta info */}
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span className="inline-flex items-center px-2 py-1 rounded-full bg-gray-100">
                            {resource.resource_type}
                          </span>
                          {resource.usage_count > 0 && (
                            <span className="flex items-center">
                              <EyeIcon className="h-3 w-3 mr-1" />
                              {resource.usage_count}
                            </span>
                          )}
                        </div>

                        {/* Tags */}
                        {resource.tags && resource.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {resource.tags.slice(0, 2).map((tag, index) => (
                              <span
                                key={index}
                                className="px-1.5 py-0.5 bg-blue-100 text-blue-700 text-xs rounded"
                              >
                                {tag}
                              </span>
                            ))}
                            {resource.tags.length > 2 && (
                              <span className="px-1.5 py-0.5 bg-gray-100 text-gray-600 text-xs rounded">
                                +{resource.tags.length - 2}
                              </span>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Selection indicator */}
                      {isSelected && (
                        <CheckCircleIconSolid className="h-5 w-5 text-blue-600 flex-shrink-0 ml-2" />
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* Pagination (只在 All Resources 标签显示) */}
          {activeTab === 'all' && totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-500">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            {selectedResources.length} resource{selectedResources.length !== 1 ? 's' : ''} selected
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Select ({selectedResources.length})
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResourceSelectionModal;