import React, { useState } from 'react';
import { XMarkIcon, ChevronDownIcon, ChevronUpIcon, TagIcon, QuestionMarkCircleIcon, CloudIcon, MagnifyingGlassIcon, PlusIcon } from '@heroicons/react/24/outline';

const SelectionManager = ({ selections, onRemoveSelection, onClearAll, onUseAsKeywords, currentKeyword, onAddCustomKeyword, isGeneratingTopics }) => {
  const [expandedSections, setExpandedSections] = useState({
    autocomplete: true,
    custom: true,
    related: true,
    keyTerms: true,
    paa: true
  });

  const [customKeyword, setCustomKeyword] = useState('');

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Helper functions for keyword limits - exclude Reddit topics
  const getPrimaryKeywordCount = () => {
    return selections.filter(s => 
      (s.type === 'autocomplete' || s.type === 'related' || s.type === 'custom') 
      && s.type !== 'reddit_topic'
    ).length;
  };

  const getSecondaryKeywordCount = () => {
    return selections.filter(s => s.type === 'keyTerm' || s.type === 'paa').length;
  };

  // Reddit topics are no longer part of selections, they're handled separately

  const primaryKeywordLimit = 3;
  const secondaryKeywordLimit = 12;

  const handleAddCustomKeyword = () => {
    if (customKeyword.trim() && onAddCustomKeyword) {
      // Check primary keyword limit
      if (getPrimaryKeywordCount() >= primaryKeywordLimit) {
        alert('You can select a maximum of 3 primary keywords (Autocomplete + Related + Custom).');
        return;
      }

      onAddCustomKeyword({
        value: customKeyword.trim(),
        type: 'custom',
        source: 'user-added'
      });
      setCustomKeyword('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleAddCustomKeyword();
    }
  };

  // Group selections by type (Reddit topics are handled separately, not in selections)
  const groupedSelections = {
    autocomplete: selections.filter(item => item.type === 'autocomplete'),
    related: selections.filter(item => item.type === 'related'),
    keyTerms: selections.filter(item => item.type === 'keyTerm'),
    paa: selections.filter(item => item.type === 'paa'),
    custom: selections.filter(item => item.type === 'custom')
  };

  const totalCount = selections.length;

  // Show empty state only if no keyword and no selections
  if (totalCount === 0 && !currentKeyword) {
    return (
      <div className="w-80 bg-white rounded-lg shadow-lg border border-gray-200 p-4">
        <div className="text-center text-gray-500">
          <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <TagIcon className="w-6 h-6 text-gray-400" />
          </div>
          <h3 className="font-medium text-gray-900 mb-1">No Items Selected</h3>
          <p className="text-sm text-gray-500">
            Research a keyword and click "+" buttons to add items here.
          </p>
        </div>
      </div>
    );
  }

  // Show the manager if there's a keyword or selections
  if (!currentKeyword && totalCount === 0) {
    return null;
  }

  return (
    <div className="w-80 bg-white rounded-lg shadow-xl border border-gray-200 max-h-[calc(100vh-120px)] overflow-hidden flex flex-col">
      {/* Current Keyword */}
      {currentKeyword && (
        <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
          <h3 className="font-bold text-green-900 flex items-center">
            <MagnifyingGlassIcon className="w-5 h-5 mr-2 text-green-600" />
            Research: "{currentKeyword}"
          </h3>
        </div>
      )}

      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-bold text-gray-900 flex items-center">
            <TagIcon className="w-5 h-5 mr-2 text-blue-600" />
            Selected Items ({totalCount})
          </h3>
          {totalCount > 0 && (
            <button
              onClick={onClearAll}
              className="text-xs text-red-600 hover:text-red-800 font-medium"
            >
              Clear All
            </button>
          )}
        </div>
        {/* Limits Display */}
        <div className="flex items-center justify-between text-xs">
          <div className={`px-2 py-1 rounded ${
            getPrimaryKeywordCount() >= primaryKeywordLimit
              ? 'bg-red-100 text-red-700'
              : getPrimaryKeywordCount() >= primaryKeywordLimit - 1
              ? 'bg-yellow-100 text-yellow-700'
              : 'bg-green-100 text-green-700'
          }`}>
            Primary: {getPrimaryKeywordCount()}/{primaryKeywordLimit}
          </div>
          <div className={`px-2 py-1 rounded ${
            getSecondaryKeywordCount() >= secondaryKeywordLimit
              ? 'bg-red-100 text-red-700'
              : getSecondaryKeywordCount() >= secondaryKeywordLimit - 2
              ? 'bg-yellow-100 text-yellow-700'
              : 'bg-green-100 text-green-700'
          }`}>
            Secondary: {getSecondaryKeywordCount()}/{secondaryKeywordLimit}
          </div>
        </div>
      </div>

      {/* Content - Scrollable */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-4">
          {/* Show message when keyword exists but no selections */}
          {currentKeyword && totalCount === 0 && (
            <div className="text-center text-gray-500 py-8">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <TagIcon className="w-6 h-6 text-blue-400" />
              </div>
              <h4 className="font-medium text-gray-900 mb-2">Ready to Collect Items</h4>
              <p className="text-sm text-gray-500">
                Click the "+" buttons next to keywords, terms, or questions to add them here.
              </p>
            </div>
          )}
          
          
          {/* Autocomplete Keywords */}
          {groupedSelections.autocomplete.length > 0 && (
            <div className="border border-blue-200 rounded-lg">
              <button
                onClick={() => toggleSection('autocomplete')}
                className="w-full flex items-center justify-between p-3 text-left hover:bg-blue-50"
              >
                <h4 className="font-medium text-blue-900 flex items-center text-sm">
                  <MagnifyingGlassIcon className="w-4 h-4 mr-2" />
                  Autocomplete ({groupedSelections.autocomplete.length})
                </h4>
                {expandedSections.autocomplete ? (
                  <ChevronUpIcon className="w-4 h-4 text-blue-500" />
                ) : (
                  <ChevronDownIcon className="w-4 h-4 text-blue-500" />
                )}
              </button>

              {expandedSections.autocomplete && (
                <div className="px-3 pb-3 space-y-2">
                  {groupedSelections.autocomplete.map((item, index) => (
                    <div
                      key={`autocomplete-${index}`}
                      className="flex items-center justify-between p-2 bg-blue-50 rounded border border-blue-200"
                    >
                      <span className="text-sm text-blue-900 flex-1 truncate">{item.value}</span>
                      <button
                        onClick={() => onRemoveSelection(item)}
                        className="ml-2 text-blue-600 hover:text-blue-800"
                      >
                        <XMarkIcon className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Related Keywords */}
          {groupedSelections.related.length > 0 && (
            <div className="border border-gray-200 rounded-lg">
              <button
                onClick={() => toggleSection('related')}
                className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50"
              >
                <h4 className="font-medium text-gray-900 flex items-center text-sm">
                  <TagIcon className="w-4 h-4 mr-2" />
                  Related Keywords ({groupedSelections.related.length})
                </h4>
                {expandedSections.related ? (
                  <ChevronUpIcon className="w-4 h-4 text-gray-500" />
                ) : (
                  <ChevronDownIcon className="w-4 h-4 text-gray-500" />
                )}
              </button>

              {expandedSections.related && (
                <div className="px-3 pb-3 space-y-2">
                  {groupedSelections.related.map((item, index) => (
                    <div
                      key={`related-${index}`}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded border border-gray-200"
                    >
                      <div className="flex-1">
                        <span className="text-sm text-gray-900 truncate block">{item.value}</span>
                        {item.relevance && (
                          <span className={`text-xs px-1 py-0.5 rounded ${
                            item.relevance === 'high' ? 'bg-green-100 text-green-800' :
                            item.relevance === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {item.relevance}
                          </span>
                        )}
                      </div>
                      <button
                        onClick={() => onRemoveSelection(item)}
                        className="ml-2 text-gray-600 hover:text-gray-800"
                      >
                        <XMarkIcon className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Custom Keywords */}
          {groupedSelections.custom.length > 0 && (
            <div className="border border-green-200 rounded-lg">
              <button
                onClick={() => toggleSection('custom')}
                className="w-full flex items-center justify-between p-3 text-left hover:bg-green-50"
              >
                <h4 className="font-medium text-green-900 flex items-center text-sm">
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Custom Keywords ({groupedSelections.custom.length})
                </h4>
                {expandedSections.custom ? (
                  <ChevronUpIcon className="w-4 h-4 text-green-500" />
                ) : (
                  <ChevronDownIcon className="w-4 h-4 text-green-500" />
                )}
              </button>

              {expandedSections.custom && (
                <div className="px-3 pb-3 space-y-2">
                  {groupedSelections.custom.map((item, index) => (
                    <div
                      key={`custom-${index}`}
                      className="flex items-center justify-between p-2 bg-green-50 rounded border border-green-200"
                    >
                      <span className="text-sm text-green-900 flex-1 truncate">{item.value}</span>
                      <button
                        onClick={() => onRemoveSelection(item)}
                        className="ml-2 text-green-600 hover:text-green-800"
                      >
                        <XMarkIcon className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Key Terms */}
          {groupedSelections.keyTerms.length > 0 && (
            <div className="border border-purple-200 rounded-lg">
              <button
                onClick={() => toggleSection('keyTerms')}
                className="w-full flex items-center justify-between p-3 text-left hover:bg-purple-50"
              >
                <h4 className="font-medium text-purple-900 flex items-center text-sm">
                  <CloudIcon className="w-4 h-4 mr-2" />
                  Key Terms ({groupedSelections.keyTerms.length})
                </h4>
                {expandedSections.keyTerms ? (
                  <ChevronUpIcon className="w-4 h-4 text-purple-500" />
                ) : (
                  <ChevronDownIcon className="w-4 h-4 text-purple-500" />
                )}
              </button>

              {expandedSections.keyTerms && (
                <div className="px-3 pb-3 space-y-2">
                  {groupedSelections.keyTerms.map((item, index) => (
                    <div
                      key={`keyterm-${index}`}
                      className="flex items-center justify-between p-2 bg-purple-50 rounded border border-purple-200"
                    >
                      <div className="flex-1">
                        <span className="text-sm text-purple-900 truncate block">{item.value}</span>
                        {item.count && (
                          <span className="text-xs text-purple-600">({item.count} mentions)</span>
                        )}
                      </div>
                      <button
                        onClick={() => onRemoveSelection(item)}
                        className="ml-2 text-purple-600 hover:text-purple-800"
                      >
                        <XMarkIcon className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* People Also Ask */}
          {groupedSelections.paa.length > 0 && (
            <div className="border border-orange-200 rounded-lg">
              <button
                onClick={() => toggleSection('paa')}
                className="w-full flex items-center justify-between p-3 text-left hover:bg-orange-50"
              >
                <h4 className="font-medium text-orange-900 flex items-center text-sm">
                  <QuestionMarkCircleIcon className="w-4 h-4 mr-2" />
                  Questions ({groupedSelections.paa.length})
                </h4>
                {expandedSections.paa ? (
                  <ChevronUpIcon className="w-4 h-4 text-orange-500" />
                ) : (
                  <ChevronDownIcon className="w-4 h-4 text-orange-500" />
                )}
              </button>

              {expandedSections.paa && (
                <div className="px-3 pb-3 space-y-2">
                  {groupedSelections.paa.map((item, index) => (
                    <div
                      key={`paa-${index}`}
                      className="flex items-start justify-between p-2 bg-orange-50 rounded border border-orange-200"
                    >
                      <span className="text-sm text-orange-900 flex-1 leading-tight">{item.value}</span>
                      <button
                        onClick={() => onRemoveSelection(item)}
                        className="ml-2 text-orange-600 hover:text-orange-800 flex-shrink-0"
                      >
                        <XMarkIcon className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Add Custom Keyword Section */}
          <div className="border border-green-200 rounded-lg bg-gradient-to-r from-green-50 to-emerald-50">
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-green-900 flex items-center text-sm">
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Add Custom Keyword
                </h4>
                {getPrimaryKeywordCount() >= primaryKeywordLimit && (
                  <span className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded">
                    Primary limit reached
                  </span>
                )}
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={customKeyword}
                  onChange={(e) => setCustomKeyword(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Enter your keyword..."
                  disabled={getPrimaryKeywordCount() >= primaryKeywordLimit}
                  className={`flex-1 px-3 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:border-transparent ${
                    getPrimaryKeywordCount() >= primaryKeywordLimit
                      ? 'border-gray-300 bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'border-green-300 focus:ring-green-500'
                  }`}
                />
                <button
                  onClick={handleAddCustomKeyword}
                  disabled={!customKeyword.trim() || getPrimaryKeywordCount() >= primaryKeywordLimit}
                  className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    customKeyword.trim() && getPrimaryKeywordCount() < primaryKeywordLimit
                      ? 'bg-green-600 text-white hover:bg-green-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  <PlusIcon className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      {totalCount > 0 && (
        <div className="p-4 border-t border-gray-200 bg-gray-50 space-y-2">
          {/* Requirements Info */}
          <div className="text-xs text-gray-600 mb-2">
            <div className="flex justify-between">
              <span>Minimum: 1 primary (autocomplete/related/custom) + 5 secondary (terms/questions)</span>
              <span className={`${
                getPrimaryKeywordCount() >= 1 && getSecondaryKeywordCount() >= 5
                  ? 'text-green-600' : 'text-orange-600'
              }`}>
                {getPrimaryKeywordCount() >= 1 && getSecondaryKeywordCount() >= 5 ? '✓ Ready' : '⚠ Need more'}
              </span>
            </div>
          </div>

          <button
            onClick={() => onUseAsKeywords(selections)}
            disabled={isGeneratingTopics || getPrimaryKeywordCount() < 1 || getSecondaryKeywordCount() < 5}
            className={`w-full px-4 py-2 rounded-lg font-medium text-sm flex items-center justify-center ${
              isGeneratingTopics
                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                : getPrimaryKeywordCount() < 1 || getSecondaryKeywordCount() < 5
                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isGeneratingTopics ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                Generating Topics...
              </>
            ) : (
              `Generate Article Topics (${selections.length})`
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default SelectionManager;
