import React, { useMemo, useCallback, memo } from 'react';
import YouTube from 'react-youtube';

const YouTubeVideo = memo(({ videoId, className = "", iframeClassName = "" }) => {
  // Memoize YouTube options to prevent re-creation on every render
  const youtubeOpts = useMemo(() => ({
    width: '100%',
    height: '100%',
    playerVars: {
      autoplay: 0,
      controls: 1,
      rel: 0,
      showinfo: 0,
      modestbranding: 1,
      fs: 1,
      cc_load_policy: 0,
      iv_load_policy: 3,
      autohide: 0
    }
  }), []);

  // Memoize event handlers to prevent re-creation
  const onReady = useCallback((event) => {
    // Video is ready
    console.log('YouTube video ready:', videoId);
  }, [videoId]);

  const onError = useCallback((error) => {
    console.error('YouTube video error:', error);
  }, []);

  const onStateChange = useCallback((event) => {
    // Handle state changes if needed
    // console.log('YouTube state change:', event.data);
  }, []);

  return (
    <YouTube
      videoId={videoId}
      opts={youtubeOpts}
      onReady={onReady}
      onError={onError}
      onStateChange={onStateChange}
      className={className}
      iframeClassName={iframeClassName}
    />
  );
});

YouTubeVideo.displayName = 'YouTubeVideo';

export default YouTubeVideo;
