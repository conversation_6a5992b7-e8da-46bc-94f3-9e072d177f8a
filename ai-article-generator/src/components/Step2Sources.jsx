import React, { useState } from 'react';
import { PlusIcon, XMarkIcon, LinkIcon, DocumentTextIcon, ClockIcon, FolderOpenIcon, ArrowTopRightOnSquareIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { apiCall, API_CONFIG } from '../config/api';
import RedditInsightAssistant from './RedditInsightAssistant';
import { useAuth } from '../contexts/AuthContext';

const Step2Sources = ({ 
  data, 
  updateData, 
  onNext, 
  onPrev, 
  isFirstStep, 
  isLastStep, 
  isGeneratingTopics, 
  setIsGeneratingTopics, 
  taskId, 
  title, 
  subtitle, 
  stepNumber, 
  totalSteps,
  ...restProps 
}) => {
  const { authenticatedFetch } = useAuth();
  const [urlInput, setUrlInput] = useState('');
  const [textInput, setTextInput] = useState('');
  const [isExtracting, setIsExtracting] = useState(false);
  const [showResourceSelector, setShowResourceSelector] = useState(false);
  const [showRedditSelector, setShowRedditSelector] = useState(false);
  const [previousResources, setPreviousResources] = useState([]);
  const [previousRedditPosts, setPreviousRedditPosts] = useState([]);
  const [loadingResources, setLoadingResources] = useState(false);
  const [resourcePage, setResourcePage] = useState(1);
  const [redditPage, setRedditPage] = useState(1);
  const [resourcePagination, setResourcePagination] = useState({ total: 0, totalPages: 0 });
  const [redditPagination, setRedditPagination] = useState({ total: 0, totalPages: 0 });
  const ITEMS_PER_PAGE = 10;

  // Get selected topics from Step 1
  const selectedTopics = data.selectedTopics || [];

  // Load previous resources with pagination
  const loadPreviousResources = async (page = 1, type = 'all') => {
    setLoadingResources(true);
    try {
      const response = await authenticatedFetch(
        `${API_CONFIG.ENDPOINTS.RESOURCES.LIST}?page=${page}&limit=${ITEMS_PER_PAGE}&sortBy=updated_at&sortOrder=DESC`
      );
      if (response.ok) {
        const data = await response.json();
        const resources = data.resources || [];
        const pagination = data.pagination || { total: 0, totalPages: 0 };
        
        // Filter resources to show URL and Reddit types separately
        const urlResources = resources.filter(r => r.resource_type === 'url' || r.resource_type === 'document' || r.resource_type === 'manual');
        const redditResources = resources.filter(r => r.resource_type === 'reddit');
        
        if (type === 'all' || type === 'resources') {
          setPreviousResources(urlResources);
          setResourcePagination(pagination);
        }
        if (type === 'all' || type === 'reddit') {
          setPreviousRedditPosts(redditResources);
          setRedditPagination(pagination);
        }
      }
    } catch (error) {
      console.error('Failed to load previous resources:', error);
    } finally {
      setLoadingResources(false);
    }
  };

  // Load specific page for resources
  const loadResourcePage = async (page) => {
    setResourcePage(page);
    await loadPreviousResources(page, 'resources');
  };

  // Load specific page for Reddit
  const loadRedditPage = async (page) => {
    setRedditPage(page);
    await loadPreviousResources(page, 'reddit');
  };

  // Add previous resource to current task
  const addPreviousResource = (resource) => {
    const newSource = {
      type: resource.resource_type,
      url: resource.resource_url || '',
      title: resource.resource_name,
      content: resource.resource_content || '',
      description: resource.description || '',
      wordCount: resource.resource_content ? resource.resource_content.split(/\s+/).length : 0
    };

    // Check if already added
    const existingSources = data.sources || [];
    const alreadyExists = existingSources.some(s => 
      (s.url && s.url === newSource.url) || 
      (s.title === newSource.title && s.type === newSource.type)
    );

    if (!alreadyExists && existingSources.length < 10) {
      updateData({
        sources: [...existingSources, newSource]
      });
      
      // Mark resource as used
      authenticatedFetch(API_CONFIG.ENDPOINTS.RESOURCES.USE(resource.id), {
        method: 'POST'
      }).catch(console.error);
    }
  };

  // Add previous Reddit post to current task
  const addPreviousRedditPost = (redditResource) => {
    const redditSource = {
      type: 'reddit',
      url: redditResource.resource_url,
      title: redditResource.resource_name,
      content: redditResource.resource_content || '',
      description: redditResource.description || '',
      wordCount: redditResource.resource_content ? redditResource.resource_content.split(/\s+/).length : 0,
      // Extract subreddit from description if available
      subreddit: redditResource.description?.match(/r\/(\w+)/)?.[1] || 'unknown'
    };

    // Check if already added
    const existingSources = data.sources || [];
    const alreadyExists = existingSources.some(source => source.url === redditSource.url);
    
    if (!alreadyExists && existingSources.length < 10) {
      updateData({
        sources: [...existingSources, redditSource]
      });
      
      // Mark resource as used
      authenticatedFetch(API_CONFIG.ENDPOINTS.RESOURCES.USE(redditResource.id), {
        method: 'POST'
      }).catch(console.error);
    }
  };

  const addUrlSource = async () => {
    if (!urlInput.trim()) return;

    setIsExtracting(true);
    try {
      const extractedData = await apiCall(API_CONFIG.ENDPOINTS.SOURCES_EXTRACT, {
        method: 'POST',
        body: JSON.stringify({ url: urlInput.trim() }),
      });

      updateData({
        sources: [...(data.sources || []), {
          type: 'url',
          url: urlInput.trim(),
          title: extractedData.title,
          content: extractedData.content,
          description: extractedData.description,
          wordCount: extractedData.wordCount
        }]
      });

      setUrlInput('');
    } catch (error) {
      console.error('Error extracting URL content:', error);
      alert('Failed to extract content from URL. Please check the URL and try again.');
    } finally {
      setIsExtracting(false);
    }
  };

  const addTextSource = () => {
    if (!textInput.trim()) return;

    const wordCount = textInput.trim().split(/\s+/).length;

    updateData({
      sources: [...(data.sources || []), {
        type: 'text',
        title: 'Custom Text Block',
        content: textInput.trim(),
        wordCount: wordCount
      }]
    });

    setTextInput('');
  };

  const removeSource = (index) => {
    updateData({
      sources: (data.sources || []).filter((_, i) => i !== index)
    });
  };

  // Handle Reddit post selection
  const handleRedditPostUse = (postData) => {
    console.log('Reddit post selected:', postData);
    
    // Debug: Check for invalid postData
    if (postData && typeof postData === 'object' && postData.original && postData.edited) {
      console.error('Invalid postData object with original/edited keys:', postData);
      return;
    }
    
    // Add as Reddit source
    const redditSource = {
      type: 'reddit',
      url: postData.source.url,
      title: postData.topic,
      content: `Reddit discussion from r/${postData.source.subreddit}: ${postData.topic}`,
      description: `Upvotes: ${postData.source.upvotes}, Comments: ${postData.source.comments}`,
      wordCount: postData.topic.split(' ').length + 10, // Estimate
      subreddit: postData.source.subreddit,
      upvotes: postData.source.upvotes,
      comments: postData.source.comments
    };

    // Check if already added
    const existingSources = data.sources || [];
    const alreadyExists = existingSources.some(source => source.url === postData.source.url);
    
    if (alreadyExists) {
      // Remove existing source
      updateData({
        sources: existingSources.filter(source => source.url !== postData.source.url)
      });
    } else if (existingSources.length < 10) { // Allow more sources for comprehensive content
      // Add new source
      updateData({
        sources: [...existingSources, redditSource]
      });
    }
  };

  // Get selected Reddit posts for display
  const getSelectedRedditPosts = () => {
    const sources = data.sources || [];
    return sources.filter(source => source.type === 'reddit');
  };

  return (
    <div className="space-y-8">
      {/* Selected Topics Display */}
      {selectedTopics.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-blue-900 mb-3">Selected Topics from Step 1</h3>
          <div className="grid grid-cols-1 gap-2">
            {selectedTopics.map((topic, index) => (
              <div key={index} className="bg-white border border-blue-200 rounded-md p-3">
                <h4 className="font-medium text-blue-800">
                  {typeof topic === 'string' ? topic : (topic.edited || topic.original || 'Untitled Topic')}
                </h4>
              </div>
            ))}
          </div>
          <p className="text-sm text-blue-700 mt-3">
            These topics will be used as the foundation for your article. Add resources below to provide additional context and information.
          </p>
        </div>
      )}

      <div>
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-medium text-gray-900">Resource Integration</h3>
          <button
            onClick={() => {
              loadPreviousResources();
              setShowResourceSelector(!showResourceSelector);
            }}
            className="flex items-center gap-2 px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors shadow-md"
          >
            <ClockIcon className="w-5 h-5" />
            Load Previous Resources
          </button>
        </div>
        <p className="text-sm text-gray-600 mb-6">
          Add research materials to enrich your article content. You can add URLs for automatic content extraction, paste text blocks directly, or discover relevant Reddit discussions.
        </p>
      </div>

      {/* Previous Resources Selector */}
      {showResourceSelector && (
        <div className="mb-6 p-6 bg-white border-2 border-blue-200 rounded-xl shadow-lg">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h4 className="text-lg font-semibold text-gray-900">Select Previous Resources</h4>
              <p className="text-sm text-gray-600">Choose from your previously saved research materials</p>
            </div>
            <button
              onClick={() => setShowResourceSelector(false)}
              className="text-gray-500 hover:text-gray-700 p-1"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>
          
          {loadingResources ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading resources...</p>
            </div>
          ) : previousResources.length > 0 ? (
            <>
              <div className="space-y-3 max-h-96 overflow-y-auto mb-4">
                {previousResources.map((resource) => {
                  const isSelected = (data.sources || []).some(s => 
                    (s.url && s.url === resource.resource_url) || 
                    (s.title === resource.resource_name)
                  );
                  return (
                    <div
                      key={resource.id}
                      className={`p-4 border-2 rounded-lg transition-all ${
                        isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300 hover:shadow-md'
                      }`}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h5 className="font-semibold text-gray-900 mb-1">{resource.resource_name}</h5>
                          <p className="text-sm text-gray-600 mb-2">{resource.description}</p>
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span className="bg-gray-100 px-2 py-1 rounded-md font-medium">{resource.resource_type}</span>
                            <span>Used {resource.usage_count} times</span>
                            {isSelected && <span className="text-blue-600 font-semibold">✓ Already added</span>}
                          </div>
                        </div>
                        <div className="flex gap-2 ml-4">
                          {resource.resource_url && (
                            <a
                              href={resource.resource_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-1 px-3 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <ArrowTopRightOnSquareIcon className="w-3 h-3" />
                              View
                            </a>
                          )}
                          {!isSelected && (
                            <button
                              onClick={() => addPreviousResource(resource)}
                              className="px-3 py-1.5 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                            >
                              Add to Task
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
              
              {/* Pagination for resources */}
              {resourcePagination.totalPages > 1 && (
                <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
                  <p className="text-sm text-gray-600">
                    Showing {((resourcePage - 1) * ITEMS_PER_PAGE) + 1} to {Math.min(resourcePage * ITEMS_PER_PAGE, resourcePagination.total)} of {resourcePagination.total} resources
                  </p>
                  <div className="flex gap-2">
                    <button
                      onClick={() => loadResourcePage(resourcePage - 1)}
                      disabled={resourcePage <= 1}
                      className="flex items-center gap-1 px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeftIcon className="w-4 h-4" />
                      Previous
                    </button>
                    <span className="px-3 py-1.5 text-sm text-gray-700">
                      Page {resourcePage} of {resourcePagination.totalPages}
                    </span>
                    <button
                      onClick={() => loadResourcePage(resourcePage + 1)}
                      disabled={resourcePage >= resourcePagination.totalPages}
                      className="flex items-center gap-1 px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                      <ChevronRightIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-8">
              <DocumentTextIcon className="w-12 h-12 text-gray-300 mx-auto mb-2" />
              <p className="text-gray-500">No previous resources found</p>
              <p className="text-sm text-gray-400">Start adding resources to see them here</p>
            </div>
          )}
        </div>
      )}

      {/* URL Input Section */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <LinkIcon className="w-4 h-4 inline mr-1" />
          Add URL Source
        </label>
        <div className="flex gap-2 mb-4">
          <input
            type="url"
            value={urlInput}
            onChange={(e) => setUrlInput(e.target.value)}
            placeholder="https://example.com/article"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <button
            onClick={addUrlSource}
            disabled={!urlInput.trim() || isExtracting}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            {isExtracting ? 'Extracting...' : <PlusIcon className="w-5 h-5" />}
          </button>
        </div>
      </div>

      {/* Text Input Section */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <DocumentTextIcon className="w-4 h-4 inline mr-1" />
          Add Text Block
        </label>
        <textarea
          value={textInput}
          onChange={(e) => setTextInput(e.target.value)}
          placeholder="Paste your research notes, product descriptions, or any relevant text here..."
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <div className="flex justify-between items-center mt-2">
          <span className="text-sm text-gray-500">
            {textInput.trim() ? `${textInput.trim().split(/\s+/).length} words` : ''}
          </span>
          <button
            onClick={addTextSource}
            disabled={!textInput.trim()}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          >
            Add Text Block
          </button>
        </div>
      </div>

      {/* Reddit Insight Assistant */}
      <div className="relative">
        {/* Load Previous Reddit Posts Button */}
        <div className="absolute top-20 right-6 z-10">
          <button
            onClick={() => {
              if (previousRedditPosts.length === 0) {
                loadPreviousResources();
              }
              setShowRedditSelector(!showRedditSelector);
            }}
            className="flex items-center gap-2 px-4 py-2 text-sm bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors shadow-md"
          >
            <FolderOpenIcon className="w-5 h-5" />
            Load Previous Reddit Posts
          </button>
        </div>

        {/* Previous Reddit Posts Selector */}
        {showRedditSelector && (
          <div className="mb-4 p-6 bg-white border-2 border-orange-200 rounded-xl shadow-lg">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h4 className="text-lg font-semibold text-gray-900">Select Previous Reddit Posts</h4>
                <p className="text-sm text-gray-600">Choose from your previously saved Reddit discussions</p>
              </div>
              <button
                onClick={() => setShowRedditSelector(false)}
                className="text-gray-500 hover:text-gray-700 p-1"
              >
                <XMarkIcon className="w-6 h-6" />
              </button>
            </div>
            
            {loadingResources ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading Reddit posts...</p>
              </div>
            ) : previousRedditPosts.length > 0 ? (
              <>
                <div className="space-y-3 max-h-96 overflow-y-auto mb-4">
                  {previousRedditPosts.map((post) => {
                    const isSelected = (data.sources || []).some(s => s.url === post.resource_url);
                    return (
                      <div
                        key={post.id}
                        className={`p-4 border-2 rounded-lg transition-all ${
                          isSelected ? 'border-orange-500 bg-orange-50' : 'border-orange-200 hover:border-orange-400 hover:shadow-md'
                        }`}
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h5 className="font-semibold text-gray-900 mb-1">{post.resource_name}</h5>
                            <p className="text-sm text-gray-600 mb-2">{post.description}</p>
                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded-md font-medium">Reddit</span>
                              <span>Used {post.usage_count} times</span>
                              {isSelected && <span className="text-orange-600 font-semibold">✓ Already added</span>}
                            </div>
                          </div>
                          <div className="flex gap-2 ml-4">
                            {post.resource_url && (
                              <a
                                href={post.resource_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1 px-3 py-1.5 text-xs bg-orange-100 text-orange-700 rounded-md hover:bg-orange-200 transition-colors"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <ArrowTopRightOnSquareIcon className="w-3 h-3" />
                                View on Reddit
                              </a>
                            )}
                            {!isSelected && (
                              <button
                                onClick={() => addPreviousRedditPost(post)}
                                className="px-3 py-1.5 text-xs bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors"
                              >
                                Add to Task
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
                
                {/* Pagination for Reddit posts */}
                {redditPagination.totalPages > 1 && (
                  <div className="flex justify-between items-center mt-4 pt-4 border-t border-orange-200">
                    <p className="text-sm text-gray-600">
                      Showing {((redditPage - 1) * ITEMS_PER_PAGE) + 1} to {Math.min(redditPage * ITEMS_PER_PAGE, redditPagination.total)} of {redditPagination.total} Reddit posts
                    </p>
                    <div className="flex gap-2">
                      <button
                        onClick={() => loadRedditPage(redditPage - 1)}
                        disabled={redditPage <= 1}
                        className="flex items-center gap-1 px-3 py-1.5 text-sm bg-orange-100 text-orange-700 rounded-md hover:bg-orange-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ChevronLeftIcon className="w-4 h-4" />
                        Previous
                      </button>
                      <span className="px-3 py-1.5 text-sm text-gray-700">
                        Page {redditPage} of {redditPagination.totalPages}
                      </span>
                      <button
                        onClick={() => loadRedditPage(redditPage + 1)}
                        disabled={redditPage >= redditPagination.totalPages}
                        className="flex items-center gap-1 px-3 py-1.5 text-sm bg-orange-100 text-orange-700 rounded-md hover:bg-orange-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Next
                        <ChevronRightIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8">
                <FolderOpenIcon className="w-12 h-12 text-orange-300 mx-auto mb-2" />
                <p className="text-gray-500">No previous Reddit posts found</p>
                <p className="text-sm text-gray-400">Start adding Reddit posts to see them here</p>
              </div>
            )}
          </div>
        )}

        <RedditInsightAssistant
          onUsePost={handleRedditPostUse}
          currentKeyword={data.keywords?.[0] || ''}
          selectedPosts={getSelectedRedditPosts()}
        />
      </div>

      {/* Sources Summary */}
      {(data.sources || []).length > 0 && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h4 className="text-md font-medium text-gray-800 mb-3">
            Added Sources ({(data.sources || []).length})
          </h4>
          <div className="space-y-3">
            {(data.sources || []).map((source, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h5 className="font-medium text-gray-900">{typeof source.title === 'string' ? source.title : 'Untitled'}</h5>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        source.type === 'url' ? 'bg-blue-100 text-blue-800' :
                        source.type === 'reddit' ? 'bg-orange-100 text-orange-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {source.type === 'url' ? 'URL' : 
                         source.type === 'reddit' ? 'Reddit' : 'Text'}
                      </span>
                    </div>
                    
                    {source.type === 'url' && (
                      <p className="text-sm text-blue-600 break-all">{typeof source.url === 'string' ? source.url : ''}</p>
                    )}
                    
                    {source.type === 'reddit' && (
                      <div className="text-sm text-orange-600">
                        <p>r/{typeof source.subreddit === 'string' ? source.subreddit : ''} • {typeof source.upvotes === 'number' ? source.upvotes : 0} upvotes • {typeof source.comments === 'number' ? source.comments : 0} comments</p>
                        <a href={typeof source.url === 'string' ? source.url : '#'} target="_blank" rel="noopener noreferrer" 
                           className="text-blue-600 hover:text-blue-800 break-all">
                          {typeof source.url === 'string' ? source.url : 'Invalid URL'}
                        </a>
                      </div>
                    )}
                    
                    {source.description && typeof source.description === 'string' && (
                      <p className="text-sm text-gray-600 mt-1">{source.description}</p>
                    )}
                    
                    <p className="text-xs text-gray-500 mt-1">
                      {typeof source.wordCount === 'number' ? source.wordCount : 0} words
                    </p>
                  </div>
                  
                  <button
                    onClick={() => removeSource(index)}
                    className="ml-2 text-red-600 hover:text-red-800"
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                </div>

                {/* Content Preview */}
                <div className="bg-gray-50 rounded p-3 mt-2">
                  <p className="text-sm text-gray-700 line-clamp-3">
                    {typeof source.content === 'string' ? source.content.substring(0, 200) : 'Content preview unavailable'}
                    {typeof source.content === 'string' && source.content.length > 200 && '...'}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {(!data.sources || data.sources.length === 0) && (
        <div className="text-center py-8 text-gray-500">
          <DocumentTextIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p className="text-sm">
            Add sources to provide context and research material for your article
          </p>
        </div>
      )}
    </div>
  );
};

export default Step2Sources;