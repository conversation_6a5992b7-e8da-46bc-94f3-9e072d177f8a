import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  ArrowLeftIcon,
  UserIcon,
  EnvelopeIcon,
  CreditCardIcon,
  CheckCircleIcon,
  XCircleIcon,
  CalendarDaysIcon,
  DocumentTextIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import WriterJLogo from '../WriterJLogo';

const Profile = () => {
  const { user, logout } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusBadge = (verified) => {
    if (verified) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <CheckCircleIcon className="w-3 h-3 mr-1" />
          Verified
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <XCircleIcon className="w-3 h-3 mr-1" />
          Unverified
        </span>
      );
    }
  };

  const getPlanBadge = (planType) => {
    const plans = {
      'V1_DEFAULT_ACCESS': { name: 'Free', color: 'bg-gray-100 text-gray-800' },
      'free': { name: 'Free', color: 'bg-gray-100 text-gray-800' },
      'pro': { name: 'Pro', color: 'bg-blue-100 text-blue-800' },
      'max': { name: 'Max', color: 'bg-purple-100 text-purple-800' }
    };
    
    const plan = plans[planType] || plans['free'];
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${plan.color}`}>
        {plan.name}
      </span>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Link
                to="/dashboard"
                className="p-2 rounded-lg text-gray-700 hover:bg-white/50 transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5" />
              </Link>
              <WriterJLogo className="w-8 h-8" showText={true} textClassName="text-xl font-bold text-gray-900" />
              <h1 className="text-2xl font-bold text-gray-900">Profile</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome, {user?.fullName}</span>
              <button
                onClick={handleLogout}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Profile Card */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          {/* Header Section */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                  <UserIcon className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">{user?.fullName || 'User'}</h2>
                  <p className="text-blue-100">{user?.email}</p>
                </div>
              </div>
              <Link
                to="/settings"
                className="flex items-center space-x-2 px-4 py-2 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors"
              >
                <PencilIcon className="w-4 h-4" />
                <span>Edit Profile</span>
              </Link>
            </div>
          </div>

          {/* Content Section */}
          <div className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Account Information */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Account Information</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <EnvelopeIcon className="w-5 h-5 text-gray-400" />
                      <span className="text-sm font-medium text-gray-700">Email Status</span>
                    </div>
                    {getStatusBadge(user?.emailVerified)}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <CreditCardIcon className="w-5 h-5 text-gray-400" />
                      <span className="text-sm font-medium text-gray-700">Plan</span>
                    </div>
                    {getPlanBadge(user?.planType)}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <CalendarDaysIcon className="w-5 h-5 text-gray-400" />
                      <span className="text-sm font-medium text-gray-700">Member Since</span>
                    </div>
                    <span className="text-sm text-gray-600">
                      {user?.createdAt ? formatDate(user.createdAt) : 'N/A'}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <DocumentTextIcon className="w-5 h-5 text-gray-400" />
                      <span className="text-sm font-medium text-gray-700">Total Articles</span>
                    </div>
                    <span className="text-sm text-gray-600">{user?.taskCount || 0}</span>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <Link
                    to="/start-article"
                    className="block w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-center font-medium"
                  >
                    Create New Article
                  </Link>
                  
                  <Link
                    to="/tasks"
                    className="block w-full px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-center font-medium"
                  >
                    View All Articles
                  </Link>
                  
                  <Link
                    to="/settings"
                    className="block w-full px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-center font-medium"
                  >
                    Account Settings
                  </Link>
                  
                  {user?.planType === 'V1_DEFAULT_ACCESS' && (
                    <Link
                      to="/pricing"
                      className="block w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-colors text-center font-medium"
                    >
                      Upgrade to Pro
                    </Link>
                  )}
                </div>
              </div>
            </div>

            {/* Stats Section */}
            <div className="mt-8 pt-8 border-t border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Usage Statistics</h3>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{user?.taskCount || 0}</div>
                  <div className="text-sm text-blue-800">Articles Created</div>
                </div>
                
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {user?.planType === 'V1_DEFAULT_ACCESS' ? 'Free' : 'Pro'}
                  </div>
                  <div className="text-sm text-green-800">Current Plan</div>
                </div>
                
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {user?.emailVerified ? 'Active' : 'Inactive'}
                  </div>
                  <div className="text-sm text-purple-800">Account Status</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;