import React from 'react';
import { Link } from 'react-router-dom';
import YouTubeVideo from '../YouTubeVideo';
import {
  SparklesIcon,
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  StarIcon,
  UserGroupIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import <PERSON><PERSON><PERSON><PERSON> from '../WriterJLogo';

const Homepage = () => {
  const features = [
    {
      icon: <SparklesIcon className="w-8 h-8 text-blue-600" />,
      title: "Guided 7-Step Process",
      description: "From keyword research to final article - never wonder what to do next"
    },
    {
      icon: <DocumentTextIcon className="w-8 h-8 text-green-600" />,
      title: "Built-in Research Tools",
      description: "Keyword analysis and source integration included in every step"
    },
    {
      icon: <ClockIcon className="w-8 h-8 text-purple-600" />,
      title: "Save & Continue Anytime",
      description: "Resume your work exactly where you left off with auto-save drafts"
    },
    {
      icon: <CheckCircleIcon className="w-8 h-8 text-indigo-600" />,
      title: "Publication-Ready Output",
      description: "Get formatted, SEO-optimized articles ready to publish immediately"
    }
  ];

  const benefits = [
    { label: "Structured Process", value: "7 Steps", icon: <DocumentTextIcon className="w-6 h-6" /> },
    { label: "Research Included", value: "Keywords", icon: <UserGroupIcon className="w-6 h-6" /> },
    { label: "AI Assistance", value: "Every Step", icon: <ClockIcon className="w-6 h-6" /> },
    { label: "Ready to Publish", value: "Complete", icon: <ChartBarIcon className="w-6 h-6" /> }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <WriterJLogo className="w-10 h-10" showText={true} textClassName="text-2xl font-bold text-gray-900" />
            
            <div className="hidden md:flex items-center space-x-8">
              <Link to="/features" className="text-gray-700 hover:text-blue-600 transition-colors">Features</Link>
              <Link to="/pricing" className="text-gray-700 hover:text-blue-600 transition-colors">Pricing</Link>
              <Link to="/blog" className="text-gray-700 hover:text-blue-600 transition-colors">Blog</Link>
            </div>
            
            <div className="flex items-center space-x-4">
              <Link
                to="/login"
                className="text-gray-700 hover:text-blue-600 transition-colors"
              >
                Sign In
              </Link>
              <Link
                to="/register"
                className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Try Free
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <div className="mb-8">
            <div className="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <StarIcon className="w-4 h-4" />
              <span>AI-Powered 7-Step Writing Process</span>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Turn Keywords Into{' '}
              <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                High-Ranking Articles
              </span>{' '}
              In Just 7 Steps
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Our structured 7-step workflow guides you from keyword research to publication-ready articles. 
              No more staring at blank pages or wondering what to write next.
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link
              to="/register"
              className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white text-lg font-semibold rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
            >
              Try Writer J Free
              <ArrowRightIcon className="w-5 h-5 ml-2" />
            </Link>
            <Link
              to="/register"
              className="inline-flex items-center justify-center px-8 py-4 bg-white text-gray-700 text-lg font-semibold rounded-xl hover:bg-gray-50 transition-all duration-200 shadow-lg hover:shadow-xl border border-gray-200"
            >
              See How It Works
            </Link>
          </div>

          {/* Demo Video */}
          <div className="relative max-w-4xl mx-auto">
            <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-200">
              <div className="mb-6 text-center">
                <h3 className="text-2xl font-semibold text-gray-900 mb-2">See Writer J in Action</h3>
                <p className="text-gray-600">Watch how easy it is to create professional articles with AI assistance</p>
              </div>
              <div className="aspect-video rounded-xl overflow-hidden shadow-lg">
                <YouTubeVideo
                  videoId="pvlSC9ycQPU"
                  className="w-full h-full"
                  iframeClassName="w-full h-full rounded-xl"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-white/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Everything You Need to Create Great Articles
            </h2>
            <p className="text-lg text-gray-600">
              Our guided workflow takes the guesswork out of content creation
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center">
                <div className="flex justify-center mb-3">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center text-white">
                    {benefit.icon}
                  </div>
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-1">{benefit.value}</div>
                <div className="text-gray-600">{benefit.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Why Writer J Works Better Than Other Tools
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Most AI writing tools give you a blank page and expect magic. 
              We guide you through a proven process that actually works.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-200 border border-gray-100">
                <div className="mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Stop Struggling With Blank Pages
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Get your first article written in under 30 minutes with our guided process.
            Start free - no credit card required.
          </p>
          <Link
            to="/register"
            className="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 text-lg font-semibold rounded-xl hover:bg-gray-50 transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
          >
            Start Writing Your First Article
            <ArrowRightIcon className="w-5 h-5 ml-2" />
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <WriterJLogo className="w-8 h-8" showText={true} textClassName="text-xl font-bold text-white" />
              <p className="text-gray-400 mt-4">
                AI-powered article generation platform for content creators.
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/features" className="hover:text-white transition-colors">Features</Link></li>
                <li><Link to="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><Link to="/register" className="hover:text-white transition-colors">Try Demo</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/about" className="hover:text-white transition-colors">About</Link></li>
                <li><Link to="/blog" className="hover:text-white transition-colors">Blog</Link></li>
                <li><Link to="/contact" className="hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Legal</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/terms" className="hover:text-white transition-colors">Terms of Service</Link></li>
                <li><Link to="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link></li>
                <li><Link to="/refund" className="hover:text-white transition-colors">Refund Policy</Link></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2025 Writer J. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Homepage;
