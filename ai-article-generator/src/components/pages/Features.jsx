import React from 'react';
import { Link } from 'react-router-dom';
import {
  SparklesIcon,
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  MagnifyingGlassIcon,
  BookOpenIcon,
  UserIcon,
  CogIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline';
import Writer<PERSON><PERSON>ogo from '../WriterJLogo';

const Features = () => {
  const features = [
    {
      icon: <MagnifyingGlassIcon className="w-8 h-8 text-blue-600" />,
      title: "Step 1: Keyword Research",
      description: "Start with AI-powered keyword research. Find topics people are actually searching for and understand search intent.",
      benefits: ["Search suggestions from Google", "Related keyword discovery", "Search volume insights", "Question-based keywords"]
    },
    {
      icon: <LightBulbIcon className="w-8 h-8 text-green-600" />,
      title: "Step 2: Topic Selection",
      description: "Choose from AI-generated topic suggestions based on your keywords. Get titles that match search intent.",
      benefits: ["Multiple topic angles", "SEO-optimized headlines", "Intent-based suggestions", "Trending topic alerts"]
    },
    {
      icon: <BookOpenIcon className="w-8 h-8 text-purple-600" />,
      title: "Step 3: Source Integration",
      description: "Add credible sources to support your content. Import information from URLs and organize your research.",
      benefits: ["URL content extraction", "Source organization", "Reference management", "Credibility scoring"]
    },
    {
      icon: <UserIcon className="w-8 h-8 text-indigo-600" />,
      title: "Step 4: Author Profile",
      description: "Build your E-E-A-T authority with detailed author profiles. Establish expertise and trustworthiness.",
      benefits: ["Experience documentation", "Expertise establishment", "Authority building", "Trust signals"]
    },
    {
      icon: <CogIcon className="w-8 h-8 text-orange-600" />,
      title: "Step 5: Style & Format",
      description: "Define your writing style, tone, and formatting preferences. Ensure brand consistency across all content.",
      benefits: ["Voice customization", "Tone selection", "Format templates", "Brand consistency"]
    },
    {
      icon: <SparklesIcon className="w-8 h-8 text-pink-600" />,
      title: "Step 6: AI Generation",
      description: "Generate your complete article using all previous inputs. Get a publication-ready draft in minutes.",
      benefits: ["Full article generation", "SEO optimization", "Structured formatting", "Instant output"]
    },
    {
      icon: <DocumentTextIcon className="w-8 h-8 text-teal-600" />,
      title: "Step 7: Review & Export",
      description: "Review your generated article, make final edits, and export in your preferred format for publishing.",
      benefits: ["Built-in editor", "Multiple export formats", "Final review tools", "Publishing preparation"]
    },
    {
      icon: <ClockIcon className="w-8 h-8 text-gray-600" />,
      title: "Save & Resume Anytime",
      description: "Never lose your progress. Save your work at any step and return exactly where you left off.",
      benefits: ["Auto-save functionality", "Draft management", "Progress tracking", "Multi-device sync"]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link to="/home" className="flex items-center space-x-3">
              <WriterJLogo className="w-10 h-10" showText={true} textClassName="text-2xl font-bold text-gray-900" />
            </Link>
            
            <div className="hidden md:flex items-center space-x-8">
              <Link to="/features" className="text-blue-600 font-medium">Features</Link>
              <Link to="/pricing" className="text-gray-700 hover:text-blue-600 transition-colors">Pricing</Link>
              <Link to="/blog" className="text-gray-700 hover:text-blue-600 transition-colors">Blog</Link>
            </div>
            
            <div className="flex items-center space-x-4">
              <Link
                to="/login"
                className="text-gray-700 hover:text-blue-600 transition-colors"
              >
                Sign In
              </Link>
              <Link
                to="/register"
                className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Try Free
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            The Complete{' '}
            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
              7-Step Writing System
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            No more blank page syndrome. Our structured process takes you from keyword idea 
            to published article in 7 clear steps.
          </p>
          <Link
            to="/register"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white text-lg font-semibold rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
          >
            Try the 7-Step Process Free
          </Link>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-200 border border-gray-100">
                <div className="mb-6">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">{feature.title}</h3>
                <p className="text-gray-600 mb-6">{feature.description}</p>
                <ul className="space-y-2">
                  {feature.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-center text-sm text-gray-600">
                      <CheckCircleIcon className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      {benefit}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Our 7-Step Content Creation Process
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Follow our guided workflow to create professional, engaging articles that rank well in search engines.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              { step: 1, title: "Keyword Research", desc: "Discover opportunities" },
              { step: 2, title: "Topic Selection", desc: "Choose your focus" },
              { step: 3, title: "Source Gathering", desc: "Add credible references" },
              { step: 4, title: "Product Integration", desc: "Optional mentions" },
              { step: 5, title: "Authority Profile", desc: "Establish expertise" },
              { step: 6, title: "Style & Format", desc: "Customize output" },
              { step: 7, title: "AI Generation", desc: "Create your article" }
            ].slice(0, 4).map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center text-white text-xl font-bold mx-auto mb-4">
                  {item.step}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h3>
                <p className="text-gray-600">{item.desc}</p>
              </div>
            ))}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8 max-w-4xl mx-auto">
            {[
              { step: 5, title: "Authority Profile", desc: "Establish expertise" },
              { step: 6, title: "Style & Format", desc: "Customize output" },
              { step: 7, title: "AI Generation", desc: "Create your article" }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center text-white text-xl font-bold mx-auto mb-4">
                  {item.step}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h3>
                <p className="text-gray-600">{item.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Experience These Features?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of content creators who are already using Writer J to produce
            amazing articles faster and more efficiently than ever before.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/register"
              className="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 text-lg font-semibold rounded-xl hover:bg-gray-50 transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
            >
              Start Free Trial
            </Link>
            <Link
              to="/generator"
              className="inline-flex items-center justify-center px-8 py-4 bg-transparent border-2 border-white text-white text-lg font-semibold rounded-xl hover:bg-white hover:text-blue-600 transition-all duration-200"
            >
              Try Demo
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <WriterJLogo className="w-8 h-8" showText={true} textClassName="text-xl font-bold text-white" />
              <p className="text-gray-400 mt-4">
                AI-powered article generation platform for content creators.
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/features" className="hover:text-white transition-colors">Features</Link></li>
                <li><Link to="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><Link to="/generator" className="hover:text-white transition-colors">Try Demo</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/about" className="hover:text-white transition-colors">About</Link></li>
                <li><Link to="/blog" className="hover:text-white transition-colors">Blog</Link></li>
                <li><Link to="/contact" className="hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Legal</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link></li>
                <li><Link to="/terms" className="hover:text-white transition-colors">Terms of Service</Link></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Writer J. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Features;
