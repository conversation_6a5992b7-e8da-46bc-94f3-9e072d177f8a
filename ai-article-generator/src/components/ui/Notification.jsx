import React, { useState, useEffect } from 'react';
import {
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

const Notification = ({ type, title, message, onClose, autoClose = true, duration = 5000 }) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (autoClose) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [autoClose, duration]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      if (onClose) onClose();
    }, 300); // Wait for animation to complete
  };

  const getNotificationStyles = () => {
    const baseStyles = "fixed top-4 right-4 max-w-md w-full bg-white rounded-lg shadow-lg border-l-4 p-4 transition-all duration-300 transform z-50";
    
    if (!isVisible) {
      return `${baseStyles} translate-x-full opacity-0`;
    }

    const typeStyles = {
      success: "border-green-500",
      error: "border-red-500", 
      warning: "border-yellow-500",
      info: "border-blue-500"
    };

    return `${baseStyles} translate-x-0 opacity-100 ${typeStyles[type] || typeStyles.info}`;
  };

  const getIcon = () => {
    const iconStyles = "w-5 h-5 flex-shrink-0";
    
    switch (type) {
      case 'success':
        return <CheckCircleIcon className={`${iconStyles} text-green-500`} />;
      case 'error':
        return <XCircleIcon className={`${iconStyles} text-red-500`} />;
      case 'warning':
        return <ExclamationTriangleIcon className={`${iconStyles} text-yellow-500`} />;
      case 'info':
      default:
        return <InformationCircleIcon className={`${iconStyles} text-blue-500`} />;
    }
  };

  const getTitleColor = () => {
    const colors = {
      success: "text-green-800",
      error: "text-red-800",
      warning: "text-yellow-800", 
      info: "text-blue-800"
    };
    return colors[type] || colors.info;
  };

  return (
    <div className={getNotificationStyles()}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {getIcon()}
        </div>
        <div className="ml-3 flex-1">
          {title && (
            <h3 className={`text-sm font-medium ${getTitleColor()}`}>
              {title}
            </h3>
          )}
          {message && (
            <p className="text-sm text-gray-600 mt-1">
              {message}
            </p>
          )}
        </div>
        <div className="ml-4 flex-shrink-0">
          <button
            onClick={handleClose}
            className="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none transition-colors"
          >
            <XMarkIcon className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Notification;