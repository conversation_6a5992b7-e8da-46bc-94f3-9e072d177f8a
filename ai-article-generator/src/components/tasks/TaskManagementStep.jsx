import React, { useState, useEffect } from 'react';
import {
  LinkIcon,
  UserIcon,
  DocumentTextIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  StarIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';
import { useAuth } from '../../contexts/AuthContext';
import { API_CONFIG, presetService } from '../../config/api';

const TaskManagementStep = ({ 
  data, 
  updateData, 
  title, 
  subtitle, 
  stepNumber, 
  totalSteps, 
  type = 'resources' // 'resources', 'author', 'product'
}) => {
  const { authenticatedFetch } = useAuth();
  const [loading, setLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState(null);

  // Data states
  const [resources, setResources] = useState([]);
  const [authors, setAuthors] = useState([]);
  const [products, setProducts] = useState([]);

  // Form states based on type
  const [resourceForm, setResourceForm] = useState({
    resource_name: '',
    resource_type: 'url',
    resource_url: '',
    resource_content: '',
    description: '',
    tags: []
  });

  const [authorForm, setAuthorForm] = useState({
    presetName: '',
    presetData: {
      authorName: '',
      authorBio: '',
      targetAudience: '',
      articleGoal: ''
    }
  });

  const [productForm, setProductForm] = useState({
    presetName: '',
    presetData: {
      name: '',
      link: '',
      description: '',
      features: []
    }
  });

  useEffect(() => {
    loadData();
  }, [type]);

  const loadData = async () => {
    setLoading(true);
    try {
      if (type === 'resources') {
        const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.RESOURCES.LIST);
        if (response.ok) {
          const data = await response.json();
          setResources(data.resources || []);
        }
      } else if (type === 'author') {
        const authorsData = await presetService.getPresets('author');
        setAuthors(authorsData);
      } else if (type === 'product') {
        const productsData = await presetService.getPresets('product');
        setProducts(productsData);
      }
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Get current selections from task data
  const getCurrentSelections = () => {
    if (type === 'resources') {
      return data.comprehensiveResources || [];
    } else if (type === 'author') {
      return data.eeatProfile || null;
    } else if (type === 'product') {
      return data.productInfo || null;
    }
    return null;
  };

  // Handle selections
  const handleResourceSelect = (resource) => {
    const currentResources = data.comprehensiveResources || [];
    const isSelected = currentResources.some(r => r.id === resource.id);
    
    let newResources;
    if (isSelected) {
      newResources = currentResources.filter(r => r.id !== resource.id);
    } else {
      // Convert resource to task format
      const taskResource = {
        id: resource.id,
        type: 'saved_resource',
        resource_name: resource.resource_name,
        resource_type: resource.resource_type,
        url: resource.resource_url,
        content: resource.resource_content || '',
        description: resource.description || '',
        addedAt: new Date().toISOString()
      };
      newResources = [...currentResources, taskResource];
      
      // Mark resource as used
      authenticatedFetch(API_CONFIG.ENDPOINTS.RESOURCES.USE(resource.id), {
        method: 'POST'
      }).catch(console.error);
    }
    
    updateData({ comprehensiveResources: newResources });
  };

  const handleAuthorSelect = (author) => {
    updateData({
      eeatProfile: {
        authorName: author.presetData.authorName || '',
        authorBio: author.presetData.authorBio || '',
        targetAudience: author.presetData.targetAudience || '',
        articleGoal: author.presetData.articleGoal || ''
      }
    });
  };

  const handleProductSelect = (product) => {
    updateData({
      productInfo: {
        name: product.presetData.name || '',
        link: product.presetData.link || '',
        description: product.presetData.description || '',
        features: product.presetData.features || []
      }
    });
  };

  // Form submission handlers
  const handleResourceSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingItem) {
        const response = await authenticatedFetch(
          API_CONFIG.ENDPOINTS.RESOURCES.UPDATE(editingItem.id),
          {
            method: 'PUT',
            body: JSON.stringify(resourceForm)
          }
        );
        if (response.ok) {
          await loadData();
          resetResourceForm();
        }
      } else {
        const response = await authenticatedFetch(
          API_CONFIG.ENDPOINTS.RESOURCES.CREATE,
          {
            method: 'POST',
            body: JSON.stringify(resourceForm)
          }
        );
        if (response.ok) {
          await loadData();
          resetResourceForm();
        }
      }
    } catch (error) {
      console.error('Failed to save resource:', error);
    }
  };

  const handleAuthorSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingItem) {
        await presetService.updatePreset(editingItem.id, authorForm.presetName, authorForm.presetData);
      } else {
        await presetService.createPreset('author', authorForm.presetName, authorForm.presetData);
      }
      await loadData();
      resetAuthorForm();
    } catch (error) {
      console.error('Failed to save author:', error);
    }
  };

  const handleProductSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingItem) {
        await presetService.updatePreset(editingItem.id, productForm.presetName, productForm.presetData);
      } else {
        await presetService.createPreset('product', productForm.presetName, productForm.presetData);
      }
      await loadData();
      resetProductForm();
    } catch (error) {
      console.error('Failed to save product:', error);
    }
  };

  // Form reset functions
  const resetResourceForm = () => {
    setResourceForm({
      resource_name: '',
      resource_type: 'url',
      resource_url: '',
      resource_content: '',
      description: '',
      tags: []
    });
    setShowForm(false);
    setEditingItem(null);
  };

  const resetAuthorForm = () => {
    setAuthorForm({
      presetName: '',
      presetData: {
        authorName: '',
        authorBio: '',
        targetAudience: '',
        articleGoal: ''
      }
    });
    setShowForm(false);
    setEditingItem(null);
  };

  const resetProductForm = () => {
    setProductForm({
      presetName: '',
      presetData: {
        name: '',
        link: '',
        description: '',
        features: []
      }
    });
    setShowForm(false);
    setEditingItem(null);
  };

  // Edit handlers
  const editResource = (resource) => {
    setResourceForm({
      resource_name: resource.resource_name,
      resource_type: resource.resource_type,
      resource_url: resource.resource_url || '',
      resource_content: resource.resource_content || '',
      description: resource.description || '',
      tags: resource.tags || []
    });
    setEditingItem(resource);
    setShowForm(true);
  };

  const editAuthor = (author) => {
    setAuthorForm({
      presetName: author.presetName,
      presetData: author.presetData
    });
    setEditingItem(author);
    setShowForm(true);
  };

  const editProduct = (product) => {
    setProductForm({
      presetName: product.presetName,
      presetData: product.presetData
    });
    setEditingItem(product);
    setShowForm(true);
  };

  // Delete handler
  const handleDelete = async (item) => {
    if (!confirm('Are you sure you want to delete this item?')) return;
    
    try {
      if (type === 'resources') {
        await authenticatedFetch(API_CONFIG.ENDPOINTS.RESOURCES.DELETE(item.id), { method: 'DELETE' });
      } else {
        await presetService.deletePreset(item.id);
      }
      await loadData();
    } catch (error) {
      console.error('Failed to delete item:', error);
    }
  };

  const currentSelections = getCurrentSelections();

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">{title}</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          <span className="font-semibold text-blue-600">Step {stepNumber} of {totalSteps}:</span> {subtitle}
        </p>
      </div>

      {/* Current Selection Summary */}
      {type === 'resources' && currentSelections.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-medium text-green-800 mb-2 flex items-center">
            <CheckCircleIcon className="h-5 w-5 mr-2" />
            Selected Resources ({currentSelections.length})
          </h3>
          <div className="space-y-1">
            {currentSelections.slice(0, 3).map((resource, index) => (
              <div key={index} className="text-sm text-green-700">
                • {resource.resource_name || resource.title || 'Unnamed Resource'}
              </div>
            ))}
            {currentSelections.length > 3 && (
              <div className="text-sm text-green-600">+{currentSelections.length - 3} more...</div>
            )}
          </div>
        </div>
      )}

      {type === 'author' && currentSelections && currentSelections.authorName && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-medium text-green-800 mb-2 flex items-center">
            <CheckCircleIcon className="h-5 w-5 mr-2" />
            Selected Author Profile
          </h3>
          <div className="text-sm text-green-700">
            {currentSelections.authorName} - {currentSelections.targetAudience}
          </div>
        </div>
      )}

      {type === 'product' && currentSelections && currentSelections.name && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-medium text-green-800 mb-2 flex items-center">
            <CheckCircleIcon className="h-5 w-5 mr-2" />
            Selected Product
          </h3>
          <div className="text-sm text-green-700">
            {currentSelections.name}
          </div>
        </div>
      )}

      {/* Add/Create Section */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">
          {type === 'resources' && 'Available Resources'}
          {type === 'author' && 'Author Profiles'}
          {type === 'product' && 'Product Information'}
        </h3>
        <button
          onClick={() => setShowForm(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add New</span>
        </button>
      </div>

      {/* Form */}
      {showForm && (
        <div className="bg-gray-50 rounded-lg p-6">
          {type === 'resources' && (
            <ResourceForm
              formData={resourceForm}
              setFormData={setResourceForm}
              onSubmit={handleResourceSubmit}
              onCancel={resetResourceForm}
              editingItem={editingItem}
            />
          )}
          {type === 'author' && (
            <AuthorForm
              formData={authorForm}
              setFormData={setAuthorForm}
              onSubmit={handleAuthorSubmit}
              onCancel={resetAuthorForm}
              editingItem={editingItem}
            />
          )}
          {type === 'product' && (
            <ProductForm
              formData={productForm}
              setFormData={setProductForm}
              onSubmit={handleProductSubmit}
              onCancel={resetProductForm}
              editingItem={editingItem}
            />
          )}
        </div>
      )}

      {/* Content Grid */}
      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {type === 'resources' && resources.map((resource) => (
            <ResourceCard
              key={resource.id}
              resource={resource}
              isSelected={currentSelections.some(r => r.id === resource.id)}
              onSelect={() => handleResourceSelect(resource)}
              onEdit={() => editResource(resource)}
              onDelete={() => handleDelete(resource)}
            />
          ))}
          
          {type === 'author' && authors.map((author) => (
            <AuthorCard
              key={author.id}
              author={author}
              isSelected={currentSelections && currentSelections.authorName === author.presetData.authorName}
              onSelect={() => handleAuthorSelect(author)}
              onEdit={() => editAuthor(author)}
              onDelete={() => handleDelete(author)}
            />
          ))}
          
          {type === 'product' && products.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              isSelected={currentSelections && currentSelections.name === product.presetData.name}
              onSelect={() => handleProductSelect(product)}
              onEdit={() => editProduct(product)}
              onDelete={() => handleDelete(product)}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Card Components
const ResourceCard = ({ resource, isSelected, onSelect, onEdit, onDelete }) => (
  <div 
    className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
      isSelected ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:border-gray-300'
    }`}
    onClick={onSelect}
  >
    <div className="flex items-start justify-between mb-2">
      <h4 className="font-medium text-gray-900 truncate">{resource.resource_name}</h4>
      <div className="flex items-center space-x-1">
        <button 
          onClick={(e) => {
            e.stopPropagation();
            onSelect();
          }} 
          className="text-indigo-600 hover:text-indigo-800"
        >
          {isSelected ? <StarSolidIcon className="h-4 w-4" /> : <StarIcon className="h-4 w-4" />}
        </button>
        <button 
          onClick={(e) => {
            e.stopPropagation();
            onEdit();
          }} 
          className="text-gray-600 hover:text-gray-800"
        >
          <PencilIcon className="h-4 w-4" />
        </button>
        <button 
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
          }} 
          className="text-red-600 hover:text-red-800"
        >
          <TrashIcon className="h-4 w-4" />
        </button>
      </div>
    </div>
    <p className="text-sm text-gray-600 mb-2">{resource.description}</p>
    <div className="flex items-center justify-between text-xs text-gray-500">
      <span className="bg-gray-100 px-2 py-1 rounded">{resource.resource_type}</span>
      <span>Used {resource.usage_count} times</span>
    </div>
  </div>
);

const AuthorCard = ({ author, isSelected, onSelect, onEdit, onDelete }) => (
  <div 
    className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
      isSelected ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:border-gray-300'
    }`}
    onClick={onSelect}
  >
    <div className="flex items-start justify-between mb-2">
      <h4 className="font-medium text-gray-900">{author.presetName}</h4>
      <div className="flex items-center space-x-1">
        <button 
          onClick={(e) => {
            e.stopPropagation();
            onSelect();
          }} 
          className="text-indigo-600 hover:text-indigo-800"
        >
          {isSelected ? <StarSolidIcon className="h-4 w-4" /> : <StarIcon className="h-4 w-4" />}
        </button>
        <button 
          onClick={(e) => {
            e.stopPropagation();
            onEdit();
          }} 
          className="text-gray-600 hover:text-gray-800"
        >
          <PencilIcon className="h-4 w-4" />
        </button>
        <button 
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
          }} 
          className="text-red-600 hover:text-red-800"
        >
          <TrashIcon className="h-4 w-4" />
        </button>
      </div>
    </div>
    <p className="text-sm text-gray-600 mb-1">{author.presetData.authorName}</p>
    <p className="text-xs text-gray-500 truncate">{author.presetData.authorBio}</p>
  </div>
);

const ProductCard = ({ product, isSelected, onSelect, onEdit, onDelete }) => (
  <div 
    className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
      isSelected ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:border-gray-300'
    }`}
    onClick={onSelect}
  >
    <div className="flex items-start justify-between mb-2">
      <h4 className="font-medium text-gray-900">{product.presetName}</h4>
      <div className="flex items-center space-x-1">
        <button 
          onClick={(e) => {
            e.stopPropagation();
            onSelect();
          }} 
          className="text-indigo-600 hover:text-indigo-800"
        >
          {isSelected ? <StarSolidIcon className="h-4 w-4" /> : <StarIcon className="h-4 w-4" />}
        </button>
        <button 
          onClick={(e) => {
            e.stopPropagation();
            onEdit();
          }} 
          className="text-gray-600 hover:text-gray-800"
        >
          <PencilIcon className="h-4 w-4" />
        </button>
        <button 
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
          }} 
          className="text-red-600 hover:text-red-800"
        >
          <TrashIcon className="h-4 w-4" />
        </button>
      </div>
    </div>
    <p className="text-sm text-gray-600 mb-1">{product.presetData.name}</p>
    <p className="text-xs text-gray-500 truncate">{product.presetData.description}</p>
  </div>
);

// Form Components
const ResourceForm = ({ formData, setFormData, onSubmit, onCancel, editingItem }) => (
  <div>
    <h4 className="font-semibold text-gray-900 mb-4">
      {editingItem ? 'Edit Resource' : 'Add New Resource'}
    </h4>
    <form onSubmit={onSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Resource Name</label>
          <input
            type="text"
            value={formData.resource_name}
            onChange={(e) => setFormData({...formData, resource_name: e.target.value})}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
          <select
            value={formData.resource_type}
            onChange={(e) => setFormData({...formData, resource_type: e.target.value})}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          >
            <option value="url">URL</option>
            <option value="reddit">Reddit</option>
            <option value="document">Document</option>
            <option value="manual">Manual</option>
          </select>
        </div>
      </div>
      
      {(formData.resource_type === 'url' || formData.resource_type === 'reddit') && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">URL</label>
          <input
            type="url"
            value={formData.resource_url}
            onChange={(e) => setFormData({...formData, resource_url: e.target.value})}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            required
          />
        </div>
      )}
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({...formData, description: e.target.value})}
          rows={3}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
        />
      </div>
      
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
        >
          {editingItem ? 'Update' : 'Create'}
        </button>
      </div>
    </form>
  </div>
);

const AuthorForm = ({ formData, setFormData, onSubmit, onCancel, editingItem }) => (
  <div>
    <h4 className="font-semibold text-gray-900 mb-4">
      {editingItem ? 'Edit Author Profile' : 'Add New Author Profile'}
    </h4>
    <form onSubmit={onSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Profile Name</label>
        <input
          type="text"
          value={formData.presetName}
          onChange={(e) => setFormData({...formData, presetName: e.target.value})}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          required
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Author Name</label>
          <input
            type="text"
            value={formData.presetData.authorName}
            onChange={(e) => setFormData({
              ...formData,
              presetData: {...formData.presetData, authorName: e.target.value}
            })}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Target Audience</label>
          <input
            type="text"
            value={formData.presetData.targetAudience}
            onChange={(e) => setFormData({
              ...formData,
              presetData: {...formData.presetData, targetAudience: e.target.value}
            })}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          />
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Author Bio</label>
        <textarea
          value={formData.presetData.authorBio}
          onChange={(e) => setFormData({
            ...formData,
            presetData: {...formData.presetData, authorBio: e.target.value}
          })}
          rows={3}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Article Goal</label>
        <textarea
          value={formData.presetData.articleGoal}
          onChange={(e) => setFormData({
            ...formData,
            presetData: {...formData.presetData, articleGoal: e.target.value}
          })}
          rows={2}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
        />
      </div>
      
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
        >
          {editingItem ? 'Update' : 'Create'}
        </button>
      </div>
    </form>
  </div>
);

const ProductForm = ({ formData, setFormData, onSubmit, onCancel, editingItem }) => {
  const addFeature = () => {
    setFormData({
      ...formData,
      presetData: {
        ...formData.presetData,
        features: [...formData.presetData.features, '']
      }
    });
  };

  const updateFeature = (index, value) => {
    const newFeatures = [...formData.presetData.features];
    newFeatures[index] = value;
    setFormData({
      ...formData,
      presetData: {
        ...formData.presetData,
        features: newFeatures
      }
    });
  };

  const removeFeature = (index) => {
    setFormData({
      ...formData,
      presetData: {
        ...formData.presetData,
        features: formData.presetData.features.filter((_, i) => i !== index)
      }
    });
  };

  return (
    <div>
      <h4 className="font-semibold text-gray-900 mb-4">
        {editingItem ? 'Edit Product' : 'Add New Product'}
      </h4>
      <form onSubmit={onSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Profile Name</label>
          <input
            type="text"
            value={formData.presetName}
            onChange={(e) => setFormData({...formData, presetName: e.target.value})}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            required
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Product Name</label>
            <input
              type="text"
              value={formData.presetData.name}
              onChange={(e) => setFormData({
                ...formData,
                presetData: {...formData.presetData, name: e.target.value}
              })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Product Link</label>
            <input
              type="url"
              value={formData.presetData.link}
              onChange={(e) => setFormData({
                ...formData,
                presetData: {...formData.presetData, link: e.target.value}
              })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea
            value={formData.presetData.description}
            onChange={(e) => setFormData({
              ...formData,
              presetData: {...formData.presetData, description: e.target.value}
            })}
            rows={3}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          />
        </div>
        
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium text-gray-700">Features</label>
            <button
              type="button"
              onClick={addFeature}
              className="text-sm text-indigo-600 hover:text-indigo-800"
            >
              + Add Feature
            </button>
          </div>
          {formData.presetData.features.map((feature, index) => (
            <div key={index} className="flex items-center space-x-2 mb-2">
              <input
                type="text"
                value={feature}
                onChange={(e) => updateFeature(index, e.target.value)}
                className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                placeholder="Feature description"
              />
              <button
                type="button"
                onClick={() => removeFeature(index)}
                className="text-red-600 hover:text-red-800"
              >
                <TrashIcon className="h-4 w-4" />
              </button>
            </div>
          ))}
        </div>
        
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
          >
            {editingItem ? 'Update' : 'Create'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default TaskManagementStep;