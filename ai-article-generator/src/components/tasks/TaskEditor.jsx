import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { ChevronRightIcon, ChevronLeftIcon, SparklesIcon, ArrowLeftIcon, CheckIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import StepIndicator from '../StepIndicator';
import Step0KeywordResearch from '../Step0KeywordResearch';
import Step1TopicSelection from '../Step1TopicSelection';
import Step2Sources from '../Step2Sources';
import Step3Product from '../Step3Product';
import Step4EEAT from '../Step4EEAT';
import TaskManagementStep from './TaskManagementStep';
import Step4Parameters from '../Step4Parameters';
import Step5Generation from '../Step5Generation';
import { API_CONFIG } from '../../config/api';

const STEPS = [
  { id: 1, title: 'Keyword Research', subtitle: 'Discover content opportunities', component: Step0KeywordResearch, icon: '🔍' },
  { id: 2, title: 'Choose Topics', subtitle: 'Define your article focus', component: Step1TopicSelection, icon: '🎯' },
  { id: 3, title: 'Source Integration', subtitle: 'Add research materials and Reddit insights', component: Step2Sources, icon: '📚' },
  { id: 4, title: 'Product Integration', subtitle: 'Select product to mention (optional)', component: TaskManagementStep, componentProps: { type: 'product' }, icon: '🏷️' },
  { id: 5, title: 'Authority Profile', subtitle: 'Choose your author profile', component: TaskManagementStep, componentProps: { type: 'author' }, icon: '👤' },
  { id: 6, title: 'Style & Format', subtitle: 'Customize writing style', component: Step4Parameters, icon: '⚙️' },
  { id: 7, title: 'Generate Article', subtitle: 'Create your content', component: Step5Generation, icon: '✨' },
];

// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Step component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || <div>Something went wrong.</div>;
    }
    return this.props.children;
  }
}

// Error Fallback Component
const StepErrorFallback = ({ stepTitle }) => (
  <div className="flex flex-col items-center justify-center py-12">
    <ExclamationTriangleIcon className="w-16 h-16 text-red-500 mb-4" />
    <h3 className="text-lg font-semibold text-gray-900 mb-2">Step Error</h3>
    <p className="text-gray-600 text-center mb-4">
      There was an error loading the "{stepTitle}" step.
    </p>
    <button
      onClick={() => window.location.reload()}
      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
    >
      Reload Page
    </button>
  </div>
);

const TaskEditor = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();
  const { authenticatedFetch } = useAuth();

  const [task, setTask] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [isGeneratingTopics, setIsGeneratingTopics] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [isFinishing, setIsFinishing] = useState(false);

  useEffect(() => {
    if (taskId) {
      fetchTask();
    }
  }, [taskId]);

  const fetchTask = async () => {
    try {
      const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.TASKS.GET(taskId));

      if (response.ok) {
        const data = await response.json();
        setTask(data.task);

        // If task has generated article, automatically go to final step
        if (data.task.generated_article) {
          setCurrentStep(STEPS.length - 1);
        } else {
          setCurrentStep(data.task.current_step || 0);
        }
      } else {
        console.error('Failed to fetch task');
        navigate('/tasks');
      }
    } catch (error) {
      console.error('Error fetching task:', error);
      navigate('/tasks');
    } finally {
      setLoading(false);
    }
  };

  const saveTask = async (updates = {}) => {
    if (!task) return;


    setSaving(true);
    try {
      const requestBody = {
        currentStep,
        status: getTaskStatus(),
        redditSources: task.redditSources || [],
        ...updates
      };

      const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.TASKS.UPDATE(taskId), {
        method: 'PUT',
        body: JSON.stringify(requestBody)
      });

      if (response.ok) {
        const data = await response.json();
        // 不要完全覆盖本地状态，只更新保存时间等服务器管理的字段
        setTask(prevTask => ({
          ...prevTask,
          // 只更新服务器管理的字段，保留本地最新的数据状态
          id: data.task.id,
          updated_at: data.task.updated_at,
          created_at: data.task.created_at,
          // 不更新 keywordResearchSelections 等用户操作的数据
        }));
        setLastSaved(new Date());
      } else {
        const errorData = await response.text();
        // Save failed silently
      }
    } catch (error) {
      // Save error silently
    } finally {
      setSaving(false);
    }
  };

  const getTaskStatus = () => {
    if (!task) return 'Draft - Step 1';

    // Only show "Completed" if the task status is actually "Completed" (after user clicks Finish)
    if (task.status === 'Completed') {
      return 'Completed';
    } else if (task.generated_article && currentStep === STEPS.length - 1) {
      return 'Ready to Finish';
    } else if (currentStep === STEPS.length - 1) {
      return 'Ready to Generate';
    } else {
      return `Draft - Step ${currentStep + 1}`;
    }
  };

  const getTaskProgress = () => {
    if (!task) return 0;

    // Only show 100% if task is actually completed (after user clicks Finish)
    if (task.status === 'Completed') {
      return 100;
    }

    // If article is generated but not finished, show 95%
    if (task.generated_article) {
      return 95;
    }

    // Otherwise, calculate based on current step
    const progress = ((currentStep + 1) / STEPS.length) * 90; // Max 90% until finished
    return progress;
  };

  const updateArticleData = (updates) => {
    if (!task) return;

    // Debug: Check for invalid update objects
    if (updates && typeof updates === 'object' && updates.original && updates.edited) {
      console.error('Invalid update object detected with original/edited keys:', updates);
      return; // Prevent processing invalid updates
    }

    // 如果updates是函数，先执行它获取真正的更新数据
    let actualUpdates = updates;
    if (typeof updates === 'function') {
      actualUpdates = updates(task);
    }

    // Double-check after function execution
    if (actualUpdates && typeof actualUpdates === 'object' && actualUpdates.original && actualUpdates.edited) {
      console.error('Invalid actualUpdates object detected with original/edited keys:', actualUpdates);
      return; // Prevent processing invalid updates
    }

    const updatedTask = {
      ...task,
      ...actualUpdates
    };

    setTask(updatedTask);

    // If article is generated, automatically go to final step
    if (actualUpdates.generated_article && currentStep !== STEPS.length - 1) {
      setCurrentStep(STEPS.length - 1);
      actualUpdates.currentStep = STEPS.length - 1;
    }

    // Auto-save after a short delay
    setTimeout(() => {
      saveTask(actualUpdates);
    }, 1000);
  };

  const nextStep = () => {
    if (currentStep < STEPS.length - 1) {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);
      saveTask({ currentStep: newStep });
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      const newStep = currentStep - 1;
      setCurrentStep(newStep);
      saveTask({ currentStep: newStep });
    }
  };

  const finishTask = async () => {
    if (isFinishing) return; // Prevent multiple clicks

    setIsFinishing(true);
    try {
      const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.TASKS.FINISH(taskId), {
        method: 'POST'
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Failed to finish task: ${response.status} ${errorData}`);
      }

      const result = await response.json();

      // Update with the complete finished task data
      setTask(result.task);

      // Force a re-render to update progress and status
      setTimeout(() => {
        setTask(prevTask => ({ ...prevTask, ...result.task }));
      }, 100);

      // Show success message
      alert('🎉 Task completed successfully! The article has been finalized and the task is now complete.');

    } catch (error) {
      alert('Failed to finish task. Please try again.');
    } finally {
      setIsFinishing(false);
    }
  };

  const goToStep = (stepIndex) => {
    setCurrentStep(stepIndex);
    saveTask({ currentStep: stepIndex });
  };

  const canProceedToNext = () => {
    if (!task) return false;

    switch (currentStep) {
      case 0:
        return true; // Keyword research is optional
      case 1:
        return task.selectedTopics && task.selectedTopics.length > 0;
      case 2:
        return true; // Sources are optional
      case 3:
        return true; // Product info is optional
      case 4:
        return true; // E-E-A-T is optional
      case 5:
        return true; // Parameters have defaults
      case 6:
        return true; // Final step
      default:
        return false;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading task...</p>
        </div>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Task Not Found</h2>
          <p className="text-gray-600 mb-4">The requested task could not be found.</p>
          <button
            onClick={() => navigate('/tasks')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Tasks
          </button>
        </div>
      </div>
    );
  }

  const CurrentStepComponent = STEPS[currentStep].component;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50 shadow-lg shadow-blue-500/5">
        <div className="w-full px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/tasks')}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white/50 rounded-lg transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5" />
              </button>
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                  <SparklesIcon className="w-7 h-7 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-800 bg-clip-text text-transparent">
                  {task.name}
                </h1>
                <p className="text-sm text-gray-600 font-medium">
                  {getTaskStatus()} • Step {currentStep + 1} of {STEPS.length}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {saving && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-600"></div>
                  <span>Saving...</span>
                </div>
              )}
              {lastSaved && !saving && (
                <div className="text-sm text-gray-500">
                  Saved {lastSaved.toLocaleTimeString()}
                </div>
              )}
              <div className="hidden md:flex items-center space-x-2 px-4 py-2 bg-white/60 rounded-full">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-gray-700">AI Ready</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="w-full py-8">
        <div className="flex min-h-screen">
          {/* Left Sidebar - Step Indicator */}
          <div className="w-64 flex-shrink-0 px-4">
            <div className="sticky top-32">
              <StepIndicator
                steps={STEPS}
                currentStep={currentStep}
                onStepClick={goToStep}
                isTaskCompleted={task?.status === 'Completed'}
              />
            </div>
          </div>

          {/* Main Content Area - Full Width */}
          <div className="flex-1 px-6">
            <div className="bg-white/70 backdrop-blur-sm rounded-3xl shadow-2xl shadow-blue-500/10 border border-white/30 overflow-hidden">
              {/* Step Header */}
              <div className="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 px-8 py-6 border-b border-white/30">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-1">
                      {STEPS[currentStep].title}
                    </h2>
                    <p className="text-gray-600 font-medium">
                      {STEPS[currentStep].subtitle}
                    </p>
                  </div>
                  <div className="text-4xl opacity-80">
                    {STEPS[currentStep].icon}
                  </div>
                </div>
              </div>

              {/* Step Content */}
              <div className="relative p-8">
                {/* Error Boundary for Step Components */}
                <ErrorBoundary fallback={<StepErrorFallback stepTitle={STEPS[currentStep].title} />}>
                  <CurrentStepComponent
                    data={task}
                    updateData={updateArticleData}
                    onNext={nextStep}
                    onPrev={prevStep}
                    isFirstStep={currentStep === 0}
                    isLastStep={currentStep === STEPS.length - 1}
                    isGeneratingTopics={isGeneratingTopics}
                    setIsGeneratingTopics={setIsGeneratingTopics}
                    taskId={taskId}
                    // Additional props for TaskManagementStep
                    title={STEPS[currentStep].title}
                    subtitle={STEPS[currentStep].subtitle}
                    stepNumber={currentStep + 1}
                    totalSteps={STEPS.length}
                    {...(STEPS[currentStep].componentProps || {})}
                  />
                </ErrorBoundary>
              </div>

              {/* Navigation */}
              <div className="bg-gradient-to-r from-gray-50 via-blue-50 to-indigo-50 px-8 py-6 border-t border-white/30">
                <div className="flex justify-between items-center">
                  <button
                    onClick={prevStep}
                    disabled={currentStep === 0}
                    className="flex items-center space-x-2 px-6 py-3 bg-white/80 text-gray-700 rounded-xl font-medium hover:bg-white hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 border border-gray-200/50"
                  >
                    <ChevronLeftIcon className="w-4 h-4" />
                    <span>Previous</span>
                  </button>

                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600 font-medium">
                      Step {currentStep + 1} of {STEPS.length}
                    </span>
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${getTaskProgress()}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Show Finish button on last step with generated article, otherwise show Next */}
                  {currentStep === STEPS.length - 1 && task?.generated_article ? (
                    <button
                      onClick={finishTask}
                      disabled={isFinishing || task?.status === 'Completed'}
                      className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg ${
                        isFinishing || task?.status === 'Completed'
                          ? 'bg-gray-400 cursor-not-allowed'
                          : 'bg-gradient-to-r from-green-600 to-emerald-600 text-white hover:from-green-700 hover:to-emerald-700 hover:shadow-lg'
                      }`}
                    >
                      {isFinishing ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          <span>Finishing...</span>
                        </>
                      ) : task?.status === 'Completed' ? (
                        <>
                          <CheckIcon className="w-4 h-4" />
                          <span>Completed</span>
                        </>
                      ) : (
                        <>
                          <span>Finish</span>
                          <CheckIcon className="w-4 h-4" />
                        </>
                      )}
                    </button>
                  ) : (
                    <button
                      onClick={nextStep}
                      disabled={currentStep === STEPS.length - 1 || !canProceedToNext()}
                      className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium hover:from-blue-700 hover:to-indigo-700 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
                    >
                      <span>Next</span>
                      <ChevronRightIcon className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default TaskEditor;
