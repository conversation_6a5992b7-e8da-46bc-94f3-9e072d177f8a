import React, { useState, useEffect } from 'react';
import { 
  CogIcon, 
  DocumentTextIcon, 
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowDownTrayIcon,
  UserIcon,
  BugAntIcon
} from '@heroicons/react/24/outline';
import { buildApiUrl, API_CONFIG } from '../../config/api';
import { Link } from 'react-router-dom';

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('models');
  const [models, setModels] = useState([]);
  const [prompts, setPrompts] = useState([]);
  const [stats, setStats] = useState({});
  const [tasks, setTasks] = useState([]);
  const [selectedTask, setSelectedTask] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [testingModel, setTestingModel] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [exportingTasks, setExportingTasks] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    if (activeTab === 'tasks' && tasks.length === 0) {
      loadTasksData();
    }
  }, [activeTab]);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('token');
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      };

      const [modelsRes, statsRes] = await Promise.all([
        fetch(buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.MODELS), { headers }),
        fetch(buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.STATS), { headers })
      ]);

      if (!modelsRes.ok || !statsRes.ok) {
        throw new Error('Failed to load admin data');
      }

      const [modelsData, statsData] = await Promise.all([
        modelsRes.json(),
        statsRes.json()
      ]);

      setModels(modelsData.models || []);
      setPrompts([]); // Prompts are now managed in code
      setStats(statsData.stats || {});
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const updateModel = async (modelName, updates) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.UPDATE_MODEL(modelName)), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        throw new Error('Failed to update model');
      }

      await loadData(); // Reload data
    } catch (err) {
      setError(err.message);
    }
  };

  const updatePrompt = async (id, updates) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.UPDATE_PROMPT(id)), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        throw new Error('Failed to update prompt');
      }

      setSuccessMessage('Prompt updated successfully and applied immediately!');
      setTimeout(() => setSuccessMessage(null), 5000); // Clear after 5 seconds
      await loadData(); // Reload data
    } catch (err) {
      setError(err.message);
    }
  };

  const testModel = async (modelName) => {
    setTestingModel(modelName);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.TEST_MODEL(modelName)), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();

      if (result.success) {
        alert(`✅ ${result.message}`);
      } else {
        alert(`❌ ${result.message}`);
      }
    } catch (err) {
      alert(`❌ Test failed: ${err.message}`);
    } finally {
      setTestingModel(null);
    }
  };

  const exportTasks = async () => {
    setExportingTasks(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(buildApiUrl('/api/admin/tasks/export'), {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('导出失败');
      }

      // Create blob from response
      const blob = await response.blob();
      
      // Get filename from response headers or create default
      const contentDisposition = response.headers.get('content-disposition');
      let filename = 'tasks_export.csv';
      if (contentDisposition) {
        const matches = contentDisposition.match(/filename="([^"]+)"/);
        if (matches) {
          filename = matches[1];
        }
      }

      // Create download link and trigger download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      setSuccessMessage('Tasks数据导出成功！');
      setTimeout(() => setSuccessMessage(null), 5000);
    } catch (err) {
      setError(`导出失败: ${err.message}`);
    } finally {
      setExportingTasks(false);
    }
  };

  const loadTasksData = async () => {
    try {
      const token = localStorage.getItem('token');
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      };

      const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.TASKS), { headers });
      if (!response.ok) {
        throw new Error('Failed to load tasks data');
      }

      const data = await response.json();
      setTasks(data.tasks || []);
    } catch (err) {
      setError(err.message);
    }
  };

  const loadTaskDebug = async (taskId) => {
    try {
      const token = localStorage.getItem('token');
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      };

      const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.TASK_DEBUG(taskId)), { headers });
      if (!response.ok) {
        throw new Error('Failed to load task debug data');
      }

      const data = await response.json();
      setSelectedTask(data);
    } catch (err) {
      setError(err.message);
    }
  };

  const tabs = [
    { id: 'models', name: 'AI Models', icon: CogIcon },
    { id: 'prompts', name: 'Prompts', icon: DocumentTextIcon },
    { id: 'stats', name: 'Statistics', icon: ChartBarIcon },
    { id: 'tasks', name: 'Tasks Debug', icon: BugAntIcon }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="mt-2 text-gray-600">Manage AI models, prompts, and system configuration</p>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                to="/admin/users"
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <UserIcon className="w-5 h-5" />
                <span>User Management</span>
              </Link>
              <Link
                to="/admin/blog"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <DocumentTextIcon className="w-5 h-5" />
                <span>Blog Management</span>
              </Link>
            </div>
          </div>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="mt-1 text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {successMessage && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <CheckCircleIcon className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">Success</h3>
                <p className="mt-1 text-sm text-green-700">{successMessage}</p>
              </div>
            </div>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`}
                >
                  <Icon className="h-5 w-5 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'models' && (
          <ModelsTab 
            models={models} 
            onUpdateModel={updateModel} 
            onTestModel={testModel}
            testingModel={testingModel}
          />
        )}
        
        {activeTab === 'prompts' && (
          <PromptsTab
            prompts={prompts}
            onUpdatePrompt={updatePrompt}
          />
        )}

        {activeTab === 'stats' && (
          <StatsTab 
            stats={stats} 
            onExportTasks={exportTasks}
            exportingTasks={exportingTasks}
          />
        )}

        {activeTab === 'tasks' && (
          <TasksDebugTab 
            tasks={tasks}
            selectedTask={selectedTask}
            onLoadTaskDebug={loadTaskDebug}
            onCloseTaskDebug={() => setSelectedTask(null)}
          />
        )}
      </div>
    </div>
  );
};

// Models Tab Component
const ModelsTab = ({ models, onUpdateModel, onTestModel, testingModel }) => {
  const [editingModel, setEditingModel] = useState(null);
  const [formData, setFormData] = useState({});

  const startEdit = (model) => {
    setEditingModel(model.model_name);
    setFormData({
      api_key: '',
      model_version: model.model_version || '',
      is_active: model.is_active,
      is_default: model.is_default
    });
  };

  const saveModel = async () => {
    await onUpdateModel(editingModel, formData);
    setEditingModel(null);
    setFormData({});
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-900">AI Model Configuration</h2>
        <p className="mt-1 text-sm text-gray-600">
          Configure API keys, model versions, and settings for each AI provider.
          API keys are stored securely in the database and can be updated here.
        </p>
      </div>

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Model
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Provider
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Version
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                API Key
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {models.map((model) => (
              <tr key={model.model_name}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="text-sm font-medium text-gray-900">
                      {model.model_name}
                    </div>
                    {model.is_default && (
                      <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Default
                      </span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {model.provider}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {editingModel === model.model_name ? (
                    <input
                      type="text"
                      value={formData.model_version}
                      onChange={(e) => setFormData({...formData, model_version: e.target.value})}
                      className="border border-gray-300 rounded px-2 py-1 text-sm"
                    />
                  ) : (
                    model.model_version || 'Not set'
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {editingModel === model.model_name ? (
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.is_active}
                          onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                          className="mr-2"
                        />
                        Active
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.is_default}
                          onChange={(e) => setFormData({...formData, is_default: e.target.checked})}
                          className="mr-2"
                        />
                        Default
                      </label>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      {model.is_active ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 text-red-500" />
                      )}
                      <span className={`text-sm ${model.is_active ? 'text-green-700' : 'text-red-700'}`}>
                        {model.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {editingModel === model.model_name ? (
                    <input
                      type="password"
                      placeholder="Enter new API key"
                      value={formData.api_key}
                      onChange={(e) => setFormData({...formData, api_key: e.target.value})}
                      className="border border-gray-300 rounded px-2 py-1 text-sm w-full"
                    />
                  ) : (
                    <span className={model.has_api_key ? 'text-green-600' : 'text-red-600'}>
                      {model.has_api_key ? '✓ Configured' : '✗ Missing'}
                    </span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  {editingModel === model.model_name ? (
                    <>
                      <button
                        onClick={saveModel}
                        className="text-green-600 hover:text-green-900"
                      >
                        Save
                      </button>
                      <button
                        onClick={() => setEditingModel(null)}
                        className="text-gray-600 hover:text-gray-900"
                      >
                        Cancel
                      </button>
                    </>
                  ) : (
                    <>
                      <button
                        onClick={() => startEdit(model)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => onTestModel(model.model_name)}
                        disabled={testingModel === model.model_name || !model.has_api_key}
                        className="text-purple-600 hover:text-purple-900 disabled:text-gray-400"
                      >
                        {testingModel === model.model_name ? 'Testing...' : 'Test'}
                      </button>
                    </>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Prompts Tab Component
const PromptsTab = ({ prompts, onUpdatePrompt }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Prompt Templates</h3>
        <p className="mt-2 text-sm text-gray-600">
          Prompt management has been moved to code-based configuration.
        </p>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <div className="flex">
          <ExclamationTriangleIcon className="h-6 w-6 text-yellow-400 mr-3" />
          <div>
            <h4 className="text-sm font-medium text-yellow-800">Feature Migrated</h4>
            <div className="mt-2 text-sm text-yellow-700">
              <p>Prompts are now managed in <code className="bg-yellow-100 px-1 rounded">/backend/config/prompts.js</code> for better version control.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Stats Tab Component
const StatsTab = ({ stats, onExportTasks, exportingTasks }) => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">System Statistics</h2>
        <button
          onClick={onExportTasks}
          disabled={exportingTasks}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
          {exportingTasks ? '导出中...' : '导出Tasks数据'}
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-blue-600">{stats.totalUsers || 0}</div>
          <div className="text-sm text-gray-600">Total Users</div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-green-600">{stats.totalTasks || 0}</div>
          <div className="text-sm text-gray-600">Total Tasks</div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-purple-600">{stats.activeModels || 0}</div>
          <div className="text-sm text-gray-600">Active Models</div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-orange-600">{stats.totalPrompts || 0}</div>
          <div className="text-sm text-gray-600">Prompt Templates</div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">数据导出</h3>
        <div className="space-y-4">
          <div className="border-l-4 border-blue-500 pl-4">
            <h4 className="font-medium text-gray-900">Tasks完整数据导出</h4>
            <p className="text-sm text-gray-600 mt-1">
              导出tasks表的完整原始数据，包括所有JSON字段的具体内容，如关键词列表、生成的话题建议、完整文章内容等，用于深度质量评估和内容分析。
            </p>
            <p className="text-xs text-gray-500 mt-2">
              导出格式: CSV | 包含字段: 任务基础信息、完整JSON数据（关键词、话题建议、文章内容等）、用户信息、时间戳
            </p>
            <p className="text-xs text-orange-600 mt-1">
              注意: 导出文件可能较大，包含完整的文章文本和JSON数据
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Tasks Debug Tab Component
const TasksDebugTab = ({ tasks, selectedTask, onLoadTaskDebug, onCloseTaskDebug }) => {
  if (selectedTask) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">
            Task Debug: {selectedTask.name}
          </h3>
          <button
            onClick={onCloseTaskDebug}
            className="text-gray-400 hover:text-gray-600"
          >
            <XCircleIcon className="h-6 w-6" />
          </button>
        </div>
        
        {/* Task Information */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h4 className="text-sm font-medium text-gray-900">Task Information</h4>
            <div className="mt-2 text-sm text-gray-600 grid grid-cols-2 gap-4">
              <div>
                <p><strong>User:</strong> {selectedTask.user_name} ({selectedTask.user_email})</p>
                <p><strong>Created:</strong> {new Date(selectedTask.created_at).toLocaleString()}</p>
                <p><strong>Updated:</strong> {new Date(selectedTask.updated_at).toLocaleString()}</p>
              </div>
              <div>
                <p><strong>Prompt Length:</strong> {selectedTask.debug_prompt ? selectedTask.debug_prompt.length.toLocaleString() : 0} chars</p>
                <p><strong>Article Length:</strong> {selectedTask.article_length ? selectedTask.article_length.toLocaleString() : 0} chars</p>
                <p><strong>Has Article:</strong> {selectedTask.has_generated_article ? '✅ Yes' : '❌ No'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Side-by-Side Comparison */}
        <div className="grid grid-cols-2 gap-6">
          {/* Left Side - Debug Prompt */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-sm font-medium text-gray-900 flex items-center">
                <DocumentTextIcon className="h-4 w-4 mr-2 text-blue-500" />
                Complete Prompt Sent to Gemini
              </h4>
            </div>
            <div className="px-6 py-4">
              {selectedTask.debug_prompt ? (
                <div className="bg-gray-50 p-4 rounded-lg border h-96 overflow-auto">
                  <pre className="text-xs whitespace-pre-wrap font-mono text-gray-800">
                    {selectedTask.debug_prompt}
                  </pre>
                </div>
              ) : (
                <div className="text-gray-500 italic text-center py-8">
                  No debug prompt available for this task
                </div>
              )}
            </div>
          </div>

          {/* Right Side - Generated Article */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-sm font-medium text-gray-900 flex items-center">
                <DocumentTextIcon className="h-4 w-4 mr-2 text-green-500" />
                Generated Article Output
              </h4>
            </div>
            <div className="px-6 py-4">
              {selectedTask.generated_article ? (
                <div className="bg-gray-50 p-4 rounded-lg border h-96 overflow-auto">
                  <div className="text-sm whitespace-pre-wrap text-gray-800 prose prose-sm max-w-none">
                    {selectedTask.generated_article}
                  </div>
                </div>
              ) : (
                <div className="text-gray-500 italic text-center py-8">
                  No generated article available for this task
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Analysis Section */}
        {selectedTask.debug_prompt && selectedTask.generated_article && (
          <div className="bg-blue-50 rounded-lg p-6">
            <h4 className="text-sm font-medium text-blue-900 mb-3 flex items-center">
              <ChartBarIcon className="h-4 w-4 mr-2" />
              Quick Analysis
            </h4>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div className="bg-white p-3 rounded border">
                <div className="text-blue-600 font-medium">Prompt→Article Ratio</div>
                <div className="text-xl font-bold text-gray-900">
                  1:{Math.round(selectedTask.article_length / selectedTask.debug_prompt.length)}
                </div>
              </div>
              <div className="bg-white p-3 rounded border">
                <div className="text-blue-600 font-medium">Word Count (Est.)</div>
                <div className="text-xl font-bold text-gray-900">
                  ~{Math.round(selectedTask.article_length / 5)}
                </div>
              </div>
              <div className="bg-white p-3 rounded border">
                <div className="text-blue-600 font-medium">Content Status</div>
                <div className="text-xl font-bold text-green-600">
                  Generated ✓
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Tasks Debug</h3>
        <p className="mt-2 text-sm text-gray-600">
          View tasks with debug information and complete prompts sent to AI services.
        </p>
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h4 className="text-sm font-medium text-gray-900">Recent Tasks</h4>
        </div>
        
        <div className="divide-y divide-gray-200">
          {tasks.length === 0 ? (
            <div className="px-6 py-8 text-center text-gray-500">
              No tasks found
            </div>
          ) : (
            tasks.map((task) => (
              <div key={task.id} className="px-6 py-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h5 className="text-sm font-medium text-gray-900 truncate">
                        {task.name}
                      </h5>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        task.status === 'Completed' 
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {task.status}
                      </span>
                      {task.has_debug_prompt && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          <BugAntIcon className="h-3 w-3 mr-1" />
                          Debug Available
                        </span>
                      )}
                    </div>
                    <div className="mt-1 text-xs text-gray-500">
                      <span>User: {task.user_email}</span>
                      <span className="mx-2">•</span>
                      <span>Updated: {new Date(task.updated_at).toLocaleDateString()}</span>
                      {task.has_debug_prompt && (
                        <>
                          <span className="mx-2">•</span>
                          <span>Prompt: {task.debug_prompt_length} chars</span>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    {task.has_debug_prompt && (
                      <button
                        onClick={() => onLoadTaskDebug(task.id)}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <BugAntIcon className="h-4 w-4 mr-1" />
                        View Debug
                      </button>
                    )}
                  </div>
                </div>
                
                {task.selected_topics && task.selected_topics.length > 0 && (
                  <div className="mt-2">
                    <div className="text-xs text-gray-500 mb-1">Selected Topics:</div>
                    <div className="flex flex-wrap gap-1">
                      {task.selected_topics.slice(0, 3).map((topic, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                        >
                          {typeof topic === 'string' ? topic.substring(0, 50) : (topic.edited || topic.original || '').substring(0, 50)}
                          {(typeof topic === 'string' ? topic : (topic.edited || topic.original || '')).length > 50 && '...'}
                        </span>
                      ))}
                      {task.selected_topics.length > 3 && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                          +{task.selected_topics.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
