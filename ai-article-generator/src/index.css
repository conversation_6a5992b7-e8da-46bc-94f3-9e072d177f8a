@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: #f8fafc;
}

/* Enhanced Editor Styles */
.article-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.6;
}

.article-editor:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Enhanced Preview Styles */
.article-preview {
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.7;
}

.article-preview h1 {
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.article-preview h2 {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.25rem;
}

.article-preview blockquote {
  border-left: 4px solid #3b82f6;
  background-color: #f8fafc;
  padding: 1rem;
  margin: 1rem 0;
  font-style: italic;
}

.article-preview code {
  background-color: #f1f5f9;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.article-preview pre {
  background-color: #1e293b;
  color: #f1f5f9;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
}

/* Fullscreen mode */
.fullscreen-editor {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
  padding: 1rem;
}

/* Smooth transitions */
.view-transition {
  transition: all 0.3s ease-in-out;
}

/* Custom scrollbar for preview */
.article-preview::-webkit-scrollbar {
  width: 8px;
}

.article-preview::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.article-preview::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.article-preview::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
