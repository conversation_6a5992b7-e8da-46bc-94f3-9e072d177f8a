import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  PlusIcon,
  DocumentDuplicateIcon,
  ClockIcon,
  CheckCircleIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  TrashIcon,
  PencilIcon,
  SparklesIcon,
  ArrowPathIcon,
  XCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { apiCall } from '../config/api';

const BatchTasksPage = () => {
  const { authenticatedFetch } = useAuth();
  const [batchTasks, setBatchTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [sortBy, setSortBy] = useState('updated_at');
  const [sortOrder, setSortOrder] = useState('DESC');

  useEffect(() => {
    fetchBatchTasks();
  }, [statusFilter, sortBy, sortOrder]);

  const fetchBatchTasks = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        limit: '50'
      });
      
      if (statusFilter) {
        params.append('status', statusFilter);
      }

      const response = await apiCall(`/api/batch-tasks?${params}`, {
        method: 'GET'
      });
      
      if (response.batchTasks) {
        setBatchTasks(response.batchTasks || []);
      } else {
        console.error('Failed to fetch batch tasks');
        setBatchTasks([]);
      }
    } catch (error) {
      console.error('Error fetching batch tasks:', error);
      setBatchTasks([]);
    } finally {
      setLoading(false);
    }
  };

  const deleteBatchTask = async (taskId) => {
    if (!confirm('Are you sure you want to delete this batch task? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await apiCall(`/api/batch-tasks/${taskId}`, {
        method: 'DELETE'
      });

      if (response.message) {
        setBatchTasks(batchTasks.filter(task => task.id !== taskId));
      } else {
        alert('Failed to delete batch task');
      }
    } catch (error) {
      console.error('Error deleting batch task:', error);
      alert('Error deleting batch task');
    }
  };

  const getStatusIcon = (status) => {
    if (status === 'completed') {
      return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
    } else if (status === 'processing') {
      return <ArrowPathIcon className="w-5 h-5 text-yellow-500 animate-spin" />;
    } else if (status === 'failed') {
      return <XCircleIcon className="w-5 h-5 text-red-500" />;
    } else if (status === 'cancelled') {
      return <ExclamationTriangleIcon className="w-5 h-5 text-gray-500" />;
    } else {
      return <DocumentDuplicateIcon className="w-5 h-5 text-purple-500" />;
    }
  };

  const getStatusColor = (status) => {
    if (status === 'completed') return 'bg-green-100 text-green-800';
    if (status === 'processing') return 'bg-yellow-100 text-yellow-800';
    if (status === 'failed') return 'bg-red-100 text-red-800';
    if (status === 'cancelled') return 'bg-gray-100 text-gray-800';
    if (status === 'pending') return 'bg-blue-100 text-blue-800';
    return 'bg-purple-100 text-purple-800';
  };

  // Filter tasks
  const filteredTasks = batchTasks.filter(task =>
    task.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg">
        {/* Header */}
        <div className="border-b border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Batch Tasks</h1>
              <p className="text-gray-600">Manage your batch article generation projects</p>
            </div>
            <Link
              to="/advanced-batch-generator"
              className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <SparklesIcon className="w-5 h-5 mr-2" />
              New Batch Task
            </Link>
          </div>
        </div>

        {/* Filters */}
        <div className="border-b border-gray-200 p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search batch tasks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              />
            </div>

            {/* Status Filter */}
            <div className="flex items-center space-x-2">
              <FunnelIcon className="w-5 h-5 text-gray-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              >
                <option value="">All Status</option>
                <option value="draft">Draft</option>
                <option value="pending">Pending</option>
                <option value="processing">Processing</option>
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </div>
        </div>

        {/* Task List */}
        <div className="divide-y divide-gray-200">
          {filteredTasks.length === 0 ? (
            <div className="text-center py-12">
              <DocumentDuplicateIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No batch tasks</h3>
              <p className="mt-1 text-sm text-gray-500">Get started by creating a new batch task.</p>
              {!searchTerm && !statusFilter && (
                <Link
                  to="/advanced-batch-generator"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-200 shadow-lg hover:shadow-xl mt-4"
                >
                  <SparklesIcon className="w-5 h-5 mr-2" />
                  Create Your First Batch Task
                </Link>
              )}
            </div>
          ) : (
            filteredTasks.map((task) => (
              <div key={task.id} className="px-6 py-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    {getStatusIcon(task.status)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h4 className="text-sm font-medium text-gray-900 truncate">{task.name}</h4>
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                          <DocumentDuplicateIcon className="w-3 h-3 mr-1" />
                          Batch
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">
                        {task.total_articles || 0} articles • 
                        {task.completed_articles || 0} completed • 
                        Updated {new Date(task.updated_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4 flex-shrink-0">
                    {/* Progress Bar */}
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${task.progress || 0}%` }}
                      ></div>
                    </div>

                    {/* Status Badge */}
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                      {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                    </span>

                    {/* Action Buttons */}
                    <div className="flex items-center space-x-2">
                      <Link
                        to={`/batch-tasks/${task.id}`}
                        className="text-purple-600 hover:text-purple-700 text-sm font-medium"
                      >
                        View Details
                      </Link>
                      {task.status === 'draft' && (
                        <Link
                          to={`/advanced-batch-generator/${task.id}`}
                          className="p-1 text-gray-400 hover:text-purple-600 transition-colors"
                        >
                          <PencilIcon className="w-4 h-4" />
                        </Link>
                      )}
                      <button
                        onClick={() => deleteBatchTask(task.id)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default BatchTasksPage;
