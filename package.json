{"name": "j-writer", "version": "1.0.0", "description": "Writer J - A sophisticated AI-powered article generation tool", "private": true, "workspaces": ["ai-article-generator", "backend"], "scripts": {"start": "cd backend && npm start", "build": "cd ai-article-generator && npm install && npm run build", "dev:frontend": "cd ai-article-generator && npm run dev", "dev:backend": "cd backend && npm run dev", "install:frontend": "cd ai-article-generator && npm install", "install:backend": "cd backend && npm install", "install:all": "npm run install:frontend && npm run install:backend"}, "keywords": ["ai", "article", "generator", "react", "vite", "gemini", "content"], "author": "", "license": "ISC", "engines": {"node": ">=16.0.0"}}