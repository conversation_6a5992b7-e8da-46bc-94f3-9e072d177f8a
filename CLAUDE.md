# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

Writer J 是一个 AI 驱动的文章生成平台，通过 7 步工作流程帮助用户创建高质量、SEO 友好的文章。

## 项目结构

```
writer-j/
├── j-writer/
│   ├── ai-article-generator/    # 前端 React 应用 (Vite)
│   └── backend/                 # 后端 Express API
```

## 开发命令

### 前端开发 (在 j-writer/ai-article-generator/ 目录)
```bash
npm install          # 安装依赖
npm run dev         # 启动开发服务器 (http://localhost:5173)
npm run build       # 构建生产版本
npm run preview     # 预览生产版本
```

### 后端开发 (在 j-writer/backend/ 目录)
```bash
npm install          # 安装依赖
npm run dev         # 启动开发服务器 with nodemon (http://localhost:3001)
npm start           # 启动生产服务器
npm run init-db     # 初始化数据库表和默认数据
npm run migrate     # 运行数据库迁移脚本
npm run dev:local   # 使用本地 .env.local 配置启动
npm run test-postgres # 测试 PostgreSQL 连接
```

## 技术架构

### 前端技术栈
- React 19.1.0 + Vite
- Tailwind CSS
- React Router
- Headless UI + Heroicons

### 后端技术栈
- Node.js + Express.js (生产端口: 3001)
- 数据库: **PostgreSQL only** (不再支持 SQLite)
- AI 服务: Google Gemini API, DeepSeek (通过 aiServiceManager 统一管理)
- 认证: JWT + bcryptjs
- 搜索 API: Serper API
- 安全: Helmet, CORS, Rate Limiting
- 日志: Morgan + audit logging

### 双重文章生成架构

项目提供两种完全不同的文章生成方式:

## 1. 基于任务的单文章生成 (7步工作流程)

**路由**: `/tasks/editor/:id` | **组件**: `TaskEditor.jsx`

详细的7步引导式流程:

1. **Step 0**: 关键词研究 (`Step0KeywordResearch.jsx`)
   - Serper API 关键词分析
   - 自动补全建议系统
   - PAA (People Also Ask) 数据提取

2. **Step 1**: 主题选择 (`Step1TopicSelection.jsx`)
   - AI 生成主题建议
   - 多主题选择支持 (1-5个)
   - 自定义主题编辑

3. **Step 2**: 资源整合 (`Step2Sources.jsx`)
   - URL 内容提取和摘要
   - 手动文本块输入
   - Reddit 内容集成

4. **Step 3**: 产品整合 (`Step3Product.jsx`, 可选)
   - 产品信息管理
   - 自然产品植入策略
   - 链接和引用管理

5. **Step 4**: E-E-A-T 档案 (`Step4EEAT.jsx`)
   - 作者权威信息
   - 专业背景和经验
   - 可信度建立

6. **Step 5**: 参数设置 (`Step4Parameters.jsx`)
   - 写作风格和语调
   - 文章长度 (短/中/长)
   - SEO 优化参数

7. **Step 6**: 文章生成 (`Step5Generation.jsx`)
   - 流式 AI 内容生成
   - 实时编辑功能
   - 自动保存和导出

**特点**: 
- 详细的用户交互和控制
- 支持断点续写和草稿保存
- 状态追踪 (`current_step` 字段)
- 每步数据持久化到 `tasks` 表

## 2. 批量多文章生成 (自动化流程)

**路由**: `/advanced-batch-generator` | **组件**: `AdvancedBatchGenerator.jsx` | **API**: `POST /api/tasks/advanced-batch-generate`

高度自动化的批量生成系统:

### 输入模式 (3种)
- **KEYWORDS**: 每个关键词生成1篇文章
- **TITLES**: 每个标题生成1篇文章
- **IDEAS**: 创意描述自动扩展为多个关键词

### 核心处理流程
1. **输入解析**: 根据模式解析内容
2. **关键词增强**: 使用 Serper API 进行真实数据分析
3. **智能资源整合**: AI 分析资源相关性
4. **主题大纲生成**: 自动生成文章结构
5. **批量文章生成**: 分批处理 (BATCH_SIZE = 3)

### 批量生成专用的 AI 方法
- `parseIdeasToKeywords()`: 创意转关键词
- `extractKeywordFromTitle()`: 标题提取关键词  
- `enhancedKeywordResearch()`: 增强关键词研究
- `intelligentResourceIntegration()`: 智能资源整合
- `generateTopicAndOutline()`: 主题大纲生成
- `generateAdvancedArticle()`: 高级文章生成

**特点**:
- 高度自动化，最少用户干预
- 批处理优化和错误恢复
- 质量评估 (SEO得分、可读性等)
- 成本控制和进度跟踪

### 使用场景对比

| 特性 | 单文章生成 (7步工作流) | 批量生成 (自动化流程) |
|------|----------------------|----------------------|
| **用户控制** | 高 - 每步都可编辑调整 | 低 - 预设参数自动执行 |
| **生成速度** | 慢 - 需要逐步操作 | 快 - 批量自动处理 |
| **内容质量** | 高 - 精细化控制 | 中高 - AI自动优化 |
| **适用场景** | 重要文章、精品内容 | 大量内容、SEO文章 |
| **学习成本** | 高 - 需了解7步流程 | 低 - 简单输入即可 |
| **API消耗** | 中等 - 按需调用 | 高 - 大量自动调用 |

## 数据库架构

### 核心数据表
- `users` - 用户账户 (支持 user/admin 角色)
- `tasks` - 文章生成任务 (7步工作流状态)
- `user_presets` - 用户预设 (author/product/resource 类型)
- `user_resources` - 用户管理的研究资源
- `blog_posts` - 博客系统文章
- `ai_models` - AI 模型配置管理
- `user_oauth_tokens` - OAuth 令牌 (Reddit 等)
- `user_sessions` - 会话管理
- `audit_logs` - 审计日志

### 数据库特性
- **PostgreSQL 专用**: 使用 `$1, $2` 占位符语法
- **自动索引**: 性能优化的预建索引
- **事务支持**: 内置事务管理方法
- **连接池**: 使用 pg Pool 管理连接

## 关键架构组件

### AI 服务管理 (`backend/services/aiServiceManager.js`)
- **统一管理**: 所有 AI 模型通过此服务调用
- **动态配置**: 支持运行时切换模型和 API 密钥
- **提示词管理**: 现在通过 `config/prompts.js` 管理，不再使用数据库
- **错误处理**: 内置重试和fallback机制

### 前端状态管理
- **AuthContext**: JWT 认证状态管理 (`src/contexts/AuthContext.jsx`)
- **NotificationContext**: 全局通知系统
- **API 配置**: 集中在 `src/config/api.js` 的完整端点配置

### 数据库抽象层 (`backend/config/database.js`)
- **查询转换**: 自动将 `?` 占位符转换为 PostgreSQL `$n` 格式
- **连接管理**: 内置连接池和事务支持
- **初始化**: 自动创建表和索引

## 环境变量

### 必需的后端环境变量
- `DATABASE_URL` - PostgreSQL 连接字符串 (必须以 postgresql:// 开头)
- `JWT_SECRET` - JWT 签名密钥

### AI 服务 API 密钥
- `GOOGLE_API_KEY` - Google Gemini API (主要模型)
- `DEEPSEEK_API_KEY` - DeepSeek API (可选)
- `SERPER_API_KEY` - Serper 搜索 API

### 前端环境变量
- `VITE_API_BASE_URL` - API 基础 URL (默认: Railway 生产环境)

## 重要开发注意事项

### 数据库查询
- **仅支持 PostgreSQL**: 不再支持 SQLite，所有查询必须兼容 PostgreSQL
- **占位符**: 可以使用 `?` 占位符，系统会自动转换为 `$1, $2` 格式
- **事务**: 使用 `database.transaction()` 方法进行事务操作

### AI 服务集成
- **统一入口**: 所有 AI 调用通过 `aiServiceManager` 进行
- **双重架构**: 单文章生成使用基础 AI 方法，批量生成使用专门的增强方法
- **提示词**: 在 `config/prompts.js` 中管理，不要直接写在代码中
- **错误处理**: AI 服务调用需要适当的错误处理和重试机制
- **批处理优化**: 批量生成使用 `BATCH_SIZE = 3` 避免 API 限制

### 前端开发
- **API 调用**: 使用 `src/config/api.js` 中的预定义端点
- **状态管理**: 通过 Context API 管理全局状态
- **路由保护**: 使用 `ProtectedRoute` 和 `AdminRoute` 组件

### 测试和质量
- **无测试框架**: 项目目前没有配置自动化测试
- **无代码检查**: 没有 ESLint/Prettier 配置
- **手动验证**: 开发后需要手动测试功能

### 部署架构
- **前端**: Vercel (通过 `vercel.json` 配置)
- **后端**: Railway (通过 `railway.toml` 配置)  
- **数据库**: Railway PostgreSQL
- **环境检测**: 自动根据 `PORT` 环境变量判断生产环境