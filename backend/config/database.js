const { Pool } = require('pg');

class Database {
  constructor() {
    this.pool = null;
    
    // 确保必须有 DATABASE_URL
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is required for PostgreSQL connection');
    }
    
    if (!process.env.DATABASE_URL.startsWith('postgresql://')) {
      throw new Error('DATABASE_URL must be a PostgreSQL connection string (postgresql://)');
    }
  }

  async connect() {
    try {
      // PostgreSQL connection only
      this.pool = new Pool({
        connectionString: process.env.DATABASE_URL,
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
      });

      // Test the connection
      const client = await this.pool.connect();
      client.release();
      console.log('Connected to PostgreSQL database');

      await this.initializeTables();
      return Promise.resolve();
    } catch (error) {
      console.error('PostgreSQL connection error:', error);
      throw error;
    }
  }

  async initializeTables() {
    const tables = this.getPostgresTables();

    for (const table of tables) {
      await this.run(table);
    }

    // Create indexes for better performance
    const indexes = await this.getPostgresIndexes();

    for (const index of indexes) {
      try {
        await this.run(index);
      } catch (error) {
        // Only log if it's not a "column does not exist" error
        // These errors are expected before migrations run
        if (!error.message.includes('column') || !error.message.includes('does not exist')) {
          console.warn(`Warning: Failed to create index: ${index}`);
          console.warn(`Error: ${error.message}`);
        }
      }
    }

    console.log('Database tables initialized successfully');
  }

  getPostgresTables() {
    return [
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(255),
        plan_type VARCHAR(50) DEFAULT 'V1_DEFAULT_ACCESS',
        role VARCHAR(20) DEFAULT 'user', -- 'user' or 'admin'
        email_verified BOOLEAN DEFAULT FALSE,
        verification_token VARCHAR(255),
        reset_token VARCHAR(255),
        reset_token_expires TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tasks table
      `CREATE TABLE IF NOT EXISTS tasks (
        id VARCHAR(255) PRIMARY KEY,
        user_id INTEGER NOT NULL,
        name VARCHAR(255) NOT NULL,
        status VARCHAR(100) DEFAULT 'Draft - Step 1',
        current_step INTEGER DEFAULT 0,
        keywords TEXT, -- JSON array
        selected_topics TEXT, -- JSON array
        topic_suggestions TEXT, -- JSON object
        keyword_research_data TEXT, -- JSON object
        keyword_research_selections TEXT, -- JSON array
        reddit_sources TEXT, -- JSON array
        sources TEXT, -- JSON array
        topic_sources TEXT, -- JSON array (deprecated - use comprehensive_resources)
        comprehensive_resources TEXT, -- JSON array
        product_info TEXT, -- JSON object
        eeat_profile TEXT, -- JSON object
        output_parameters TEXT, -- JSON object
        generated_article TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // User presets table
      `CREATE TABLE IF NOT EXISTS user_presets (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        preset_type VARCHAR(50) NOT NULL, -- 'author', 'product', or 'resource'
        preset_name VARCHAR(255) NOT NULL,
        preset_data TEXT NOT NULL, -- JSON object
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // User resources table for managing research sources
      `CREATE TABLE IF NOT EXISTS user_resources (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        resource_name VARCHAR(255) NOT NULL,
        resource_type VARCHAR(50) NOT NULL, -- 'url', 'reddit', 'document', 'manual'
        resource_url TEXT,
        resource_content TEXT, -- extracted content or manual content
        description TEXT,
        tags TEXT, -- JSON array of tags
        is_favorite BOOLEAN DEFAULT FALSE,
        usage_count INTEGER DEFAULT 0,
        last_used_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // Blog posts table
      `CREATE TABLE IF NOT EXISTS blog_posts (
        id SERIAL PRIMARY KEY,
        title VARCHAR(500) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        excerpt TEXT,
        content TEXT NOT NULL,
        meta_description VARCHAR(300),
        featured_image VARCHAR(500),
        author_id INTEGER,
        status VARCHAR(20) DEFAULT 'draft', -- 'draft', 'published', 'archived'
        seo_title VARCHAR(255),
        seo_keywords TEXT,
        reading_time INTEGER DEFAULT 5,
        view_count INTEGER DEFAULT 0,
        published_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (author_id) REFERENCES users (id) ON DELETE SET NULL
      )`,

      // User sessions table (for tracking active sessions)
      `CREATE TABLE IF NOT EXISTS user_sessions (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        session_token VARCHAR(255) UNIQUE NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // AI models table
      `CREATE TABLE IF NOT EXISTS ai_models (
        id SERIAL PRIMARY KEY,
        model_name VARCHAR(100) UNIQUE NOT NULL,
        provider VARCHAR(50) NOT NULL, -- 'google', 'deepseek', etc.
        api_key TEXT,
        model_version VARCHAR(100),
        is_active BOOLEAN DEFAULT TRUE,
        is_default BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Prompt templates are now managed in code (config/prompts.js)
      // This table is deprecated and no longer created

      // User OAuth tokens table
      `CREATE TABLE IF NOT EXISTS user_oauth_tokens (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        provider VARCHAR(50) NOT NULL, -- 'reddit', 'google', etc.
        access_token TEXT NOT NULL,
        refresh_token TEXT,
        token_type VARCHAR(50) DEFAULT 'Bearer',
        expires_at TIMESTAMP,
        scope TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // Audit logs table
      `CREATE TABLE IF NOT EXISTS audit_logs (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        user_email VARCHAR(255) NOT NULL,
        user_role VARCHAR(50) NOT NULL,
        action VARCHAR(100) NOT NULL,
        entity_type VARCHAR(100) NOT NULL,
        entity_id VARCHAR(255),
        entity_description TEXT,
        changes TEXT, -- JSON object storing old and new values
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
      )`
    ];
  }

  async getPostgresIndexes() {
    return [
      // User indexes
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)',
      'CREATE INDEX IF NOT EXISTS idx_users_plan_type ON users(plan_type)',

      // Task indexes
      'CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON tasks(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)',
      'CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at)',

      // User presets indexes
      'CREATE INDEX IF NOT EXISTS idx_user_presets_user_id ON user_presets(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_user_presets_type ON user_presets(preset_type)',

      // Blog posts indexes
      'CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON blog_posts(slug)',
      'CREATE INDEX IF NOT EXISTS idx_blog_posts_status ON blog_posts(status)',
      'CREATE INDEX IF NOT EXISTS idx_blog_posts_author_id ON blog_posts(author_id)',
      'CREATE INDEX IF NOT EXISTS idx_blog_posts_published_at ON blog_posts(published_at)',

      // User sessions indexes  
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_session_token ON user_sessions(session_token)',
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at)',

      // AI models indexes
      'CREATE INDEX IF NOT EXISTS idx_ai_models_name ON ai_models(model_name)',
      'CREATE INDEX IF NOT EXISTS idx_ai_models_provider ON ai_models(provider)',
      'CREATE INDEX IF NOT EXISTS idx_ai_models_active ON ai_models(is_active)',

      // Prompt templates indexes are no longer needed (prompts are in code)

      // User OAuth tokens indexes
      'CREATE INDEX IF NOT EXISTS idx_user_oauth_tokens_user_id ON user_oauth_tokens(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_user_oauth_tokens_provider ON user_oauth_tokens(provider)',

      // Audit logs indexes
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_entity ON audit_logs(entity_type, entity_id)',
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action)'
    ];
  }

  // Helper method to convert SQLite-style ? placeholders to PostgreSQL $1, $2, etc.
  convertSqlPlaceholders(sql) {
    let counter = 1;
    return sql.replace(/\?/g, () => `$${counter++}`);
  }

  // PostgreSQL query methods
  async run(sql, params = []) {
    const client = await this.pool.connect();
    try {
      const pgSql = this.convertSqlPlaceholders(sql);
      const result = await client.query(pgSql, params);
      return {
        changes: result.rowCount || 0,
        lastID: result.rows[0]?.id || null
      };
    } finally {
      client.release();
    }
  }

  async get(sql, params = []) {
    const client = await this.pool.connect();
    try {
      const pgSql = this.convertSqlPlaceholders(sql);
      const result = await client.query(pgSql, params);
      return result.rows[0] || null;
    } finally {
      client.release();
    }
  }

  async all(sql, params = []) {
    const client = await this.pool.connect();
    try {
      const pgSql = this.convertSqlPlaceholders(sql);
      const result = await client.query(pgSql, params);
      return result.rows;
    } finally {
      client.release();
    }
  }

  async close() {
    if (this.pool) {
      await this.pool.end();
      console.log('PostgreSQL connection pool closed');
    }
  }

  // Helper method to get PostgreSQL client for transactions
  async getClient() {
    return await this.pool.connect();
  }

  async transaction(callback) {
    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');
      // Create a wrapper client that converts placeholders
      const wrappedClient = {
        query: (sql, params = []) => {
          const pgSql = this.convertSqlPlaceholders(sql);
          return client.query(pgSql, params);
        }
      };
      const result = await callback(wrappedClient);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }
}

module.exports = new Database();