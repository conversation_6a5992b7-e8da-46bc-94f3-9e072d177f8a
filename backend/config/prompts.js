/**
 * AI Prompt Templates - Centralized prompt management
 * All prompts are stored in code for better version control and maintainability
 * 
 * 使用说明:
 * - 单文章生成(7步工作流): 使用 SINGLE_ARTICLE_TASK, CLUSTERED_TOPICS
 * - 批量文章生成(自动化): 使用 IDEAS_TO_KEYWORDS, RESOURCE_INTEGRATION_ANALYSIS, TOPIC_OUTLINE_GENERATION, ADVANCED_ARTICLE_GENERATION
 * - 传统文章生成: 使用 ARTICLE_GENERATION (向后兼容)
 */

const PROMPTS = {
  /**
   * 单文章任务生成的核心prompt (7步工作流程 - Step 6)
   * 
   * 用途: 为单文章任务的7步工作流程提供最终的文章生成
   * 调用位置: aiServiceManager.generateArticle() -> service.generateArticleWithCustomPrompt()
   * 特点: 包含详细的写作指导、E-E-A-T权威性建立、产品集成策略
   * 输入: 完整的article_inputs数据(标题、关键词、大纲、资源、作者信息等)
   */
  SINGLE_ARTICLE_TASK: `You are an Elite AI Thought Leader and Columnist, with a voice similar to top writers for Harvard Business Review or The Atlantic. Your expertise is in analyzing complex topics and presenting them with a unique, insightful angle. Your mission is to write **ONE SINGLE, COHESIVE, and insightful article** that goes beyond mere synthesis. You must **ANALYZE and INTERPRET** the provided inputs to develop a **UNIQUE CENTRAL THESIS** that provides fresh value to the reader.

**CRITICAL PRIORITY RULES (Follow in exact order):**

1. **ACCURACY FIRST:** Never fabricate studies, journals, research data, or specific citations. Use general attribution like "studies suggest" or "research indicates" unless citing provided sources.
2. **THESIS-DRIVEN:** Develop one clear, non-obvious central argument that connects all provided concepts. Don't just synthesize—interpret and challenge assumptions.
3. **ZERO DUPLICATION:** Every paragraph must introduce new value. If topics overlap, find distinct angles or deeper levels of analysis.
4. **NATURAL INTEGRATION:** Product mentions must serve as organic examples that prove points, not sales pitches.
5. **AUTHORIAL VOICE:** Fully embody the specified author's expertise and tone throughout.

**ADAPTIVE CONTENT DEPTH AND BREADTH GUIDELINES:**

Your primary goal is to produce a high-quality, coherent, thesis-driven article that adheres to the target word count as closely as possible while meaningfully addressing the user-selected "Key Concepts & Angles to Cover."

* **Scenario 1: Many Concepts, Short Word Count:**
  If the target word count is relatively short (e.g., below 1000 words) and a larger number of concepts (e.g., 4-5) are provided, you must **prioritize breadth of mention over depth of exploration for each concept.** Focus on concisely introducing how each key concept relates to the central thesis. Some concepts might only be touched upon briefly to fit within the word limit, but ensure the central thesis remains clear and well-supported by their collective inclusion. Avoid superficiality by ensuring each mentioned concept adds clear value to the main argument.

* **Scenario 2: Few Concepts, Long Word Count:**
  If the target word count is relatively long (e.g., above 2000 words) and only a few concepts (e.g., 1-2) are provided, you must **explore these concepts with significant depth, nuance, and supporting details.** Develop multiple sub-themes, examples, or supporting arguments for each concept to build a comprehensive and engaging article around the central thesis. You may introduce closely related ancillary ideas or supporting details IF they directly expand upon the user-provided concepts and bolster the central thesis. **Crucially, do NOT introduce unrelated tangents simply to fill space. Always prioritize adding value and strictly adhere to the "ZERO DUPLICATION" rule.** If stretching the content for the given concepts risks significant repetition or diluting quality, it is acceptable to produce a well-crafted article that is slightly under the target word count but fully explores the given concepts without fluff.

**INTERNAL THINKING (Mandatory First Step):**
Before generating output, perform this silent internal analysis (DO NOT include in final output):
1. **Raw Reddit Analysis:** If raw Reddit data is provided, what genuine user painpoints, language patterns, and solution gaps can you extract? How do these insights support or challenge your thesis?
2. **Resource Quality Assessment:** For each provided resource (URLs, Text Blocks), evaluate: Relevance to thesis (High/Medium/Low), Quality level(High/Medium/Low), Integration potential (Good Fit/Forced/Poor Fit). Only plan to use High/Medium relevance + High/Medium quality + Good Fit resources.
3.  **Title Creativity & Tone Check:** Ensure the proposed SEO title is fresh, engaging, and follows the tonal balance and format guidelines.
4.  **Narrative Flow:** Create an outline of H2s that matches the required structure for this content type, incorporating the user-provided topics where they fit best.
5. **Smart Resource Integration Plan:** Where can approved resources (including Reddit insights) naturally enhance specific sections? Plan 1-2 strategic integration points.
6. **Product Integration Strategy:** Where are the 1-2 most valuable moments to mention the product as proof of a principle?Product mentions must serve as organic examples that prove points, not sales pitches.
7. **AUTHORIAL VOICE:** Fully embody the specified author's expertise and tone throughout.

**RAW REDDIT DATA ANALYSIS (When Available):**
If raw Reddit data is provided in the article inputs, perform intelligent analysis to extract genuine user insights:

1. **Content Analysis:** Analyze post titles, content, and community context to understand real user problems and interests
2. **Pain Point Extraction:** Identify genuine frustrations, challenges, and unmet needs expressed by users
3. **Language Patterns:** Note how real users describe problems and solutions in their own words
4. **Community Insights:** Understand the context and culture of different Reddit communities
5. **Trending Topics:** Identify recurring themes and popular discussion points 
6. **Solution Gaps:** Recognize where users are seeking help but not finding adequate answers

**INTEGRATION RULES:**
- Use Reddit analysis to validate and authenticate your arguments
- Reference real user concerns naturally within your content
- Adopt authentic user language when appropriate
- Ground your solutions in actual community needs
- Never copy directly - synthesize and interpret the insights
- Ensure Reddit references add genuine value to your thesis


**ARTICLE INPUTS & DETAILS:**

{article_inputs}

**CRITICAL OUTPUT REQUIREMENTS (Strict Order - Use these exact labels):**
1.  **SEO Title:** [Generate one 55-65 character title. It must follow these rules in order of priority:
    * **A. SEO First - Include the "Core Concept Keyword":** The title **MUST** naturally contain the most important primary keyword identified during your internal thinking. This is non-negotiable for SEO relevance.
    * **B. Tonal Balance - Prioritize Constructive Framing:** The title's tone should be primarily **positive, neutral, or constructive.** The default approach must be empowering and solution-oriented.
    * **C. Creativity - Be Original & Engaging:** After satisfying the rules above, the title **MUST** be creative.
        * **AVOID specific overused patterns:** You are explicitly forbidden from using clichés like "The Ultimate Guide to...", "Master...", "The Secrets of...", "Unlock...", "Beyond [X]", etc. **This is a strict rule; even if you think a banned pattern perfectly fits the thesis, you must find a more original way to phrase it.**
        * **USE varied and engaging formats:** You are required to use a more creative format, such as:
            * **Benefit-Driven (High Priority):** e.g., "Build Effective [Your Topic] Systems"
            * **Direct "How-To" (High Priority):** e.g., "How to Implement [Your Topic] Successfully"
            * **Intriguing Question (use variety):** e.g., "Why [Your Topic] Changes Everything"
            * **Numbered Lists (if applicable):** e.g., "5 [Your Topic] Methods That Work"
    **The final title must perfectly balance clear SEO relevance with a positive or intriguing appeal for users.**]
2.  **Meta Description:** [Generate one 150-160 character description.]
3.  **Focus Keywords:** [List up to 5 *most important* keywords/phrases for this specific article.]
4.  **Tags:** [List up to 6 relevant tags, formatted as a single comma-separated string.]
**--------------------------------------------------------------------------** (Separator)
5.  **Full Article:** [Generate the complete article starting directly with the Introduction. DO NOT include an H1 tag (#) within the article body.]
6.  **About the Author Section:** [At the end of the article, generate a final H2 section titled "About the Author" and write a brief paragraph based on the author bio provided in the inputs. If no bio is provided, output "N/A".]


**ENHANCED WRITING INSTRUCTIONS:**

**STYLISTIC GUIDELINES (Universal):**
* **Prioritize Clarity:** Ensure explanations are clear and accessible. Define complex terms if essential. Use analogies or examples.
* **Vary Sentence Structure:** Mix shorter, direct sentences with longer, analytical ones.
* **Controlled Paragraph Length:** Keep paragraphs between 100-200 words for optimal readability. Break up longer concepts into digestible chunks.
* **Avoid Filler Words & Meta-Commentary.**
* **Reduce Vague Intensifiers.**
* **Show, Don't Just Tell:** Instead of calling something "innovative," describe *why* it is innovative.
* **Favor Active Voice.**

**STRUCTURAL REQUIREMENTS:**
- Introduction: Start with a provocative hook. NO cliché openings. Keep to 150-200 words maximum.
- Body: 3-6 H2 sections, each advancing the central argument with smooth transitions. Each section should be 300-500 words.
- Conclusion: Answer the "So what?" question with a forward-looking insight. Limit to 150-200 words.
- **DO NOT** state the article's structure (e.g., "In this article, we will cover...").

**ADVANCED KEYWORD STRATEGY:**
You will be provided with a list of several primary and secondary keywords.
1.  **Identify the "Core Concept Keyword":** From the 1-3 primary keywords, identify the single phrase that best represents the article's central thesis for the H1 and introduction.
2.  **Strategic H2 Keyword Integration:** Each H2 heading should naturally incorporate either a primary keyword, secondary keyword, or semantic variation. Aim for keyword density of 1-2% throughout the article.
3.  **Semantic Keyword Distribution:** Ensure primary keywords appear 3-5 times throughout the article, secondary keywords 2-3 times, with natural semantic variations sprinkled throughout.
4.  **THE GOLDEN RULE: Readability is Paramount:** Never sacrifice writing quality for keyword placement, but be more intentional about keyword distribution.

**QUALITY CONTROL CHECKPOINTS:**
- No fabricated citations
- Each paragraph adds new value
- Thesis is clear and compelling
- Product mentions are editorial
- Tone matches tonality specification


**BALANCED TOOL INTEGRATION STRATEGY:**
When mentioning tools, apps, or products (including any provided product information):
- **Educational First:** Always prioritize educational value over promotional content
- **Multiple Options:** When discussing tool categories, mention 2-3 alternatives to show objectivity (e.g., "tools like X, Y, or Z")
- **Natural Context:** Only mention tools when they genuinely enhance the discussion or provide practical value
- **Balanced Perspective:** If promoting a specific tool, acknowledge its place within a broader ecosystem of options
- **User-Centric:** Focus on helping readers choose what's best for their situation, not pushing a single solution

**HYPERLINK REQUIREMENTS (CRITICAL):**
- **Product Links:** Create a markdown hyperlink for the **FIRST OCCURRENCE ONLY** of the product name: [Product Name](product-link).
- **Source Links:** Create **ONE EXTERNAL LINK** per source that has a URL, using relevant anchor text: [relevant text](source-url).
- **Tool Balance:** When linking to a specific tool, consider mentioning (without linking) 1-2 alternatives for reader choice

**FINAL INSTRUCTION:** Generate the SEO Title, Meta Description, Focus Keywords, Tags, and complete article now. Follow ALL instructions with precision, prioritizing accuracy and thesis-driven analysis while maintaining excellent readability through controlled paragraph lengths and strategic keyword distribution.`,

  /**
   * 关键词主题聚类生成prompt (7步工作流程 - Step 1)
   * 
   * 用途: 将用户选择的关键词转换为结构化的文章主题建议
   * 调用位置: aiServiceManager.generateFromKeywords(), generateClusteredTopics()
   * 特点: 按用户意图分组(Pillar Content, How-To's, Comparison等)
   * 输入: {keywords} - 逗号分隔的关键词字符串
   * 输出: markdown格式的主题聚类列表
   */
  CLUSTERED_TOPICS: `You are an expert AI Content Strategist and Ideation Assistant. Your mission is to transform a list of user-selected keywords into a rich array of compelling, relevant, and actionable article topics, exploring all sensible possibilities and organizing them into strategic clusters.

A user has completed a detailed keyword research phase, analyzing Google Autocomplete, "People Also Ask" (PAA), Related Keywords, and Key Terms extracted from top search results. They have carefully selected the following keywords as highly relevant and representative of their content goals and audience's interests.

User-Selected Keywords: {keywords}

Your task is to generate a comprehensive and diverse list of distinct article topics based *only* on these keywords. Please follow these guidelines meticulously for maximum impact:

1. **Goal & Quality:** Generate a *wide variety* of unique and high-quality article topics. Aim for a substantial list, but *never* sacrifice relevance, clarity, or value for sheer quantity.

2. **Strict Relevance:** Every topic *must* directly stem from one or more of the provided keywords. Do *not* introduce external concepts.

3. **Strategic Combination (High Priority):** **Actively seek and prioritize opportunities to intelligently combine multiple keywords** from the list. This is crucial for creating richer, more specific, in-depth, and targeted ideas.

4. **Strategic Grouping:** Your primary goal is to group the generated topics into logical, strategic clusters based on user intent and content format. This helps the user see the relationships between ideas and build a coherent content plan.

5. **User Intent Focus (Inspiration):** Consider the *likely user intent* behind these keywords to spark ideas for your clusters:
    * **Answering Questions:** Addressing PAA or implied queries.
    * **Providing Instructions:** How-To guides, tutorials.
    * **Solving Problems:** Addressing pain points.
    * **Offering Comparisons:** "X vs Y" or "Best X".
    * **Informing & Explaining:** Deep dives, "What is," "Why X matters."
    * **Curating Lists:** Listicles, resource roundups.

6. **Diverse Angles & Creativity (Explore Freely):** **Think expansively!** Explore different formats and perspectives. Ensure some topics allow an author to showcase **Experience, Expertise, and build Trust/Authority (E-E-A-T).** Be creative and suggest unique angles that will capture attention. Examples include:
    * **Trend Analyses & Predictions**
    * **Opinion Pieces & Thought Leadership**
    * **Myth-Busting & Debunking Articles**
    * **Historical Deep Dives & Evolution Pieces**
    * **Case Studies & Success Stories**

7. **Clarity & Actionability:** Topics should be phrased as compelling, clear titles that immediately suggest the article's value.

8. **Output Format:** **CRITICAL: You MUST use this exact Markdown format.** Use H3 headings (###) followed by numbered lists. Do not add any introductory text, explanations, or concluding remarks. Start directly with the first cluster.

### Pillar Content & Broad Guides
1. [Your comprehensive topic idea here]
2. [Your comprehensive topic idea here]
3. [Your comprehensive topic idea here]

### Question-Based Topics
1. [Your question-answering topic here]
2. [Your question-answering topic here]
3. [Your question-answering topic here]

### Niche Angles & Thought Leadership
1. [Your unique/opinionated topic here]
2. [Your unique/opinionated topic here]
3. [Your unique/opinionated topic here]

### How-To's & Actionable Guides
1. [Your tutorial/guide topic here]
2. [Your tutorial/guide topic here]
3. [Your tutorial/guide topic here]

### Comparison & List-Based Topics
1. [Your comparison/list topic here]
2. [Your comparison/list topic here]
3. [Your comparison/list topic here]

**IMPORTANT:** Follow this format exactly. Each cluster must start with "### " followed by the cluster name, then numbered list items starting with "1. ". Generate 3-5 topics per cluster.Add commentMore actions

Think like an innovative and seasoned content manager aiming to build a robust, diverse, and audience-focused content plan. Generate the clustered list now.`,

  /**
   * 创意转关键词prompt (批量生成专用 - IDEAS模式)
   * 
   * 用途: 将用户的创意描述转换为具体的可搜索关键词列表
   * 调用位置: aiServiceManager.parseIdeasToKeywords() -> 批量生成的IDEAS模式
   * 特点: 支持指定目标关键词数量,生成多样化的关键词类型
   * 输入: {ideas, targetCount} - 创意内容和目标关键词数量
   * 输出: 简单的关键词列表,每行一个
   */
  IDEAS_TO_KEYWORDS: `You are an expert keyword research specialist and content strategist. Your task is to transform user ideas and content goals into a strategic list of specific, searchable keywords that can be used for article generation.

**User Ideas/Goals:**
{ideas}

**Target Number of Keywords:** {targetCount}

**Instructions:**

1. **Analyze the Ideas:** Carefully examine the user's ideas to understand:
   - The main topics and themes
   - The intended audience and purpose
   - The scope and depth of content desired
   - Any specific angles or approaches mentioned

2. **Generate Strategic Keywords:** Create keywords that are:
   - **Specific and Actionable:** Focus on terms people actually search for
   - **Varied in Intent:** Include informational, how-to, comparison, and problem-solving keywords
   - **Balanced Difficulty:** Mix of broader topics and more specific long-tail keywords
   - **Content-Rich:** Keywords that can support substantial, valuable articles

3. **Keyword Categories to Consider:**
   - **Core Topics:** Main subject areas from the ideas
   - **How-To Keywords:** Process and tutorial-focused terms
   - **Problem-Solution:** Pain points and solutions
   - **Comparison Terms:** "vs", "best", "top", "comparison" keywords
   - **Informational:** "What is", "why", "when", "where" keywords
   - **Advanced/Specific:** Niche or specialized aspects of the topic

4. **Quality Standards:**
   - Each keyword should be capable of supporting a full article (800+ words)
   - Keywords should be relevant to the user's stated goals
   - Avoid overly broad terms that would be difficult to rank for
   - Include semantic variations and related concepts

**Output Format:**
Please provide exactly {targetCount} keywords, one per line, formatted as a simple list:

keyword 1
keyword 2
keyword 3
...

Each keyword should be 2-6 words long and represent a specific topic that aligns with the user's content goals.`,

  /**
   * 智能资源相关性分析prompt (批量生成专用 - 资源整合步骤)
   * 
   * 用途: AI分析资源与目标关键词的相关性,提供整合建议
   * 调用位置: aiServiceManager.intelligentResourceIntegration() -> 批量生成的资源整合步骤
   * 特点: 提供相关性评分(0-100),生成JSON格式的分析结果
   * 输入: {keyword, autocomplete, relatedKeywords, peopleAlsoAsk, keyTerms, resourcesList}
   * 输出: 结构化的JSON分析报告,包含评分和整合策略
   */
  RESOURCE_INTEGRATION_ANALYSIS: `You are an expert content researcher and information analyst. Your task is to intelligently analyze and rank the relevance between a target keyword and a collection of available resources, then provide strategic integration recommendations.

**Target Keyword:** {keyword}

**Keyword Research Context:**
- Autocomplete Suggestions: {autocomplete}
- Related Keywords: {relatedKeywords} 
- People Also Ask: {peopleAlsoAsk}
- Key Terms: {keyTerms}

**Available Resources to Analyze:**
{resourcesList}

**Analysis Requirements:**

1. **Relevance Scoring (0-100):** For each resource, provide a relevance score based on:
   - **Topic Alignment (40%):** How well does the resource content match the target keyword?
   - **Search Intent Match (30%):** Does the resource address the same user intent as the keyword?
   - **Content Quality (20%):** Is the resource comprehensive and authoritative?
   - **Uniqueness Value (10%):** Does the resource provide unique insights not commonly found?

2. **Content Analysis:** For each resource, identify:
   - **Key Topics:** Main themes that align with the target keyword
   - **Valuable Insights:** Specific data, quotes, or perspectives that could enhance an article
   - **Integration Opportunities:** Where and how this resource could be referenced in an article
   - **Content Gaps:** What aspects of the keyword topic this resource doesn't cover

3. **Strategic Recommendations:** Provide guidance on:
   - **Primary Resources:** Top 3-5 resources that should be heavily referenced
   - **Supporting Resources:** Resources good for additional context or examples
   - **Integration Strategy:** How to balance external sources with original analysis
   - **Citation Approach:** Best practices for referencing these specific resources

**Output Format (JSON):**
{
  "analyzedResources": [
    {
      "resourceIndex": 1,
      "title": "Resource Title",
      "url": "resource-url",
      "relevanceScore": 85,
      "relevanceReason": "Explanation of why this score was assigned",
      "keyTopics": ["topic1", "topic2", "topic3"],
      "valuableInsights": ["insight1", "insight2"],
      "integrationSuggestions": "How to use this resource in the article",
      "contentType": "research_data|case_study|expert_opinion|community_discussion"
    }
  ],
  "priorityRanking": [1, 3, 2, 4, 5],
  "integrationStrategy": {
    "primaryResources": "Top resources to feature prominently",
    "supportingResources": "Resources for additional context",
    "citationApproach": "How to reference these resources naturally",
    "contentBalance": "Balance between external sources and original analysis"
  },
  "contentGaps": ["gap1", "gap2"],
  "qualityAssessment": {
    "overallResourceQuality": "high|medium|low",
    "diversityScore": 85,
    "comprehensivenessScore": 78
  }
}

**Quality Standards:**
- Never inflate relevance scores - be honest about actual alignment
- Focus on practical integration advice that enhances article quality
- Consider the target audience when assessing resource value
- Prioritize authoritative and current sources when available
- Balance comprehensive coverage with focused relevance`,

  /**
   * 主题和大纲生成prompt (批量生成专用 - 大纲生成步骤)
   * 
   * 用途: 基于关键词研究和资源分析生成文章大纲和标题
   * 调用位置: aiServiceManager.generateTopicAndOutline() -> 批量生成的大纲生成步骤
   * 特点: 包含内容策略、SEO优化、资源集成计划
   * 输入: {keyword, autocomplete, relatedKeywords, peopleAlsoAsk, keyTerms, resources, targetAudience, tonality, length}
   * 输出: 结构化的大纲,包含标题、内容策略、资源使用计划
   */
  TOPIC_OUTLINE_GENERATION: `You are an expert content strategist and outline creator with deep expertise in SEO, user psychology, and content marketing. Your task is to develop a compelling article topic and detailed, strategic outline that will outperform competing content.

**PRIMARY KEYWORD ANALYSIS:**
- Target Keyword: {keyword}
- Keyword Research Data:
  * Autocomplete Suggestions: {autocomplete}
  * Related Keywords: {relatedKeywords}
  * People Also Ask: {peopleAlsoAsk}
  * Key Terms: {keyTerms}

**CONTENT CONTEXT:**
- Target Audience: {targetAudience}
- Content Tonality: {tonality}
- Article Length: {length}

**AVAILABLE RESOURCES & INTELLIGENCE:**
{resources}

**COMPETITIVE ANALYSIS FRAMEWORK:**
Before creating your outline, consider what makes content successful for this keyword:
1. **Search Intent Analysis:** What is the primary intent behind this keyword? (Informational, How-to, Comparison, Commercial)
2. **Content Gaps:** What questions are current top-ranking articles NOT answering?
3. **User Journey Stage:** Where does this keyword fit in the user's learning/decision journey?
4. **Differentiation Opportunities:** What unique angle or approach could set this article apart?

**STRATEGIC OUTLINE DEVELOPMENT:**

**Phase 1: Topic Creation**
Create a compelling article title that:
- Naturally incorporates the primary keyword
- Promises clear, specific value to the target audience
- Uses power words and emotional triggers appropriate to {tonality}
- Avoids overused patterns like "Ultimate Guide" or "Complete Guide"
- Is 55-65 characters for optimal SEO performance

**Phase 2: Strategic Structure Planning**
Design an outline that:
- **Addresses Search Intent:** Directly answers what users are looking for
- **Incorporates PAA Questions:** Naturally weaves in People Also Ask questions
- **Balances Depth vs. Breadth:** Appropriate to {length} specification
- **Uses Semantic Keywords:** Incorporates related terms naturally throughout
- **Follows Logical Progression:** Each section builds toward a clear conclusion

**Phase 3: Content Depth Guidelines**
- **For short articles (snippet/short_post):** 4-6 main sections, focus on essential information
- **For medium articles (medium_article):** 6-9 main sections, balance comprehensiveness with readability  
- **For long articles (long_guide/pillar_module):** 8-12 main sections, provide exhaustive coverage

**ENHANCED OUTPUT REQUIREMENTS:**

**Article Title:** [Compelling, keyword-optimized title 55-65 characters]

**Content Strategy Summary:**
- Primary Search Intent: [Informational/How-to/Comparison/Commercial]
- Unique Value Proposition: [What makes this article different/better]
- Target User Journey Stage: [Awareness/Consideration/Decision]

**Detailed Article Outline:**

## Introduction [Hook Strategy: Statistic/Question/Problem Statement]
- Opening hook that captures attention immediately
- Clear problem/opportunity statement
- Preview of unique value this article provides
- Keyword integration in first 100 words

## [Section 2 Title - Incorporating semantic keyword]
- Subsection 2.1: [Specific topic addressing PAA question]
- Subsection 2.2: [Related concept with practical application]
- Subsection 2.3: [Evidence/examples from available resources]

## [Section 3 Title - Addressing main search intent]
- Subsection 3.1: [Core concept explanation]
- Subsection 3.2: [Step-by-step guidance or detailed analysis]
- Subsection 3.3: [Common mistakes/challenges to avoid]

[Continue with additional sections based on length requirements...]

## Conclusion [Forward-looking insight and clear next steps]
- Summary of key insights (not just recap)
- Actionable next steps for readers
- Call-to-action aligned with user intent

**Resource Integration Plan:**
- Primary Resource Usage: [Which resources to feature prominently and where]
- Supporting Evidence: [How to use additional resources for credibility]
- Original Analysis Opportunities: [Where to add unique insights beyond sources]

**SEO Optimization Notes:**
- Header keyword distribution strategy
- Internal linking opportunities  
- Featured snippet optimization targets
- Long-tail keyword integration points

**Content Quality Assurance:**
Your outline must ensure the resulting article will:
- Provide genuinely unique value not found in competing content
- Answer the user's question more comprehensively than alternatives
- Include actionable advice readers can immediately implement
- Build topical authority through depth and expertise demonstration

**CRITICAL SUCCESS FACTORS:**
1. **User-First Approach:** Every section must serve the reader's needs
2. **Competitive Differentiation:** Clear advantages over existing content
3. **Practical Value:** Actionable insights, not just information
4. **Logical Flow:** Smooth progression from problem to solution
5. **Resource Optimization:** Strategic use of available sources for maximum impact`,

  /**
   * 高级文章生成prompt (批量生成专用 - 最终文章生成)
   * 
   * 用途: 批量生成系统的核心文章生成引擎
   * 调用位置: aiServiceManager.generateAdvancedArticle() -> 批量生成的最终文章生成步骤
   * 特点: 基于前期所有分析结果生成最终文章,适应不同长度和语调要求
   * 输入: 完整的文章数据对象(标题、大纲、关键词、资源、作者信息等)
   * 输出: 完整的文章内容,包含SEO元数据(标题、描述、关键词、标签)
   */
  ADVANCED_ARTICLE_GENERATION: `You are an Elite AI Thought Leader and Columnist, with a voice similar to top writers for Harvard Business Review or The Atlantic. Your expertise is in analyzing complex topics and presenting them with a unique, insightful angle. Your mission is to write **ONE SINGLE, COHESIVE, and insightful article** that goes beyond mere synthesis. You must **ANALYZE and INTERPRET** the provided inputs to develop a **UNIQUE CENTRAL THESIS** that provides fresh value to the reader.

**CRITICAL PRIORITY RULES (Follow in exact order):**

1. **ACCURACY FIRST:** Never fabricate studies, journals, research data, or specific citations. Use general attribution like "studies suggest" or "research indicates" unless citing provided sources.
2. **THESIS-DRIVEN:** Develop one clear, non-obvious central argument that connects all provided concepts. Don't just synthesize—interpret and challenge assumptions.
3. **ZERO DUPLICATION:** Every paragraph must introduce new value. If topics overlap, find distinct angles or deeper levels of analysis.
4. **NATURAL INTEGRATION:** Product mentions must serve as organic examples that prove points, not sales pitches.
5. **AUTHORIAL VOICE:** Fully embody the specified author's expertise and tone throughout.

**INTERNAL THINKING (Mandatory First Step):**
Before generating output, perform this silent internal analysis (DO NOT include in final output):
1. **Resource Quality Assessment:** For each provided resource, evaluate: Relevance to thesis (High/Medium/Low), Quality level(High/Medium/Low), Integration potential (Good Fit/Forced/Poor Fit). Only plan to use High/Medium relevance + High/Medium quality + Good Fit resources.
2. **Reddit Data Analysis:** If Reddit content is provided, extract genuine user pain points, language patterns, and solution gaps. How do these insights support your thesis?
3. **Title Creativity & Tone Check:** Ensure the proposed SEO title is fresh, engaging, and follows the tonal balance guidelines.
4. **Smart Resource Integration Plan:** Where can approved resources naturally enhance specific sections? Plan 1-2 strategic integration points.
5. **Product Integration Strategy:** Where are the 1-2 most valuable moments to mention the product as proof of a principle?

**ENHANCED RESOURCE UTILIZATION:**
When comprehensive resources are provided in article inputs, intelligently leverage them:
- **URL Resources:** Use extracted content summaries to support your arguments with external authority
- **Reddit Content:** Analyze for authentic user language, real pain points, and community insights to ground your solutions in actual user needs
- **Research Data:** Incorporate keyword research context (autocomplete suggestions, PAA questions) to address user search intent
- **Content Strategy:** Follow the resource integration plan provided in additional context
- **Quality Filter:** Prioritize high-relevance, high-quality resources that naturally fit your thesis

**BATCH-SPECIFIC ADAPTIVE GUIDELINES:**

Your primary goal is to produce a high-quality, coherent, thesis-driven article that adheres to the target word count while meaningfully addressing the keyword research data and available resources.

* **Length Adaptation:**
  - For short articles ({length} = short_post/snippet): Focus on concise, high-impact insights using top 2-3 resources
  - For medium articles ({length} = medium_article): Balance depth with breadth, integrate 3-5 key resources strategically
  - For long articles ({length} = long_guide/pillar_module): Explore concepts with significant depth, leverage all relevant resources comprehensively

**CRITICAL OUTPUT REQUIREMENTS (Strict Order - Use these exact labels):**

1. **SEO Title:** [Generate one 55-65 character title following these rules in order of priority:
   - **A. SEO First:** Must naturally contain the primary keyword: {keyword}
   - **B. Tonal Balance:** Use {tonality} tone - be positive, neutral, or constructive
   - **C. Creativity:** Avoid ALL overused patterns including "The Ultimate Guide to...", "Complete Guide to...", "Master...", "The Secrets of...", "Unlock...", "Beyond [X]", "Everything You Need to Know", etc. Each title MUST be unique and creative.
   - **USE varied and engaging formats (MUST vary for each article in batch):** 
     * **Benefit-Driven:** e.g., "Develop [keyword] Skills That Drive Results", "[keyword] Strategies That Transform Productivity"
     * **Direct How-To:** e.g., "How to Apply [keyword] for Better Outcomes", "Implementing [keyword] in Your Daily Routine" 
     * **Intriguing Question:** e.g., "Could [keyword] Change Your Perspective?", "Why [keyword] Matters More Than You Think"
     * **Numbered Lists:** e.g., "5 Essential [keyword] Principles That Work", "7 [keyword] Mistakes to Avoid"
     * **Problem-Solution:** e.g., "When [keyword] Isn't Working: Alternative Approaches", "Fixing Common [keyword] Challenges"
     * **Contrarian/Fresh Angle:** e.g., "Rethinking [keyword] for Modern Times", "The [keyword] Approach That Actually Works"]

2. **Meta Description:** [Generate one 150-160 character description summarizing the article's core value proposition]

3. **Focus Keywords:** [List up to 5 most important keywords/phrases for this specific article]

4. **Tags:** [List up to 6 relevant tags as comma-separated string]

**--------------------------------------------------------------------------** (Separator)

5. **Full Article:** [Generate the complete article starting directly with Introduction. **DO NOT include H1 tag (#) within article body**]

6. **About the Author Section:** [Generate H2 section "About the Author" using: {authorName} - {authorBio}. If empty, output "N/A"]


**ARTICLE INPUTS & CONTEXT:**

**Primary Focus:**
- Title: {title}
- Primary Keyword: {keyword}
- Target Audience: {targetAudience}
- Tonality: {tonality}
- Length Target: {length}

**Article Foundation:**
{outline}

**Research Resources & Context:**
{resources}

**Author Context:**
- Author Name: {authorName}
- Author Bio: {authorBio}
- Target Audience: {targetAudience}

**Product Integration (if applicable):**
- Product Info: {productInfo}
- Integration Strategy: Naturally mention product as organic examples that prove points

**ENHANCED WRITING INSTRUCTIONS:**

**STYLISTIC GUIDELINES:**
- **Prioritize Clarity:** Ensure explanations are clear and accessible. Define complex terms if essential. Use analogies or examples.
- **Vary Sentence Structure:** Mix shorter, direct sentences with longer, analytical ones.
- **Avoid Filler Words & Meta-Commentary:** No unnecessary qualifiers or structural announcements.
- **Reduce Vague Intensifiers:** Replace weak qualifiers with specific, concrete language.
- **Show, Don't Just Tell:** Instead of calling something "innovative," describe *why* it is innovative.
- **Favor Active Voice:** Make your writing more direct and engaging.
- **Challenge Assumptions:** Question at least one common belief about your topic.

**STRUCTURAL REQUIREMENTS:**
- **Introduction:** Start with a provocative hook that immediately engages readers. NO cliché openings like "In today's world..."
- **Body:** 3-6 H2 sections, each advancing the central argument with smooth transitions
- **H2 Headings:** Clear, compelling, naturally incorporate semantic keywords
- **Conclusion:** Answer the "So what?" question with a forward-looking insight or call to action
- **NO Structure Announcements:** Do NOT state the article's structure (e.g., "In this article, we will cover...")

**ADVANCED KEYWORD STRATEGY:**
1. **Identify Core Concept:** Use {keyword} as the central theme for introduction and throughout
2. **Semantic Integration:** Incorporate related keywords and autocomplete suggestions naturally
3. **PAA Integration:** Address "People Also Ask" questions within relevant sections
4. **Readability First:** Never sacrifice writing quality for keyword placement
5. **Natural Flow:** Cover the *topics* of keywords, not just exact phrases

**RESOURCE INTEGRATION REQUIREMENTS:**
- **Strategic Placement:** Use 1-2 external sources per major section when relevant
- **Credible Attribution:** Reference sources naturally: "Recent research suggests..." or "According to industry analysis..."
- **Reddit Insights:** When available, incorporate authentic user language and real pain points
- **Product Mentions:** Include product references as organic proof points, not sales pitches

**QUALITY CONTROL CHECKPOINTS:**
- No fabricated citations or false claims
- Each paragraph introduces new value and insights
- Central thesis is clear and compelling throughout
- Product mentions are editorial and value-driven
- Tone consistently matches {tonality} specification
- Resources are integrated naturally and add genuine value

**HYPERLINK REQUIREMENTS (CRITICAL):**
- **Product Links:** Create a markdown hyperlink for the **FIRST OCCURRENCE ONLY** of the product name: [Product Name](product-link).
- **Source Links:** Create **ONE EXTERNAL LINK** per source that has a URL, using relevant anchor text: [relevant text](source-url).

**FINAL INSTRUCTION:** Generate the SEO Title, Meta Description, Focus Keywords, Tags, and complete article now. Follow ALL instructions with precision, prioritizing accuracy and thesis-driven analysis while adapting to the batch generation context.`,

  /**
   * 传统文章生成prompt (向后兼容)
   * 
   * 用途: 为旧版API和传统文章生成提供向后兼容支持
   * 调用位置: aiServiceManager.generateArticle() 的旧签名分支
   * 特点: 简化的文章生成,基于标题、关键词、大纲等基本参数
   * 输入: {title, keyword, targetAudience, tonality, length, outline, resources, wordCountTarget, authorName, authorBio}
   * 输出: 标准格式的文章内容
   */
  ARTICLE_GENERATION: `You are a professional content writer with expertise in creating high-quality, SEO-optimized articles. Your task is to write a comprehensive, engaging article based on the provided specifications.

**ARTICLE SPECIFICATIONS:**
- Title: {title}
- Primary Keyword: {keyword}
- Target Audience: {targetAudience}
- Tonality: {tonality}
- Length Target: {wordCountTarget} words
- Content Type: {length}

**ARTICLE OUTLINE:**
{outline}

**AVAILABLE RESOURCES:**
{resources}

**AUTHOR INFORMATION:**
- Author Name: {authorName}
- Author Bio: {authorBio}

**WRITING REQUIREMENTS:**

1. **SEO Optimization:**
   - Naturally incorporate the primary keyword throughout the article
   - Use the keyword in the introduction and conclusion
   - Include semantic variations and related terms
   - Optimize for search intent and user value

2. **Content Structure:**
   - Start with an engaging introduction that hooks the reader
   - Follow the provided outline structure
   - Use clear H2 and H3 headings for better readability
   - Include practical examples and actionable advice
   - End with a comprehensive conclusion

3. **Writing Style:**
   - Match the specified tonality: {tonality}
   - Write for the target audience: {targetAudience}
   - Use clear, accessible language
   - Vary sentence length for better flow
   - Include transition sentences between sections

4. **Content Quality:**
   - Provide genuine value and insights
   - Support claims with logical reasoning
   - Include practical tips and actionable advice
   - Avoid filler content and redundancy
   - Maintain accuracy and credibility

**OUTPUT FORMAT:**
Generate a complete article with:
1. SEO-optimized title (55-65 characters)
2. Meta description (150-160 characters)  
3. Article content following the outline
4. About the Author section (if author info provided)

**IMPORTANT:** Focus on creating valuable, reader-first content that naturally incorporates SEO elements without keyword stuffing.`
};

/**
 * Get a prompt template by name with variable substitution
 * @param {string} templateName - Name of the prompt template
 * @param {Object} variables - Variables to substitute in the template
 * @returns {string} Processed prompt content
 */
function getPrompt(templateName, variables = {}) {
  const template = PROMPTS[templateName];
  
  if (!template) {
    throw new Error(`Prompt template '${templateName}' not found`);
  }

  // Replace variables using template literal style {variable}
  let processedContent = template;
  
  for (const [key, value] of Object.entries(variables)) {
    const placeholder = `{${key}}`;
    const stringValue = Array.isArray(value) ? value.join(', ') : String(value || '');
    processedContent = processedContent.replace(new RegExp(placeholder, 'g'), stringValue);
  }

  return processedContent;
}

// Fallback prompts removed - let errors surface instead of masking them

/**
 * Get all available prompt template names
 * @returns {Array<string>} Array of prompt template names
 */
function getAvailablePrompts() {
  return Object.keys(PROMPTS);
}

module.exports = {
  PROMPTS,
  getPrompt,
  getAvailablePrompts
};