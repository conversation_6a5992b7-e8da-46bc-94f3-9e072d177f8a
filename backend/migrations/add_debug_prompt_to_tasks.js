/**
 * Migration: Add debug_prompt field to tasks table
 * Date: 2025-01-20
 * Description: Adds debug_prompt TEXT field to store complete prompts sent to AI for debugging
 */

const database = require('../config/database');

async function up() {
  try {
    console.log('Adding debug_prompt column to tasks table...');
    
    await database.run(`
      ALTER TABLE tasks 
      ADD COLUMN IF NOT EXISTS debug_prompt TEXT
    `);
    
    console.log('✅ Successfully added debug_prompt column to tasks table');
    return true;
  } catch (error) {
    console.error('❌ Error adding debug_prompt column:', error);
    throw error;
  }
}

async function down() {
  try {
    console.log('Removing debug_prompt column from tasks table...');
    
    await database.run(`
      ALTER TABLE tasks 
      DROP COLUMN IF EXISTS debug_prompt
    `);
    
    console.log('✅ Successfully removed debug_prompt column from tasks table');
    return true;
  } catch (error) {
    console.error('❌ Error removing debug_prompt column:', error);
    throw error;
  }
}

module.exports = { up, down };

// Run migration if called directly
if (require.main === module) {
  up()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}