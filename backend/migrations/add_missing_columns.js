/**
 * Migration: Add missing columns to blog_posts and user_sessions tables
 * 
 * This migration adds:
 * - author_id column to blog_posts table
 * - session_token column to user_sessions table
 * 
 * Run this with: node backend/migrations/add_missing_columns.js
 */

const database = require('../config/database');

async function addMissingColumns() {
  try {
    console.log('🔄 Starting migration: Add missing columns...');
    
    await database.connect();
    
    // Check and add author_id to blog_posts if it doesn't exist
    console.log('📋 Checking blog_posts table...');
    const authorIdExists = await database.get(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'blog_posts'
        AND column_name = 'author_id'
      );
    `);
    
    if (!authorIdExists.exists) {
      console.log('➕ Adding author_id column to blog_posts table...');
      await database.run(`
        ALTER TABLE blog_posts 
        ADD COLUMN author_id INTEGER,
        ADD CONSTRAINT fk_blog_posts_author 
        <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (author_id) REFERENCES users(id) ON DELETE SET NULL;
      `);
      console.log('✅ Successfully added author_id column');
    } else {
      console.log('ℹ️  author_id column already exists in blog_posts table');
    }
    
    // Check and add session_token to user_sessions if it doesn't exist
    console.log('📋 Checking user_sessions table...');
    const sessionTokenExists = await database.get(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_sessions'
        AND column_name = 'session_token'
      );
    `);
    
    if (!sessionTokenExists.exists) {
      console.log('➕ Adding session_token column to user_sessions table...');
      await database.run(`
        ALTER TABLE user_sessions 
        ADD COLUMN session_token VARCHAR(255) UNIQUE NOT NULL DEFAULT gen_random_uuid()::text;
      `);
      console.log('✅ Successfully added session_token column');
      
      // Remove the default after adding the column
      await database.run(`
        ALTER TABLE user_sessions 
        ALTER COLUMN session_token DROP DEFAULT;
      `);
    } else {
      console.log('ℹ️  session_token column already exists in user_sessions table');
    }
    
    // Now create the indexes
    console.log('📋 Creating indexes...');
    
    // Create index for author_id if it exists
    if (authorIdExists.exists || !authorIdExists.exists) {
      try {
        await database.run('CREATE INDEX IF NOT EXISTS idx_blog_posts_author_id ON blog_posts(author_id);');
        console.log('✅ Created index for blog_posts.author_id');
      } catch (error) {
        console.log('ℹ️  Index idx_blog_posts_author_id may already exist');
      }
    }
    
    // Create index for session_token if it exists
    if (sessionTokenExists.exists || !sessionTokenExists.exists) {
      try {
        await database.run('CREATE INDEX IF NOT EXISTS idx_user_sessions_session_token ON user_sessions(session_token);');
        console.log('✅ Created index for user_sessions.session_token');
      } catch (error) {
        console.log('ℹ️  Index idx_user_sessions_session_token may already exist');
      }
    }
    
    console.log('🎉 Migration completed successfully');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await database.close();
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  addMissingColumns().catch(error => {
    console.error('Migration error:', error);
    process.exit(1);
  });
}

module.exports = addMissingColumns;