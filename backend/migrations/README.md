# Database Migrations

This directory contains database migration scripts for the Writer J application.

## Available Migrations

### 1. add_missing_columns.js
Adds missing columns to existing tables:
- `author_id` column to `blog_posts` table
- `session_token` column to `user_sessions` table

### 2. remove_prompt_templates.js
Removes the deprecated `prompt_templates` table (prompts are now managed in code)

## Running Migrations

To run a migration, use the following command from the project root:

```bash
# Make sure you have DATABASE_URL environment variable set
export DATABASE_URL="postgresql://username:password@host:port/database"

# Run specific migration
node backend/migrations/add_missing_columns.js
node backend/migrations/remove_prompt_templates.js
```

## Migration Order

1. Run `add_missing_columns.js` first to add missing columns
2. Run `remove_prompt_templates.js` if you want to remove the deprecated table

## Notes

- Migrations are idempotent - they check if changes already exist before applying
- Always backup your database before running migrations in production
- Migrations will automatically close the database connection when complete