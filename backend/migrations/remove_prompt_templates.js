/**
 * Migration: Remove prompt_templates table
 * 
 * This migration removes the prompt_templates table since prompts are now
 * managed in code (config/prompts.js) for better version control.
 * 
 * Run this with: node backend/migrations/remove_prompt_templates.js
 */

const database = require('../config/database');

async function removePromptTemplatesTable() {
  try {
    console.log('🔄 Starting migration: Remove prompt_templates table...');
    
    await database.connect();
    
    // Check if table exists first
    const tableExists = await database.get(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'prompt_templates'
      );
    `);
    
    if (tableExists.exists) {
      console.log('📋 Found prompt_templates table, removing...');
      
      // Drop the table and its indexes
      await database.run('DROP TABLE IF EXISTS prompt_templates CASCADE;');
      
      console.log('✅ Successfully removed prompt_templates table');
      console.log('📝 Prompts are now managed in config/prompts.js');
    } else {
      console.log('ℹ️  prompt_templates table does not exist, no action needed');
    }
    
    console.log('🎉 Migration completed successfully');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await database.close();
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  removePromptTemplatesTable().catch(error => {
    console.error('Migration error:', error);
    process.exit(1);
  });
}

module.exports = removePromptTemplatesTable;