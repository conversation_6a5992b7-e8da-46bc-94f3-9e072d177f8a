const express = require('express');
const router = express.Router();
const database = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

// Apply authentication middleware to all resource routes
router.use(authenticateToken);

// GET /api/resources - Get all user resources
router.get('/', async (req, res) => {
  try {
    const { type, tags, favorite } = req.query;
    
    let query = 'SELECT * FROM user_resources WHERE user_id = $1';
    const params = [req.user.id];
    
    // Add filters
    if (type) {
      query += ' AND resource_type = $' + (params.length + 1);
      params.push(type);
    }
    
    if (favorite === 'true') {
      query += ' AND is_favorite = TRUE';
    }
    
    if (tags) {
      query += ' AND tags LIKE $' + (params.length + 1);
      params.push(`%${tags}%`);
    }
    
    query += ' ORDER BY last_used_at DESC, created_at DESC';
    
    const resources = await database.all(query, params);
    
    // Parse JSON fields
    const formattedResources = resources.map(resource => ({
      ...resource,
      tags: resource.tags ? JSON.parse(resource.tags) : []
    }));
    
    res.json({
      message: 'Resources retrieved successfully',
      resources: formattedResources
    });
  } catch (error) {
    console.error('Get resources error:', error);
    res.status(500).json({
      error: 'Failed to retrieve resources',
      message: error.message
    });
  }
});

// POST /api/resources - Create new resource
router.post('/', async (req, res) => {
  try {
    const {
      resource_name,
      resource_type,
      resource_url,
      resource_content,
      description,
      tags = []
    } = req.body;

    // Validate required fields
    if (!resource_name || !resource_type) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'resource_name and resource_type are required'
      });
    }

    // Validate resource type
    const validTypes = ['url', 'reddit', 'document', 'manual'];
    if (!validTypes.includes(resource_type)) {
      return res.status(400).json({
        error: 'Invalid resource type',
        message: 'resource_type must be one of: url, reddit, document, manual'
      });
    }

    // For URL and Reddit types, resource_url is required
    if ((resource_type === 'url' || resource_type === 'reddit') && !resource_url) {
      return res.status(400).json({
        error: 'Missing URL',
        message: 'resource_url is required for URL and Reddit resources'
      });
    }

    // Use database.get with RETURNING to get the inserted resource directly
    const newResource = await database.get(
      `INSERT INTO user_resources (
        user_id, resource_name, resource_type, resource_url, 
        resource_content, description, tags, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP) 
      RETURNING *`,
      [
        req.user.id,
        resource_name,
        resource_type,
        resource_url || null,
        resource_content || null,
        description || null,
        JSON.stringify(tags)
      ]
    );

    console.log('🔍 Created resource:', newResource);
    
    if (!newResource) {
      console.error('❌ Resource creation failed - no data returned');
      throw new Error('Failed to create resource');
    }

    // Format the response
    const formattedResource = {
      ...newResource,
      tags: newResource.tags ? JSON.parse(newResource.tags) : []
    };

    res.status(201).json({
      message: 'Resource created successfully',
      resource: formattedResource
    });
  } catch (error) {
    console.error('Create resource error:', error);
    res.status(500).json({
      error: 'Failed to create resource',
      message: error.message
    });
  }
});

// PUT /api/resources/:id - Update resource
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      resource_name,
      resource_type,
      resource_url,
      resource_content,
      description,
      tags,
      is_favorite
    } = req.body;

    // Check if resource exists and belongs to user
    const existingResource = await database.get(
      'SELECT * FROM user_resources WHERE id = $1 AND user_id = $2',
      [id, req.user.id]
    );

    if (!existingResource) {
      return res.status(404).json({
        error: 'Resource not found',
        message: 'Resource not found or you do not have permission to update it'
      });
    }

    // Build update query dynamically
    const updates = [];
    const params = [];
    let paramIndex = 1;

    if (resource_name !== undefined) {
      updates.push(`resource_name = $${paramIndex++}`);
      params.push(resource_name);
    }
    if (resource_type !== undefined) {
      updates.push(`resource_type = $${paramIndex++}`);
      params.push(resource_type);
    }
    if (resource_url !== undefined) {
      updates.push(`resource_url = $${paramIndex++}`);
      params.push(resource_url);
    }
    if (resource_content !== undefined) {
      updates.push(`resource_content = $${paramIndex++}`);
      params.push(resource_content);
    }
    if (description !== undefined) {
      updates.push(`description = $${paramIndex++}`);
      params.push(description);
    }
    if (tags !== undefined) {
      updates.push(`tags = $${paramIndex++}`);
      params.push(JSON.stringify(tags));
    }
    if (is_favorite !== undefined) {
      updates.push(`is_favorite = $${paramIndex++}`);
      params.push(is_favorite);
    }

    updates.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id, req.user.id);

    await database.run(
      `UPDATE user_resources SET ${updates.join(', ')} WHERE id = $${paramIndex++} AND user_id = $${paramIndex}`,
      params
    );

    const updatedResource = await database.get(
      'SELECT * FROM user_resources WHERE id = $1 AND user_id = $2',
      [id, req.user.id]
    );

    // Format the response
    const formattedResource = {
      ...updatedResource,
      tags: updatedResource.tags ? JSON.parse(updatedResource.tags) : []
    };

    res.json({
      message: 'Resource updated successfully',
      resource: formattedResource
    });
  } catch (error) {
    console.error('Update resource error:', error);
    res.status(500).json({
      error: 'Failed to update resource',
      message: error.message
    });
  }
});

// POST /api/resources/:id/use - Mark resource as used (increment usage count)
router.post('/:id/use', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if resource exists and belongs to user
    const resource = await database.get(
      'SELECT * FROM user_resources WHERE id = $1 AND user_id = $2',
      [id, req.user.id]
    );

    if (!resource) {
      return res.status(404).json({
        error: 'Resource not found',
        message: 'Resource not found or you do not have permission to access it'
      });
    }

    await database.run(
      `UPDATE user_resources 
       SET usage_count = usage_count + 1, last_used_at = CURRENT_TIMESTAMP 
       WHERE id = $1 AND user_id = $2`,
      [id, req.user.id]
    );

    res.json({
      message: 'Resource usage recorded successfully'
    });
  } catch (error) {
    console.error('Record resource usage error:', error);
    res.status(500).json({
      error: 'Failed to record resource usage',
      message: error.message
    });
  }
});

// DELETE /api/resources/:id - Delete resource
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if resource exists and belongs to user
    const resource = await database.get(
      'SELECT * FROM user_resources WHERE id = $1 AND user_id = $2',
      [id, req.user.id]
    );

    if (!resource) {
      return res.status(404).json({
        error: 'Resource not found',
        message: 'Resource not found or you do not have permission to delete it'
      });
    }

    await database.run(
      'DELETE FROM user_resources WHERE id = $1 AND user_id = $2',
      [id, req.user.id]
    );

    res.json({
      message: 'Resource deleted successfully'
    });
  } catch (error) {
    console.error('Delete resource error:', error);
    res.status(500).json({
      error: 'Failed to delete resource',
      message: error.message
    });
  }
});

// GET /api/resources/stats - Get resource statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = await database.get(
      `SELECT 
        COUNT(*) as total_resources,
        COUNT(CASE WHEN resource_type = 'url' THEN 1 END) as url_resources,
        COUNT(CASE WHEN resource_type = 'reddit' THEN 1 END) as reddit_resources,
        COUNT(CASE WHEN resource_type = 'document' THEN 1 END) as document_resources,
        COUNT(CASE WHEN resource_type = 'manual' THEN 1 END) as manual_resources,
        COUNT(CASE WHEN is_favorite = TRUE THEN 1 END) as favorite_resources,
        SUM(usage_count) as total_usage
       FROM user_resources 
       WHERE user_id = $1`,
      [req.user.id]
    );

    res.json({
      message: 'Resource statistics retrieved successfully',
      stats: stats || {
        total_resources: 0,
        url_resources: 0,
        reddit_resources: 0,
        document_resources: 0,
        manual_resources: 0,
        favorite_resources: 0,
        total_usage: 0
      }
    });
  } catch (error) {
    console.error('Get resource stats error:', error);
    res.status(500).json({
      error: 'Failed to retrieve resource statistics',
      message: error.message
    });
  }
});

module.exports = router;