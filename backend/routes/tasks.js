const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { authenticateToken, checkResourceOwnership } = require('../middleware/auth');
const database = require('../config/database');
const aiServiceManager = require('../services/aiServiceManager');

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Helper function to auto-save new resources to user's resource library
async function autoSaveResources(userId, resources, resourceType = 'url') {
  if (!resources || !Array.isArray(resources) || resources.length === 0) {
    return;
  }

  try {
    for (const resource of resources) {
      // Skip if resource doesn't have necessary fields
      if (!resource.title && !resource.url && !resource.content) {
        continue;
      }

      // Check if this resource already exists for the user
      const existingResource = await database.get(`
        SELECT id FROM user_resources 
        WHERE user_id = ? AND (
          (resource_url = ? AND resource_url IS NOT NULL) OR
          (resource_name = ? AND resource_url IS NULL)
        )
      `, [userId, resource.url || null, resource.title || resource.name || 'Untitled Resource']);

      // Skip if resource already exists
      if (existingResource) {
        console.log(`Resource already exists, skipping: ${resource.title || resource.url}`);
        continue;
      }

      // Determine resource type based on content
      let detectedType = resourceType;
      if (resource.url) {
        if (resource.url.includes('reddit.com')) {
          detectedType = 'reddit';
        } else {
          detectedType = 'url';
        }
      } else if (resource.content && !resource.url) {
        detectedType = 'manual';
      }

      // Prepare resource data for saving
      const resourceData = {
        resource_name: resource.title || resource.name || (resource.url ? `Resource from ${new URL(resource.url).hostname}` : 'Manual Resource'),
        resource_type: detectedType,
        resource_url: resource.url || null,
        resource_content: resource.content || resource.description || '',
        description: resource.description || `Auto-saved from task on ${new Date().toLocaleDateString()}`,
        tags: JSON.stringify(resource.tags || ['auto-saved', 'from-task'])
      };

      // Insert the resource
      await database.run(`
        INSERT INTO user_resources (
          user_id, resource_name, resource_type, resource_url, 
          resource_content, description, tags, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [
        userId,
        resourceData.resource_name,
        resourceData.resource_type,
        resourceData.resource_url,
        resourceData.resource_content,
        resourceData.description,
        resourceData.tags
      ]);

      console.log(`✅ Auto-saved resource: ${resourceData.resource_name} (${resourceData.resource_type})`);
    }
  } catch (error) {
    console.error('Error auto-saving resources:', error);
    // Don't throw error - this is a background enhancement, shouldn't break task updates
  }
}

// GET /api/tasks - Get all tasks for the authenticated user
router.get('/', async (req, res) => {
  try {
    const { status, sortBy = 'updated_at', sortOrder = 'DESC', page = 1, limit = 20 } = req.query;

    let query = 'SELECT * FROM tasks WHERE user_id = ?';
    const params = [req.user.id];

    // Add status filter if provided
    if (status) {
      query += ' AND status = ?';
      params.push(status);
    }

    // Add sorting
    const validSortFields = ['name', 'status', 'created_at', 'updated_at'];
    const validSortOrders = ['ASC', 'DESC'];

    if (validSortFields.includes(sortBy) && validSortOrders.includes(sortOrder.toUpperCase())) {
      query += ` ORDER BY ${sortBy} ${sortOrder.toUpperCase()}`;
    } else {
      query += ' ORDER BY updated_at DESC';
    }

    // Add pagination
    const offset = (parseInt(page) - 1) * parseInt(limit);
    query += ' LIMIT ? OFFSET ?';
    params.push(parseInt(limit), offset);

    const tasks = await database.all(query, params);

    // Get total count for pagination
    let countQuery = 'SELECT COUNT(*) as total FROM tasks WHERE user_id = ?';
    const countParams = [req.user.id];

    if (status) {
      countQuery += ' AND status = ?';
      countParams.push(status);
    }

    const { total } = await database.get(countQuery, countParams);

    // Parse JSON fields for response
    const formattedTasks = tasks.map(task => ({
      ...task,
      keywords: task.keywords ? JSON.parse(task.keywords) : [],
      selectedTopics: task.selected_topics ? JSON.parse(task.selected_topics) : [],
      topicSuggestions: task.topic_suggestions ? JSON.parse(task.topic_suggestions) : [],
      keywordResearchData: task.keyword_research_data ? JSON.parse(task.keyword_research_data) : null,
      keywordResearchSelections: task.keyword_research_selections ? JSON.parse(task.keyword_research_selections) : [],
      redditSources: task.reddit_sources ? JSON.parse(task.reddit_sources) : [],
      sources: task.sources ? JSON.parse(task.sources) : [],
      // topicSources deprecated - use comprehensiveResources instead
      comprehensiveResources: task.comprehensive_resources ? JSON.parse(task.comprehensive_resources) : [],
      productInfo: task.product_info ? JSON.parse(task.product_info) : {},
      eeatProfile: task.eeat_profile ? JSON.parse(task.eeat_profile) : {},
      outputParameters: task.output_parameters ? JSON.parse(task.output_parameters) : {}
    }));

    res.json({
      tasks: formattedTasks,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({
      error: 'Failed to retrieve tasks',
      message: 'An error occurred while retrieving your tasks'
    });
  }
});

// GET /api/tasks/stats - Get task statistics for the user
router.get('/stats', async (req, res) => {
  try {
    const stats = await database.all(
      `SELECT
        status,
        COUNT(*) as count
      FROM tasks
      WHERE user_id = ?
      GROUP BY status`,
      [req.user.id]
    );

    const totalTasks = await database.get(
      'SELECT COUNT(*) as total FROM tasks WHERE user_id = ?',
      [req.user.id]
    );

    res.json({
      totalTasks: totalTasks.total,
      statusBreakdown: stats
    });

  } catch (error) {
    console.error('Get task stats error:', error);
    res.status(500).json({
      error: 'Failed to retrieve task statistics',
      message: 'An error occurred while retrieving task statistics'
    });
  }
});

// GET /api/tasks/:id - Get a specific task
router.get('/:id', checkResourceOwnership('task'), async (req, res) => {
  try {
    const task = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (!task) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found'
      });
    }

    // Parse JSON fields
    const formattedTask = {
      ...task,
      keywords: task.keywords ? JSON.parse(task.keywords) : [],
      selectedTopics: task.selected_topics ? JSON.parse(task.selected_topics) : [],
      topicSuggestions: task.topic_suggestions ? JSON.parse(task.topic_suggestions) : [],
      keywordResearchData: task.keyword_research_data ? JSON.parse(task.keyword_research_data) : null,
      keywordResearchSelections: task.keyword_research_selections ? JSON.parse(task.keyword_research_selections) : [],
      redditSources: task.reddit_sources ? JSON.parse(task.reddit_sources) : [],
      sources: task.sources ? JSON.parse(task.sources) : [],
      // topicSources deprecated - use comprehensiveResources instead
      comprehensiveResources: task.comprehensive_resources ? JSON.parse(task.comprehensive_resources) : [],
      productInfo: task.product_info ? JSON.parse(task.product_info) : {},
      eeatProfile: task.eeat_profile ? JSON.parse(task.eeat_profile) : {},
      outputParameters: task.output_parameters ? JSON.parse(task.output_parameters) : {}
    };

    res.json({ task: formattedTask });

  } catch (error) {
    console.error('Get task error:', error);
    res.status(500).json({
      error: 'Failed to retrieve task',
      message: 'An error occurred while retrieving the task'
    });
  }
});

// POST /api/tasks/auto-create - Auto-create a new task with timestamp
router.post('/auto-create', async (req, res) => {
  try {
    const taskId = uuidv4();
    const timestamp = new Date().toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
    const taskName = `Article Task - ${timestamp}`;

    // Prepare initial data with defaults
    const defaultData = {
      keywords: [],
      selectedTopics: [],
      topicSuggestions: [],
      keywordResearchData: null,
      keywordResearchSelections: [],
      redditSources: [],
      sources: [],
      // topicSources deprecated
      comprehensiveResources: [],
      productInfo: {
        name: '',
        link: '',
        description: '',
        features: []
      },
      eeatProfile: {
        authorName: '',
        authorBio: '',
        targetAudience: '',
        articleGoal: ''
      },
      outputParameters: {
        tonality: 'informative',
        length: 'medium_article',
        format: 'markdown'
      }
    };

    // Create task
    await database.run(
      `INSERT INTO tasks (
        id, user_id, name, status, current_step,
        keywords, selected_topics, topic_suggestions,
        keyword_research_data, keyword_research_selections,
        reddit_sources, sources, topic_sources, comprehensive_resources,
        product_info, eeat_profile, output_parameters
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        taskId,
        req.user.id,
        taskName,
        'Draft - Step 1',
        0,
        JSON.stringify(defaultData.keywords),
        JSON.stringify(defaultData.selectedTopics),
        JSON.stringify(defaultData.topicSuggestions),
        JSON.stringify(defaultData.keywordResearchData),
        JSON.stringify(defaultData.keywordResearchSelections),
        JSON.stringify(defaultData.redditSources),
        JSON.stringify(defaultData.sources),
        JSON.stringify(defaultData.topicSources),
        JSON.stringify(defaultData.comprehensiveResources),
        JSON.stringify(defaultData.productInfo),
        JSON.stringify(defaultData.eeatProfile),
        JSON.stringify(defaultData.outputParameters)
      ]
    );

    // Get the created task
    const createdTask = await database.get(
      'SELECT * FROM tasks WHERE id = ?',
      [taskId]
    );

    const formattedTask = {
      ...createdTask,
      keywords: JSON.parse(createdTask.keywords),
      selectedTopics: JSON.parse(createdTask.selected_topics),
      topicSuggestions: JSON.parse(createdTask.topic_suggestions),
      keywordResearchData: JSON.parse(createdTask.keyword_research_data),
      keywordResearchSelections: JSON.parse(createdTask.keyword_research_selections),
      redditSources: JSON.parse(createdTask.reddit_sources),
      sources: JSON.parse(createdTask.sources),
      // topicSources deprecated
      productInfo: JSON.parse(createdTask.product_info),
      eeatProfile: JSON.parse(createdTask.eeat_profile),
      outputParameters: JSON.parse(createdTask.output_parameters)
    };

    res.status(201).json({
      message: 'Task created successfully',
      task: formattedTask
    });

  } catch (error) {
    console.error('Auto-create task error:', error);
    res.status(500).json({
      error: 'Failed to create task',
      message: 'An error occurred while creating the task'
    });
  }
});

// POST /api/tasks - Create a new task (legacy endpoint)
router.post('/', async (req, res) => {
  try {
    const { name, initialData = {} } = req.body;

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return res.status(400).json({
        error: 'Invalid task name',
        message: 'Task name is required and must be a non-empty string'
      });
    }

    const taskId = uuidv4();
    const taskName = name.trim().slice(0, 255); // Limit name length

    // Prepare initial data with defaults
    const defaultData = {
      keywords: [],
      selectedTopics: [],
      topicSuggestions: [],
      keywordResearchData: null,
      keywordResearchSelections: [],
      redditSources: [],
      sources: [],
      // topicSources deprecated
      comprehensiveResources: [],
      productInfo: {
        name: '',
        link: '',
        description: '',
        features: []
      },
      eeatProfile: {
        authorName: '',
        authorBio: '',
        targetAudience: '',
        articleGoal: ''
      },
      outputParameters: {
        tonality: 'informative',
        length: 'medium_article',
        format: 'markdown'
      }
    };

    const mergedData = { ...defaultData, ...initialData };

    // Create task
    await database.run(
      `INSERT INTO tasks (
        id, user_id, name, status, current_step,
        keywords, selected_topics, topic_suggestions,
        keyword_research_data, keyword_research_selections,
        reddit_sources, sources, topic_sources, comprehensive_resources,
        product_info, eeat_profile, output_parameters
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        taskId,
        req.user.id,
        taskName,
        'Draft - Step 1',
        0,
        JSON.stringify(mergedData.keywords),
        JSON.stringify(mergedData.selectedTopics),
        JSON.stringify(mergedData.topicSuggestions),
        JSON.stringify(mergedData.keywordResearchData),
        JSON.stringify(mergedData.keywordResearchSelections),
        JSON.stringify(mergedData.redditSources),
        JSON.stringify(mergedData.sources),
        JSON.stringify(mergedData.topicSources),
        JSON.stringify(mergedData.comprehensiveResources || []),
        JSON.stringify(mergedData.productInfo),
        JSON.stringify(mergedData.eeatProfile),
        JSON.stringify(mergedData.outputParameters)
      ]
    );

    // Return the created task
    const createdTask = await database.get(
      'SELECT * FROM tasks WHERE id = ?',
      [taskId]
    );

    const formattedTask = {
      ...createdTask,
      keywords: JSON.parse(createdTask.keywords),
      selectedTopics: JSON.parse(createdTask.selected_topics),
      topicSuggestions: JSON.parse(createdTask.topic_suggestions),
      keywordResearchData: JSON.parse(createdTask.keyword_research_data),
      keywordResearchSelections: JSON.parse(createdTask.keyword_research_selections),
      redditSources: JSON.parse(createdTask.reddit_sources),
      sources: JSON.parse(createdTask.sources),
      // topicSources deprecated
      productInfo: JSON.parse(createdTask.product_info),
      eeatProfile: JSON.parse(createdTask.eeat_profile),
      outputParameters: JSON.parse(createdTask.output_parameters)
    };

    res.status(201).json({
      message: 'Task created successfully',
      task: formattedTask
    });

  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({
      error: 'Failed to create task',
      message: 'An error occurred while creating the task'
    });
  }
});

// PUT /api/tasks/:id - Update a task
router.put('/:id', checkResourceOwnership('task'), async (req, res) => {
  try {
    const {
      name,
      status,
      currentStep,
      keywords,
      selectedTopics,
      topicSuggestions,
      keywordResearchData,
      keywordResearchSelections,
      redditSources,
      sources,
      topicSources,
      comprehensiveResources,
      productInfo,
      eeatProfile,
      outputParameters,
      generatedArticle,
      generated_article  // Accept both camelCase and snake_case
    } = req.body;

    // Use either field name for generated article
    const articleContent = generatedArticle || generated_article;

    // Debug article content updates
    if (articleContent !== undefined) {
      console.log('=== ARTICLE UPDATE DEBUG ===');
      console.log('Received article content length:', articleContent ? articleContent.length : 'null/undefined');
      console.log('Article content preview:', articleContent ? articleContent.substring(0, 100) + '...' : 'null');
      console.log('============================');
    }

    // Build update query dynamically based on provided fields
    const updates = [];
    const params = [];

    if (name !== undefined) {
      updates.push('name = ?');
      params.push(name.toString().trim().slice(0, 255));
    }

    if (status !== undefined) {
      updates.push('status = ?');
      params.push(status.toString());
    }

    if (currentStep !== undefined) {
      updates.push('current_step = ?');
      params.push(parseInt(currentStep));
    }

    if (keywords !== undefined) {
      updates.push('keywords = ?');
      params.push(JSON.stringify(keywords));
    }

    if (selectedTopics !== undefined) {
      updates.push('selected_topics = ?');
      params.push(JSON.stringify(selectedTopics));
    }

    if (topicSuggestions !== undefined) {
      updates.push('topic_suggestions = ?');
      params.push(JSON.stringify(topicSuggestions));
    }

    if (keywordResearchData !== undefined) {
      updates.push('keyword_research_data = ?');
      params.push(JSON.stringify(keywordResearchData));
    }

    if (keywordResearchSelections !== undefined) {
      updates.push('keyword_research_selections = ?');
      params.push(JSON.stringify(keywordResearchSelections));
    }

    if (redditSources !== undefined) {
      updates.push('reddit_sources = ?');
      params.push(JSON.stringify(redditSources));
    }

    if (sources !== undefined) {
      updates.push('sources = ?');
      params.push(JSON.stringify(sources));
    }

    if (topicSources !== undefined) {
      updates.push('topic_sources = ?');
      params.push(JSON.stringify(topicSources));
    }

    if (comprehensiveResources !== undefined) {
      updates.push('comprehensive_resources = ?');
      params.push(JSON.stringify(comprehensiveResources));
    }

    if (productInfo !== undefined) {
      updates.push('product_info = ?');
      params.push(JSON.stringify(productInfo));
    }

    if (eeatProfile !== undefined) {
      updates.push('eeat_profile = ?');
      params.push(JSON.stringify(eeatProfile));
    }

    if (outputParameters !== undefined) {
      updates.push('output_parameters = ?');
      params.push(JSON.stringify(outputParameters));
    }

    if (articleContent !== undefined) {
      updates.push('generated_article = ?');
      params.push(articleContent);

      // Auto-update task name to article title when article is completed
      if (articleContent && typeof articleContent === 'string') {
        try {
          // First try to extract SEO title from structured format
          const seoTitleMatch = articleContent.match(/\*\*SEO Title:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                               articleContent.match(/SEO Title:\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                               articleContent.match(/\*\*SEO Title:\*\*\s*([^\n]+?)(?:\s*\n|\s*$)/i) ||
                               articleContent.match(/SEO Title:\s*([^\n]+?)(?:\s*\n|\s*$)/i);

          let articleTitle = null;

          if (seoTitleMatch && seoTitleMatch[1]) {
            articleTitle = seoTitleMatch[1].trim();
            // Remove any markdown formatting and brackets
            articleTitle = articleTitle.replace(/[#*_`\[\]]/g, '').trim();
            // Remove any trailing periods or punctuation that might be part of the format
            articleTitle = articleTitle.replace(/[.]*$/, '').trim();
            // Stop at any meta description content that might have been captured
            articleTitle = articleTitle.split(/meta\s*description/i)[0].trim();
          } else {
            // Fallback: try to extract from markdown header or first line
            const titleMatch = articleContent.match(/^#\s*(.+)$/m) ||
                             articleContent.match(/^(.+)$/m);

            if (titleMatch && titleMatch[1]) {
              articleTitle = titleMatch[1].trim();
              // Remove any markdown formatting
              articleTitle = articleTitle.replace(/[#*_`]/g, '').trim();
            }
          }

          if (articleTitle) {
            // Limit title length
            articleTitle = articleTitle.slice(0, 200);
            updates.push('name = ?');
            params.push(articleTitle); // Add to end of params array
          }
        } catch (error) {
          console.warn('Failed to extract article title:', error);
          // Continue without updating the name
        }
      }
    }

    if (updates.length === 0) {
      return res.status(400).json({
        error: 'No updates provided',
        message: 'At least one field must be provided for update'
      });
    }

    // Always update the updated_at timestamp
    updates.push('updated_at = CURRENT_TIMESTAMP');

    // Add task ID and user ID to params
    params.push(req.params.id, req.user.id);

    const query = `UPDATE tasks SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`;

    // Debug query and params
    console.log('=== UPDATE QUERY DEBUG ===');
    console.log('Query:', query);
    console.log('Params:', params);
    console.log('Updates:', updates);
    console.log('========================');

    const result = await database.run(query, params);

    if (result.changes === 0) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found or updated'
      });
    }

    // Auto-save resources to user's resource library
    try {
      if (sources !== undefined) {
        console.log('🔄 Auto-saving sources to resource library...');
        await autoSaveResources(req.user.id, sources, 'url');
      }
      
      if (comprehensiveResources !== undefined) {
        console.log('🔄 Auto-saving comprehensive resources to resource library...');
        await autoSaveResources(req.user.id, comprehensiveResources, 'url');
      }
      
      if (redditSources !== undefined) {
        console.log('🔄 Auto-saving Reddit sources to resource library...');
        await autoSaveResources(req.user.id, redditSources, 'reddit');
      }
    } catch (autoSaveError) {
      console.error('Auto-save resources failed (non-blocking):', autoSaveError);
      // Don't fail the task update if auto-save fails
    }

    // Return updated task
    const updatedTask = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    const formattedTask = {
      ...updatedTask,
      keywords: updatedTask.keywords ? JSON.parse(updatedTask.keywords) : [],
      selectedTopics: updatedTask.selected_topics ? JSON.parse(updatedTask.selected_topics) : [],
      topicSuggestions: updatedTask.topic_suggestions ? JSON.parse(updatedTask.topic_suggestions) : [],
      keywordResearchData: updatedTask.keyword_research_data ? JSON.parse(updatedTask.keyword_research_data) : null,
      keywordResearchSelections: updatedTask.keyword_research_selections ? JSON.parse(updatedTask.keyword_research_selections) : [],
      redditSources: updatedTask.reddit_sources ? JSON.parse(updatedTask.reddit_sources) : [],
      sources: updatedTask.sources ? JSON.parse(updatedTask.sources) : [],
      // topicSources deprecated
      comprehensiveResources: updatedTask.comprehensive_resources ? JSON.parse(updatedTask.comprehensive_resources) : [],
      productInfo: updatedTask.product_info ? JSON.parse(updatedTask.product_info) : {},
      eeatProfile: updatedTask.eeat_profile ? JSON.parse(updatedTask.eeat_profile) : {},
      outputParameters: updatedTask.output_parameters ? JSON.parse(updatedTask.output_parameters) : {}
    };

    res.json({
      message: 'Task updated successfully',
      task: formattedTask
    });

  } catch (error) {
    console.error('Update task error:', error);
    res.status(500).json({
      error: 'Failed to update task',
      message: 'An error occurred while updating the task'
    });
  }
});

// Progress tracking utility
function sendProgress(res, data) {
  if (res.headersSent) return;
  res.write(`data: ${JSON.stringify(data)}\n\n`);
}

// POST /api/tasks/test-batch - Test endpoint for batch generation connectivity
router.post('/test-batch', async (req, res) => {
  try {
    console.log('🧪 Test batch endpoint called');
    console.log('📋 Request body size:', JSON.stringify(req.body).length, 'bytes');

    // Simple response to test connectivity
    res.json({
      success: true,
      message: 'Test batch endpoint working',
      timestamp: new Date().toISOString(),
      requestSize: JSON.stringify(req.body).length
    });
  } catch (error) {
    console.error('Test batch error:', error);
    res.status(500).json({
      error: 'Test failed',
      message: error.message
    });
  }
});

// POST /api/tasks/advanced-batch-generate - Enhanced batch generation with multiple input modes
router.post('/advanced-batch-generate', async (req, res) => {
  const startTime = Date.now();
  console.log('🚀 Advanced batch generation started at:', new Date().toISOString());
  console.log('📋 Request body size:', JSON.stringify(req.body).length, 'bytes');

  try {
    const { inputMode, content, globalSettings = {}, useSSE = false } = req.body;

    // Log request details
    console.log('📊 Request details:');
    console.log('   - Input mode:', inputMode);
    console.log('   - Content length:', content ? content.length : 0);
    console.log('   - Global settings keys:', Object.keys(globalSettings));
    console.log('   - Use SSE:', useSSE);
    console.log('   - User ID:', req.user?.id);
    console.log('   - Request IP:', req.ip || req.connection?.remoteAddress);

    // Setup SSE if requested
    if (useSSE) {
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      });
      
      sendProgress(res, {
        type: 'start',
        message: '开始批量文章生成...',
        progress: 0
      });
    }

    // Validate input mode
    const validModes = ['IDEAS', 'KEYWORDS', 'TITLES'];
    if (!inputMode || !validModes.includes(inputMode)) {
      return res.status(400).json({
        error: 'Invalid input mode',
        message: 'Input mode must be one of: IDEAS, KEYWORDS, TITLES'
      });
    }

    // Validate content
    if (!content || (typeof content !== 'string' && !Array.isArray(content))) {
      return res.status(400).json({
        error: 'Content is required',
        message: 'Please provide content based on the selected input mode'
      });
    }

    console.log(`🚀 Starting advanced batch generation in ${inputMode} mode`);
    
    if (useSSE) {
      sendProgress(res, {
        type: 'step',
        step: 1,
        totalSteps: 6,
        message: '解析输入内容并生成关键词...',
        progress: 5
      });
    }
    
    // Set up enhanced default settings
    const settings = {
      tonality: globalSettings.tonality || 'informative',
      length: globalSettings.length || 'long_article',
      format: globalSettings.format || 'markdown',
      authorName: globalSettings.authorName || '',
      authorBio: globalSettings.authorBio || '',
      targetAudience: globalSettings.targetAudience || '',
      productInfo: globalSettings.productInfo || {
        name: '',
        link: '',
        description: '',
        features: []
      },
      sources: globalSettings.sources || [],
      redditPosts: globalSettings.redditPosts || [],
      targetCount: globalSettings.targetCount || 10,
      useRealSerperAPI: globalSettings.useRealSerperAPI || false,
      ...globalSettings
    };

    // Step 1: Parse input and generate keywords based on mode
    let keywords = [];
    let originalInput = content;

    switch(inputMode) {
      case 'KEYWORDS':
        keywords = Array.isArray(content) ? content : content.split('\n').map(k => k.trim()).filter(k => k.length > 0);
        // For KEYWORDS mode: limit to targetCount or input count, whichever is smaller
        if (keywords.length > settings.targetCount) {
          keywords = keywords.slice(0, settings.targetCount);
          console.log(`📊 KEYWORDS mode: Limited to ${settings.targetCount} keywords from ${keywords.length} input`);
        }
        break;

      case 'TITLES':
        if (useSSE) {
          sendProgress(res, {
            type: 'substep',
            message: '从标题中提取关键词...',
            progress: 8
          });
        }
        const titles = Array.isArray(content) ? content : content.split('\n').map(t => t.trim()).filter(t => t.length > 0);
        keywords = await Promise.all(
          titles.map(title => aiServiceManager.extractKeywordFromTitle(title))
        );
        // For TITLES mode: 1 article per title, no additional limiting
        console.log(`📊 TITLES mode: Processing ${keywords.length} titles (1 article each)`);
        break;

      case 'IDEAS':
        if (useSSE) {
          sendProgress(res, {
            type: 'substep',
            message: '将创意转换为关键词...',
            progress: 8
          });
        }
        // For IDEAS mode: generate up to targetCount keywords, but max 5
        const ideaTargetCount = Math.min(settings.targetCount, 5);
        keywords = await aiServiceManager.parseIdeasToKeywords(content, ideaTargetCount);
        console.log(`📊 IDEAS mode: Generated ${keywords.length} keywords (max ${ideaTargetCount})`);
        break;
    }

    if (keywords.length === 0) {
      return res.status(400).json({
        error: 'No keywords generated',
        message: 'Unable to generate keywords from the provided content'
      });
    }

    // Global safety limit: never exceed 20 articles
    if (keywords.length > 20) {
      keywords = keywords.slice(0, 20);
      console.log(`⚠️ Applied global limit: reduced to 20 articles from ${keywords.length}`);
    }

    console.log(`📋 Generated ${keywords.length} keywords:`, keywords);

    if (useSSE) {
      sendProgress(res, {
        type: 'step',
        step: 2,
        totalSteps: 6,
        message: `开始关键词研究 (${keywords.length} 个关键词)...`,
        progress: 15,
        details: {
          totalKeywords: keywords.length,
          keywords: keywords.slice(0, 5) // 只显示前5个
        }
      });
    }

    // Step 2: Enhanced keyword research for each keyword
    console.log(`🔍 Starting enhanced keyword research for ${keywords.length} keywords`);
    const keywordResearchResults = await Promise.all(
      keywords.map(async (keyword, index) => {
        console.log(`🔍 Researching keyword ${index + 1}/${keywords.length}: "${keyword}"`);
        
        if (useSSE) {
          sendProgress(res, {
            type: 'substep',
            message: `Researching keyword ${index + 1}/${keywords.length}: "${keyword}"`,
            progress: 15 + (index / keywords.length) * 15 // 15-30%
          });
        }
        
        return await aiServiceManager.enhancedKeywordResearch(keyword, settings.useRealSerperAPI);
      })
    );

    if (useSSE) {
      sendProgress(res, {
        type: 'step',
        step: 3,
        totalSteps: 6,
        message: 'Starting batch article generation...',
        progress: 30,
        details: {
          totalArticles: keywords.length,
          articlesGenerated: 0
        }
      });
    }

    // Step 3: Batch process articles with enhanced workflow
    const results = [];
    const errors = [];
    const BATCH_SIZE = 3;
    let totalProcessedArticles = 0;

    for (let i = 0; i < keywords.length; i += BATCH_SIZE) {
      const batch = keywords.slice(i, i + BATCH_SIZE);
      const batchNumber = Math.floor(i/BATCH_SIZE) + 1;
      const totalBatches = Math.ceil(keywords.length/BATCH_SIZE);
      
      console.log(`📦 Processing batch ${batchNumber}/${totalBatches}`);
      
      if (useSSE) {
        sendProgress(res, {
          type: 'batch_start',
          message: `Processing batch ${batchNumber}/${totalBatches} (articles ${i + 1}-${Math.min(i + BATCH_SIZE, keywords.length)})`,
          progress: 30 + (i / keywords.length) * 60, // 30-90% for article generation
          details: {
            batchNumber,
            totalBatches,
            articlesInBatch: batch.length,
            totalProcessed: totalProcessedArticles
          }
        });
      }
      
      const batchResults = await Promise.allSettled(
        batch.map(async (keyword, index) => {
          const globalIndex = i + index;
          console.log(`📝 Processing article ${globalIndex + 1}/${keywords.length}: "${keyword}"`);
          
          if (useSSE) {
            sendProgress(res, {
              type: 'article_start',
              message: `Generating article ${globalIndex + 1}/${keywords.length}: "${keyword}"`,
              progress: 30 + (globalIndex / keywords.length) * 60,
              details: {
                articleIndex: globalIndex + 1,
                keyword,
                stage: 'resource_integration'
              }
            });
          }
          
          try {
            const taskId = uuidv4();
            
            // Get keyword research data
            const keywordData = keywordResearchResults[globalIndex];
            
            // Step 4: Intelligent resource integration
            if (useSSE) {
              sendProgress(res, {
                type: 'article_progress',
                message: `Article ${globalIndex + 1}: Integrating resources...`,
                progress: 30 + (globalIndex / keywords.length) * 60,
                details: {
                  articleIndex: globalIndex + 1,
                  keyword,
                  stage: 'resource_integration'
                }
              });
            }
            
            const resources = await aiServiceManager.intelligentResourceIntegration(
              keyword, 
              keywordData,
              settings.sources,
              settings.redditPosts
            );
            
            // Step 5: Generate topic and outline
            if (useSSE) {
              sendProgress(res, {
                type: 'article_progress',
                message: `Article ${globalIndex + 1}: Generating topic outline...`,
                progress: 30 + ((globalIndex + 0.4) / keywords.length) * 60,
                details: {
                  articleIndex: globalIndex + 1,
                  keyword,
                  stage: 'topic_outline',
                  resourcesFound: resources.combinedResources?.length || 0
                }
              });
            }
            
            const topicOutline = await aiServiceManager.generateTopicAndOutline(
              keywordData, 
              resources, 
              settings
            );
            
            // Step 6: Prepare enhanced article data with full context from previous steps
            const articleData = {
              title: topicOutline.title,
              outline: topicOutline.outline,
              primaryKeywords: [keywordData.originalKeyword],
              secondaryKeywords: keywordData.relatedKeywords || [],
              peopleAlsoAsk: keywordData.peopleAlsoAsk || [],
              
              // Enhanced resource data from Step 4
              comprehensiveResources: resources.combinedResources || [],
              redditContent: settings.redditPosts.map(url => ({ url })) || [], // Pass Reddit original URLs directly
              resourceAnalysis: resources.aiAnalysis || {}, // AI分析结果
              contentGaps: resources.contentGaps || [], // 内容缺口分析
              
              // Enhanced strategy data from Step 5
              contentStrategy: topicOutline.contentStrategy || {}, // 内容策略
              resourceIntegrationPlan: topicOutline.resourceIntegrationPlan || '', // 资源整合计划
              seoOptimizations: topicOutline.seoOptimizations || '', // SEO优化建议
              
              // Settings and context
              tonality: settings.tonality,
              length: settings.length,
              format: settings.format,
              authorName: settings.authorName,
              authorBio: settings.authorBio,
              targetAudience: settings.targetAudience,
              articleGoal: `Create a comprehensive article about: ${topicOutline.title}`,
              productInfo: settings.productInfo,
              
              // Additional context for enhanced generation
              keywordContext: {
                originalKeyword: keywordData.originalKeyword,
                autocomplete: keywordData.autocomplete || [],
                relatedKeywords: keywordData.relatedKeywords || [],
                peopleAlsoAsk: keywordData.peopleAlsoAsk || [],
                keyTerms: keywordData.keyTerms || []
              }
            };

            // Step 7: Generate enhanced article
            if (useSSE) {
              sendProgress(res, {
                type: 'article_progress',
                message: `Article ${globalIndex + 1}: Generating final article...`,
                progress: 30 + ((globalIndex + 0.8) / keywords.length) * 60,
                details: {
                  articleIndex: globalIndex + 1,
                  keyword,
                  stage: 'article_generation',
                  title: topicOutline.title
                }
              });
            }
            
            const generatedArticle = await aiServiceManager.generateAdvancedArticle(articleData);

            // Create optimized task record for batch generation
            const taskData = {
              keywords: [keywordData.originalKeyword],
              selectedTopics: topicOutline.topics || [],
              topicSuggestions: { suggestions: topicOutline.topics || [] },
              keywordResearchData: keywordData,
              keywordResearchSelections: [],
              redditSources: resources.redditInsights || [],
              sources: settings.sources,
              comprehensiveResources: resources.combinedResources || [],
              productInfo: settings.productInfo,
              eeatProfile: {
                authorName: settings.authorName,
                authorBio: settings.authorBio,
                targetAudience: settings.targetAudience,
                articleGoal: `Create a comprehensive article about: ${topicOutline.title}`
              },
              outputParameters: {
                tonality: settings.tonality,
                length: settings.length,
                format: settings.format
              },
              batchMetadata: {
                inputMode: inputMode,
                originalInput: inputMode === 'IDEAS' ? originalInput : keyword,
                batchIndex: globalIndex,
                totalInBatch: keywords.length,
                outline: topicOutline.outline,
                resourcesUsed: resources.resourcesSummary || {},
                generationTimestamp: new Date().toISOString(),
                isBatchGenerated: true
              }
            };

            // Save to database with batch-optimized structure
            await database.run(
              `INSERT INTO tasks (
                id, user_id, name, status, current_step,
                keywords, selected_topics, topic_suggestions,
                keyword_research_data, keyword_research_selections,
                reddit_sources, sources, topic_sources, comprehensive_resources,
                product_info, eeat_profile, output_parameters, generated_article
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
              [
                taskId,
                req.user.id,
                `[Batch ${Math.floor(globalIndex/3) + 1}] ${topicOutline.title}`, // Add batch indicator
                'Completed',
                6, // Final step
                JSON.stringify(taskData.keywords),
                JSON.stringify(taskData.selectedTopics),
                JSON.stringify(taskData.topicSuggestions),
                JSON.stringify(taskData.keywordResearchData),
                JSON.stringify(taskData.keywordResearchSelections),
                JSON.stringify(taskData.redditSources),
                JSON.stringify(taskData.sources),
                JSON.stringify([]), // topic_sources deprecated
                JSON.stringify(taskData.comprehensiveResources),
                JSON.stringify(taskData.productInfo),
                JSON.stringify(taskData.eeatProfile),
                JSON.stringify(taskData.outputParameters),
                generatedArticle
              ]
            );

            console.log(`✅ Article ${globalIndex + 1} completed: "${topicOutline.title}"`);
            
            if (useSSE) {
              sendProgress(res, {
                type: 'article_completed',
                message: `✅ Article ${globalIndex + 1} completed: "${topicOutline.title}"`,
                progress: 30 + ((globalIndex + 1) / keywords.length) * 60,
                details: {
                  articleIndex: globalIndex + 1,
                  keyword,
                  title: topicOutline.title,
                  wordCount: generatedArticle.split(' ').length,
                  resourcesUsed: resources.combinedResources?.length || 0
                }
              });
            }
            
            return {
              index: globalIndex,
              keyword: keyword,
              title: topicOutline.title,
              taskId: taskId,
              success: true,
              article: generatedArticle,
              wordCount: generatedArticle.length,
              outline: topicOutline.outline,
              resourcesUsed: resources.resourcesSummary || {}
            };
          } catch (error) {
            console.error(`❌ Error processing article ${globalIndex + 1}: "${keyword}"`, error);
            
            if (useSSE) {
              sendProgress(res, {
                type: 'article_error',
                message: `❌ Article ${globalIndex + 1} failed: "${keyword}"`,
                progress: 30 + ((globalIndex + 1) / keywords.length) * 60,
                details: {
                  articleIndex: globalIndex + 1,
                  keyword,
                  error: error.message
                }
              });
            }
            
            return {
              index: globalIndex,
              keyword: keyword,
              title: `Error processing: ${keyword}`,
              success: false,
              error: error.message
            };
          }
        })
      );

      // Process batch results with improved error handling
      batchResults.forEach((result, batchIndex) => {
        const globalIndex = i + batchIndex;
        const keyword = batch[batchIndex];

        if (result.status === 'fulfilled') {
          if (result.value && result.value.success) {
            results.push(result.value);
            console.log(`✅ Article ${globalIndex + 1} added to results: "${result.value.title}"`);
          } else {
            // Handle fulfilled but failed results
            const errorInfo = {
              index: globalIndex,
              keyword: keyword,
              title: `Failed: ${keyword}`,
              success: false,
              error: result.value?.error || 'Article generation failed without specific error'
            };
            errors.push(errorInfo);
            console.log(`❌ Article ${globalIndex + 1} failed: "${keyword}" - ${errorInfo.error}`);
          }
        } else {
          // Handle rejected promises
          const errorInfo = {
            index: globalIndex,
            keyword: keyword,
            title: `Error: ${keyword}`,
            success: false,
            error: result.reason?.message || result.reason || 'Promise rejected with unknown error'
          };
          errors.push(errorInfo);
          console.log(`💥 Article ${globalIndex + 1} promise rejected: "${keyword}" - ${errorInfo.error}`);
        }
      });
      
      totalProcessedArticles += batch.length;
      
      if (useSSE) {
        sendProgress(res, {
          type: 'batch_completed',
          message: `Batch ${batchNumber}/${totalBatches} completed`,
          progress: 30 + (totalProcessedArticles / keywords.length) * 60,
          details: {
            batchNumber,
            totalBatches,
            articlesCompleted: results.length,
            articlesErrored: errors.length,
            totalProcessed: totalProcessedArticles
          }
        });
      }

      // Smart batch delay for API rate limiting
      if (i + BATCH_SIZE < keywords.length) {
        // Calculate smart delay based on batch size and remaining articles
        const remainingBatches = Math.ceil((keywords.length - totalProcessedArticles) / BATCH_SIZE);
        const smartDelay = remainingBatches > 3 ? 2000 : 1000; // 2s for many batches, 1s for few

        console.log(`⏱️ Smart delay: ${smartDelay}ms before next batch (${remainingBatches} batches remaining)...`);
        if (useSSE) {
          sendProgress(res, {
            type: 'delay',
            message: `Waiting ${smartDelay/1000}s before next batch (${remainingBatches} remaining)...`,
            progress: 30 + (totalProcessedArticles / keywords.length) * 60
          });
        }
        await new Promise(resolve => setTimeout(resolve, smartDelay));
      }
    }

    // Calculate overall statistics
    const avgWordCount = results.length > 0 ? 
      Math.round(results.reduce((sum, r) => sum + r.wordCount, 0) / results.length) : 0;
    
    const avgQualityScore = results.length > 0 ? 
      Math.round(results.reduce((sum, r) => sum + (r.qualityMetrics?.overallScore || 0), 0) / results.length * 100) / 100 : 0;

    console.log(`🎉 Advanced batch generation completed: ${results.length} successes, ${errors.length} errors`);

    // Create a master batch record for tracking
    const batchId = uuidv4();
    const batchTaskData = {
      keywords: keywords,
      selectedTopics: [],
      topicSuggestions: { suggestions: [] },
      keywordResearchData: null,
      keywordResearchSelections: [],
      redditSources: [],
      sources: settings.sources,
      comprehensiveResources: [],
      productInfo: settings.productInfo,
      eeatProfile: {
        authorName: settings.authorName,
        authorBio: settings.authorBio,
        targetAudience: settings.targetAudience,
        articleGoal: `Batch generation of ${keywords.length} articles`
      },
      outputParameters: {
        tonality: settings.tonality,
        length: settings.length,
        format: settings.format
      },
      batchSummary: {
        inputMode: inputMode,
        originalInput: originalInput,
        totalRequested: keywords.length,
        totalSuccess: results.length,
        totalErrors: errors.length,
        averageWordCount: avgWordCount,
        averageQualityScore: avgQualityScore,
        processingTime: new Date().toISOString(),
        individualTaskIds: results.map(r => r.taskId),
        isMasterBatchRecord: true
      }
    };

    // Save master batch record
    try {
      await database.run(
        `INSERT INTO tasks (
          id, user_id, name, status, current_step,
          keywords, selected_topics, topic_suggestions,
          keyword_research_data, keyword_research_selections,
          reddit_sources, sources, topic_sources, comprehensive_resources,
          product_info, eeat_profile, output_parameters, generated_article
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          batchId,
          req.user.id,
          `Batch Generation: ${inputMode} (${results.length}/${keywords.length} articles)`,
          'Completed',
          6,
          JSON.stringify(batchTaskData.keywords),
          JSON.stringify(batchTaskData.selectedTopics),
          JSON.stringify(batchTaskData.topicSuggestions),
          JSON.stringify(batchTaskData.keywordResearchData),
          JSON.stringify(batchTaskData.keywordResearchSelections),
          JSON.stringify(batchTaskData.redditSources),
          JSON.stringify(batchTaskData.sources),
          JSON.stringify([]),
          JSON.stringify(batchTaskData.comprehensiveResources),
          JSON.stringify(batchTaskData.productInfo),
          JSON.stringify(batchTaskData.eeatProfile),
          JSON.stringify(batchTaskData.outputParameters),
          JSON.stringify(batchTaskData.batchSummary) // Store summary as article content
        ]
      );
      console.log(`📋 Master batch record created: ${batchId}`);
    } catch (error) {
      console.error('Failed to create master batch record:', error);
    }

    if (useSSE) {
      sendProgress(res, {
        type: 'final_statistics',
        message: 'Calculating final statistics...',
        progress: 95,
        details: {
          successCount: results.length,
          errorCount: errors.length,
          avgWordCount,
          avgQualityScore
        }
      });
      
      // Send completion event
      sendProgress(res, {
        type: 'completed',
        message: `🎉 Batch generation completed! Success: ${results.length}, Failed: ${errors.length}`,
        progress: 100,
        results: {
          message: `Advanced batch generation completed: ${results.length} articles generated successfully`,
          inputMode: inputMode,
          originalInput: originalInput,
          generatedKeywords: keywords,
          results: results,
          errors: errors,
          statistics: {
            totalRequested: keywords.length,
            totalSuccess: results.length,
            totalErrors: errors.length,
            averageWordCount: avgWordCount,
            averageQualityScore: avgQualityScore,
            processingTime: new Date().toISOString()
          },
          settings: settings
        }
      });
      
      res.end();
      return;
    }

    res.json({
      message: `Advanced batch generation completed: ${results.length} articles generated successfully`,
      inputMode: inputMode,
      originalInput: originalInput,
      generatedKeywords: keywords,
      results: results,
      errors: errors,
      statistics: {
        totalRequested: keywords.length,
        totalSuccess: results.length,
        totalErrors: errors.length,
        averageWordCount: avgWordCount,
        averageQualityScore: avgQualityScore,
        processingTime: new Date().toISOString()
      },
      settings: settings
    });

  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    console.error('💥 Advanced batch generate error:', error);
    console.error('⏱️ Request duration:', duration, 'ms');
    console.error('📊 Error details:');
    console.error('   - Error name:', error.name);
    console.error('   - Error message:', error.message);
    console.error('   - Error stack:', error.stack);
    console.error('   - Request body size:', JSON.stringify(req.body).length, 'bytes');
    console.error('   - User ID:', req.user?.id);
    console.error('   - Request IP:', req.ip || req.connection?.remoteAddress);

    // Check if SSE was requested from the original request body
    const { useSSE = false } = req.body;

    if (useSSE) {
      sendProgress(res, {
        type: 'error',
        message: `❌ Error occurred during batch generation: ${error.message}`,
        progress: -1,
        error: error.message,
        duration: duration
      });
      res.end();
      return;
    }

    res.status(500).json({
      error: 'Failed to generate articles',
      message: error.message || 'An error occurred during advanced batch generation',
      duration: duration,
      timestamp: new Date().toISOString()
    });
  }
});


// POST /api/tasks/:id/generate-article - Generate article for a task
router.post('/:id/generate-article', checkResourceOwnership('task'), async (req, res) => {
  try {
    // Get the task first to extract data
    const task = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (!task) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found'
      });
    }

    // Parse task data
    const selectedTopics = task.selected_topics ? JSON.parse(task.selected_topics) : [];
    // Note: topic_sources is deprecated - we only use comprehensive_resources now
    const sources = task.sources ? JSON.parse(task.sources) : [];
    const productInfo = task.product_info ? JSON.parse(task.product_info) : {};
    const eeatProfile = task.eeat_profile ? JSON.parse(task.eeat_profile) : {};
    const outputParameters = task.output_parameters ? JSON.parse(task.output_parameters) : {};
    const keywordResearchData = task.keyword_research_data ? JSON.parse(task.keyword_research_data) : {};
    const keywordResearchSelections = task.keyword_research_selections ? JSON.parse(task.keyword_research_selections) : [];
    const redditSources = task.reddit_sources ? JSON.parse(task.reddit_sources) : [];
    const comprehensiveResources = task.comprehensive_resources ? JSON.parse(task.comprehensive_resources) : [];

    // Validate that we have topics to work with
    if (!selectedTopics || selectedTopics.length === 0) {
      return res.status(400).json({
        error: 'No topics selected',
        message: 'Please select at least one topic before generating an article'
      });
    }

    // Extract topics
    const allTopics = selectedTopics.map(t => t.edited || t.original || t);

    // Process sources (for backward compatibility with old 'sources' field)
    const legacySources = sources || [];
    
    console.log('');
    console.log('📊 DATA SOURCES SUMMARY:');
    console.log('   - Legacy sources:', legacySources.length);
    console.log('   - Comprehensive resources:', comprehensiveResources.length);
    console.log('   - Reddit sources:', redditSources.length);

    // Extract keywords - Debug the raw data first
    console.log('');
    console.log('🔍 KEYWORD EXTRACTION DEBUG');
    console.log('='.repeat(50));
    console.log('📋 Raw keyword research data:');
    console.log('   - Main keyword:', keywordResearchData ? keywordResearchData.keyword : 'N/A');
    console.log('   - Selections type:', typeof keywordResearchSelections);
    console.log('   - Selections count:', keywordResearchSelections ? keywordResearchSelections.length : 0);
    if (keywordResearchSelections && keywordResearchSelections.length > 0) {
      console.log('📝 Selection items preview:');
      keywordResearchSelections.slice(0, 5).forEach((sel, i) => {
        console.log(`   ${i + 1}. Type: ${sel.type}, Value: "${(sel.value || sel.text || '').substring(0, 50)}${(sel.value || sel.text || '').length > 50 ? '...' : ''}"}`);
      });
      if (keywordResearchSelections.length > 5) {
        console.log(`   ... and ${keywordResearchSelections.length - 5} more items`);
      }
    }
    console.log('='.repeat(50));

    const primaryKeywords = [];
    if (keywordResearchData && keywordResearchData.keyword) {
      primaryKeywords.push(keywordResearchData.keyword);
      console.log('✅ Added PRIMARY keyword from research data:', keywordResearchData.keyword);
    }

    if (keywordResearchSelections && Array.isArray(keywordResearchSelections)) {
      keywordResearchSelections.forEach((selection, index) => {
        // Removed verbose per-selection logging
        if (selection.type === 'autocomplete' || selection.type === 'related' || selection.type === 'custom') {
          const keyword = selection.value || selection.text;
          primaryKeywords.push(keyword);
          console.log(`✅ Added PRIMARY keyword: "${keyword}" (${selection.type})`);
        }
      });
    } else {
      console.log('⚠️  keywordResearchSelections is not a valid array');
    }

    const secondaryKeywords = [];
    if (keywordResearchSelections && Array.isArray(keywordResearchSelections)) {
      keywordResearchSelections.forEach((selection, index) => {
        // Removed verbose per-selection logging
        if (selection.type === 'keyTerm' || selection.type === 'keyTerms' || selection.type === 'paa' || selection.type === 'peopleAlsoAsk') {
          const keyword = selection.value || selection.text;
          secondaryKeywords.push(keyword);
          console.log(`✅ Added SECONDARY keyword: "${keyword}" (${selection.type})`);
        }
      });
    }

    // Prepare article data
    const articleData = {
      topics: allTopics,
      sources: legacySources, // Legacy sources for backward compatibility
      productInfo: productInfo,
      tonality: outputParameters.tonality || 'informative',
      length: outputParameters.length || 'medium_article',
      format: outputParameters.format || 'markdown',
      authorName: eeatProfile.authorName || '',
      authorBio: eeatProfile.authorBio || '',
      targetAudience: eeatProfile.targetAudience || '',
      articleGoal: eeatProfile.articleGoal || '',
      primaryKeywords: [...new Set(primaryKeywords)],
      secondaryKeywords: [...new Set(secondaryKeywords)],
      rawRedditData: redditSources || [], // Add missing rawRedditData field
      comprehensiveResources: comprehensiveResources
    };

    console.log('');
    console.log('🚀 ARTICLE GENERATION SUMMARY');
    console.log('='.repeat(50));
    console.log('📝 Task ID:', req.params.id);
    console.log('🎯 Topics to synthesize:', allTopics.length);
    allTopics.forEach((topic, i) => console.log(`   ${i + 1}. ${topic.substring(0, 80)}${topic.length > 80 ? '...' : ''}`));
    console.log('🔑 PRIMARY keywords extracted:', primaryKeywords.length);
    if (primaryKeywords.length > 0) {
      console.log('   ', primaryKeywords.join(', '));
    }
    console.log('🔑 SECONDARY keywords extracted:', secondaryKeywords.length);
    if (secondaryKeywords.length > 0) {
      console.log('   ', secondaryKeywords.slice(0, 10).join(', ') + (secondaryKeywords.length > 10 ? '...' : ''));
    }
    console.log('📄 Article parameters:');
    console.log('   - Tonality:', articleData.tonality);
    console.log('   - Length:', articleData.length);
    console.log('   - Format:', articleData.format);
    console.log('   - Author:', articleData.authorName || 'N/A');
    console.log('   - Target audience:', articleData.targetAudience || 'N/A');
    console.log('   - Product included:', articleData.productInfo && articleData.productInfo.name ? 'YES' : 'NO');
    console.log('   - Reddit data included:', articleData.rawRedditData ? articleData.rawRedditData.length : 0, 'items');
    console.log('   - Comprehensive resources:', articleData.comprehensiveResources ? articleData.comprehensiveResources.length : 0, 'items');
    console.log('='.repeat(50));
    console.log('🎬 Sending to AI service...');
    console.log('');

    // Generate article with task ID for debug logging
    articleData.taskId = req.params.id; // Add task ID for debug logging
    const generatedArticle = await aiServiceManager.generateArticle(articleData);

    // Update task with generated article only (don't auto-complete yet)
    await database.run(
      'UPDATE tasks SET generated_article = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?',
      [generatedArticle, req.params.id, req.user.id]
    );

    res.json({
      article: generatedArticle,
      message: 'Article generated successfully'
    });

  } catch (error) {
    console.error('Generate article error:', error);
    res.status(500).json({
      error: 'Failed to generate article',
      message: error.message || 'An error occurred while generating the article'
    });
  }
});

// POST /api/tasks/:id/finish - Mark task as completed and update title
router.post('/:id/finish', checkResourceOwnership('task'), async (req, res) => {
  try {
    // Get the task first to extract the article
    const task = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (!task) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found'
      });
    }

    if (!task.generated_article) {
      return res.status(400).json({
        error: 'No article to finish',
        message: 'Please generate an article before finishing the task'
      });
    }

    // Prepare update query with title extraction
    const updates = ['status = ?', 'current_step = ?', 'updated_at = CURRENT_TIMESTAMP'];
    const params = ['Completed', 6]; // Set to final step (0-indexed)

    // Extract SEO title from the generated article
    if (task.generated_article && typeof task.generated_article === 'string') {
      try {
        // Look for the SEO Title in the structured output format with more precise regex
        // This regex ensures we only capture the title line and stop at the next line or meta description
        const seoTitleMatch = task.generated_article.match(/\*\*SEO Title:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                             task.generated_article.match(/SEO Title:\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                             task.generated_article.match(/\*\*SEO Title:\*\*\s*([^\n]+?)(?:\s*\n|\s*$)/i) ||
                             task.generated_article.match(/SEO Title:\s*([^\n]+?)(?:\s*\n|\s*$)/i);

        if (seoTitleMatch && seoTitleMatch[1]) {
          let articleTitle = seoTitleMatch[1].trim();
          // Remove any markdown formatting and brackets
          articleTitle = articleTitle.replace(/[#*_`\[\]]/g, '').trim();
          // Remove any trailing periods or punctuation that might be part of the format
          articleTitle = articleTitle.replace(/[.]*$/, '').trim();
          // Stop at any meta description content that might have been captured
          articleTitle = articleTitle.split(/meta\s*description/i)[0].trim();
          // Limit title length
          articleTitle = articleTitle.slice(0, 200);

          if (articleTitle) {
            updates.push('name = ?');
            params.push(articleTitle);
            console.log('Finishing task and updating name to SEO title:', articleTitle);
          }
        } else {
          // Fallback: try to extract from markdown header or first line
          const fallbackMatch = task.generated_article.match(/^#\s*(.+)$/m) ||
                               task.generated_article.match(/^(.+)$/m);

          if (fallbackMatch && fallbackMatch[1]) {
            let articleTitle = fallbackMatch[1].trim();
            articleTitle = articleTitle.replace(/[#*_`]/g, '').trim();
            articleTitle = articleTitle.slice(0, 200);

            if (articleTitle) {
              updates.push('name = ?');
              params.push(articleTitle);
              console.log('Finishing task and updating name to fallback title:', articleTitle);
            }
          }
        }
      } catch (error) {
        console.warn('Failed to extract SEO title:', error);
        // Continue without updating the name
      }
    }

    // Add task ID and user ID to params
    params.push(req.params.id, req.user.id);

    // Update task to completed status with new title
    await database.run(
      `UPDATE tasks SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`,
      params
    );

    // Return updated task
    const updatedTask = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    const formattedTask = {
      ...updatedTask,
      keywords: updatedTask.keywords ? JSON.parse(updatedTask.keywords) : [],
      selectedTopics: updatedTask.selected_topics ? JSON.parse(updatedTask.selected_topics) : [],
      topicSuggestions: updatedTask.topic_suggestions ? JSON.parse(updatedTask.topic_suggestions) : [],
      keywordResearchData: updatedTask.keyword_research_data ? JSON.parse(updatedTask.keyword_research_data) : null,
      keywordResearchSelections: updatedTask.keyword_research_selections ? JSON.parse(updatedTask.keyword_research_selections) : [],
      redditSources: updatedTask.reddit_sources ? JSON.parse(updatedTask.reddit_sources) : [],
      sources: updatedTask.sources ? JSON.parse(updatedTask.sources) : [],
      // topicSources deprecated
      comprehensiveResources: updatedTask.comprehensive_resources ? JSON.parse(updatedTask.comprehensive_resources) : [],
      productInfo: updatedTask.product_info ? JSON.parse(updatedTask.product_info) : {},
      eeatProfile: updatedTask.eeat_profile ? JSON.parse(updatedTask.eeat_profile) : {},
      outputParameters: updatedTask.output_parameters ? JSON.parse(updatedTask.output_parameters) : {}
    };

    res.json({
      message: 'Task completed successfully',
      task: formattedTask
    });

  } catch (error) {
    console.error('Finish task error:', error);
    res.status(500).json({
      error: 'Failed to finish task',
      message: error.message || 'An error occurred while finishing the task'
    });
  }
});

// DELETE /api/tasks/:id - Delete a task
router.delete('/:id', checkResourceOwnership('task'), async (req, res) => {
  try {
    const result = await database.run(
      'DELETE FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (result.changes === 0) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found'
      });
    }

    res.json({
      message: 'Task deleted successfully'
    });

  } catch (error) {
    console.error('Delete task error:', error);
    res.status(500).json({
      error: 'Failed to delete task',
      message: 'An error occurred while deleting the task'
    });
  }
});

module.exports = router;
