const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid');
const { authenticateToken } = require('../middleware/auth');
const database = require('../config/database');
const aiServiceManager = require('../services/aiServiceManager');

// All routes require authentication
router.use(authenticateToken);

// Middleware to check if user owns the batch task
const checkBatchTaskOwnership = async (req, res, next) => {
  try {
    const batchTask = await database.get(
      'SELECT user_id FROM batch_tasks WHERE id = ?',
      [req.params.id]
    );

    if (!batchTask) {
      return res.status(404).json({
        error: 'Batch task not found',
        message: 'The requested batch task could not be found'
      });
    }

    if (batchTask.user_id !== req.user.id) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You do not have permission to access this batch task'
      });
    }

    next();
  } catch (error) {
    console.error('Error checking batch task ownership:', error);
    res.status(500).json({
      error: 'Server error',
      message: 'An error occurred while checking batch task ownership'
    });
  }
};

// GET /api/batch-tasks - Get all batch tasks for the user
router.get('/', async (req, res) => {
  try {
    const { limit = 10, offset = 0, status } = req.query;

    let sql = 'SELECT * FROM batch_tasks WHERE user_id = ?';
    const params = [req.user.id];

    if (status) {
      sql += ' AND status = ?';
      params.push(status);
    }

    sql += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const batchTasks = await database.all(sql, params);

    // Get article counts for each batch task
    const batchTasksWithCounts = await Promise.all(
      batchTasks.map(async (batchTask) => {
        const articleCount = await database.get(
          'SELECT COUNT(*) as count FROM tasks WHERE batch_task_id = ?',
          [batchTask.id]
        );

        return {
          ...batchTask,
          global_settings: batchTask.global_settings ? JSON.parse(batchTask.global_settings) : {},
          article_count: articleCount.count || 0
        };
      })
    );

    res.json({
      batchTasks: batchTasksWithCounts,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: batchTasksWithCounts.length
      }
    });
  } catch (error) {
    console.error('Error fetching batch tasks:', error);
    res.status(500).json({
      error: 'Failed to fetch batch tasks',
      message: error.message
    });
  }
});

// GET /api/batch-tasks/:id - Get specific batch task with articles
router.get('/:id', checkBatchTaskOwnership, async (req, res) => {
  try {
    const batchTask = await database.get(
      'SELECT * FROM batch_tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (!batchTask) {
      return res.status(404).json({
        error: 'Batch task not found',
        message: 'The requested batch task could not be found'
      });
    }

    // Get all articles generated for this batch task
    const articles = await database.all(
      'SELECT * FROM tasks WHERE batch_task_id = ? ORDER BY created_at ASC',
      [req.params.id]
    );

    // Format articles
    const formattedArticles = articles.map(article => ({
      ...article,
      keywords: article.keywords ? JSON.parse(article.keywords) : [],
      selectedTopics: article.selected_topics ? JSON.parse(article.selected_topics) : [],
      keywordResearchData: article.keyword_research_data ? JSON.parse(article.keyword_research_data) : null,
      comprehensiveResources: article.comprehensive_resources ? JSON.parse(article.comprehensive_resources) : [],
      productInfo: article.product_info ? JSON.parse(article.product_info) : {},
      eeatProfile: article.eeat_profile ? JSON.parse(article.eeat_profile) : {},
      outputParameters: article.output_parameters ? JSON.parse(article.output_parameters) : {}
    }));

    res.json({
      batchTask: {
        ...batchTask,
        global_settings: batchTask.global_settings ? JSON.parse(batchTask.global_settings) : {}
      },
      articles: formattedArticles
    });
  } catch (error) {
    console.error('Error fetching batch task:', error);
    res.status(500).json({
      error: 'Failed to fetch batch task',
      message: error.message
    });
  }
});

// POST /api/batch-tasks/auto-create - Auto-create a new batch task with timestamp
router.post('/auto-create', async (req, res) => {
  try {
    const { name, inputMode = 'KEYWORDS', content = '', globalSettings = {} } = req.body;

    const batchTaskId = uuidv4();
    const timestamp = new Date().toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
    const batchTaskName = name || `Batch Generation - ${timestamp}`;

    // Create batch task in draft state
    await database.run(
      `INSERT INTO batch_tasks (
        id, user_id, name, status, input_mode, input_content,
        global_settings, total_articles, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
      [
        batchTaskId,
        req.user.id,
        batchTaskName,
        'draft', // Start in draft state, not pending
        inputMode,
        content,
        JSON.stringify(globalSettings),
        0 // Will be calculated when user starts generation
      ]
    );

    // Get the created batch task
    const createdBatchTask = await database.get(
      'SELECT * FROM batch_tasks WHERE id = ?',
      [batchTaskId]
    );

    const formattedBatchTask = {
      ...createdBatchTask,
      global_settings: createdBatchTask.global_settings ? JSON.parse(createdBatchTask.global_settings) : {}
    };

    res.status(201).json({
      message: 'Batch task created successfully',
      batchTask: formattedBatchTask
    });
  } catch (error) {
    console.error('Error auto-creating batch task:', error);
    res.status(500).json({
      error: 'Failed to create batch task',
      message: error.message
    });
  }
});

// POST /api/batch-tasks - Create new batch task (async)
router.post('/', async (req, res) => {
  try {
    const { inputMode, content, globalSettings = {} } = req.body;

    // Validate input
    const validModes = ['IDEAS', 'KEYWORDS', 'TITLES'];
    if (!inputMode || !validModes.includes(inputMode)) {
      return res.status(400).json({
        error: 'Invalid input mode',
        message: 'Input mode must be one of: IDEAS, KEYWORDS, TITLES'
      });
    }

    if (!content || (typeof content !== 'string' && !Array.isArray(content))) {
      return res.status(400).json({
        error: 'Content is required',
        message: 'Please provide content based on the selected input mode'
      });
    }

    // Calculate estimated article count
    let estimatedCount = 0;
    switch(inputMode) {
      case 'KEYWORDS':
        const keywords = Array.isArray(content) ? content : content.split('\n').map(k => k.trim()).filter(k => k.length > 0);
        estimatedCount = Math.min(keywords.length, globalSettings.targetCount || 10, 20);
        break;
      case 'TITLES':
        const titles = Array.isArray(content) ? content : content.split('\n').map(t => t.trim()).filter(t => t.length > 0);
        estimatedCount = titles.length;
        break;
      case 'IDEAS':
        estimatedCount = Math.min(globalSettings.targetCount || 5, 5);
        break;
    }

    // Create batch task
    const batchTaskId = uuidv4();
    const batchTaskName = `Batch Generation - ${inputMode} (${estimatedCount} articles)`;

    await database.run(
      `INSERT INTO batch_tasks (
        id, user_id, name, status, input_mode, input_content,
        global_settings, total_articles, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
      [
        batchTaskId,
        req.user.id,
        batchTaskName,
        'pending',
        inputMode,
        typeof content === 'string' ? content : JSON.stringify(content),
        JSON.stringify(globalSettings),
        estimatedCount
      ]
    );

    // Start async processing (don't wait for it)
    processBatchTaskAsync(batchTaskId, req.user.id);

    res.status(201).json({
      batchTaskId,
      message: 'Batch task created successfully',
      estimatedArticles: estimatedCount,
      status: 'pending'
    });
  } catch (error) {
    console.error('Error creating batch task:', error);
    res.status(500).json({
      error: 'Failed to create batch task',
      message: error.message
    });
  }
});

// PUT /api/batch-tasks/:id - Update batch task
router.put('/:id', checkBatchTaskOwnership, async (req, res) => {
  try {
    const { name, inputMode, content, globalSettings } = req.body;

    const batchTask = await database.get(
      'SELECT * FROM batch_tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (!batchTask) {
      return res.status(404).json({
        error: 'Batch task not found',
        message: 'The requested batch task could not be found'
      });
    }

    // Only allow updates if task is in draft status
    if (batchTask.status !== 'draft') {
      return res.status(400).json({
        error: 'Cannot update batch task',
        message: 'Batch task can only be updated when in draft status'
      });
    }

    // Build update query dynamically
    const updates = [];
    const params = [];

    if (name !== undefined) {
      updates.push('name = ?');
      params.push(name);
    }

    if (inputMode !== undefined) {
      updates.push('input_mode = ?');
      params.push(inputMode);
    }

    if (content !== undefined) {
      updates.push('input_content = ?');
      params.push(content);
    }

    if (globalSettings !== undefined) {
      updates.push('global_settings = ?');
      params.push(JSON.stringify(globalSettings));
    }

    if (updates.length > 0) {
      updates.push('updated_at = CURRENT_TIMESTAMP');
      params.push(req.params.id);

      await database.run(
        `UPDATE batch_tasks SET ${updates.join(', ')} WHERE id = ?`,
        params
      );
    }

    // Get updated batch task
    const updatedBatchTask = await database.get(
      'SELECT * FROM batch_tasks WHERE id = ?',
      [req.params.id]
    );

    res.json({
      message: 'Batch task updated successfully',
      batchTask: {
        ...updatedBatchTask,
        global_settings: updatedBatchTask.global_settings ? JSON.parse(updatedBatchTask.global_settings) : {}
      }
    });
  } catch (error) {
    console.error('Error updating batch task:', error);
    res.status(500).json({
      error: 'Failed to update batch task',
      message: error.message
    });
  }
});

// DELETE /api/batch-tasks/:id - Cancel/delete batch task
router.delete('/:id', checkBatchTaskOwnership, async (req, res) => {
  try {
    const batchTask = await database.get(
      'SELECT status FROM batch_tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (!batchTask) {
      return res.status(404).json({
        error: 'Batch task not found',
        message: 'The requested batch task could not be found'
      });
    }

    // If task is processing, mark as cancelled instead of deleting
    if (batchTask.status === 'processing') {
      await database.run(
        'UPDATE batch_tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        ['cancelled', req.params.id]
      );
      
      res.json({
        message: 'Batch task cancelled successfully'
      });
    } else {
      // Delete the batch task and associated articles
      await database.run('DELETE FROM batch_tasks WHERE id = ?', [req.params.id]);
      
      res.json({
        message: 'Batch task deleted successfully'
      });
    }
  } catch (error) {
    console.error('Error deleting batch task:', error);
    res.status(500).json({
      error: 'Failed to delete batch task',
      message: error.message
    });
  }
});

// Async batch processing function
async function processBatchTaskAsync(batchTaskId, userId) {
  console.log(`🚀 Starting async batch processing for task: ${batchTaskId}`);
  
  try {
    // Update status to processing
    await database.run(
      'UPDATE batch_tasks SET status = ?, started_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      ['processing', batchTaskId]
    );

    // Get batch task details
    const batchTask = await database.get(
      'SELECT * FROM batch_tasks WHERE id = ?',
      [batchTaskId]
    );

    if (!batchTask) {
      throw new Error('Batch task not found');
    }

    const globalSettings = JSON.parse(batchTask.global_settings || '{}');
    const inputContent = batchTask.input_content;
    const inputMode = batchTask.input_mode;

    // Import the batch processing logic from the original route
    // This will be implemented in the next step
    await processBatchArticles(batchTask, globalSettings, userId);

  } catch (error) {
    console.error(`❌ Batch processing failed for task ${batchTaskId}:`, error);
    
    // Update batch task with error
    await database.run(
      'UPDATE batch_tasks SET status = ?, error_message = ?, completed_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      ['failed', error.message, batchTaskId]
    );
  }
}

// This function will contain the actual batch processing logic
async function processBatchArticles(batchTask, globalSettings, userId) {
  const batchTaskId = batchTask.id;
  const inputMode = batchTask.input_mode;
  const inputContent = batchTask.input_content;

  console.log(`📦 Processing batch task: ${batchTaskId} (${inputMode})`);

  try {
    // Parse input content based on mode
    let articlesToGenerate = [];

    switch(inputMode) {
      case 'KEYWORDS':
        const keywords = Array.isArray(inputContent) ? inputContent :
          (typeof inputContent === 'string' ? JSON.parse(inputContent) :
           inputContent.split('\n').map(k => k.trim()).filter(k => k.length > 0));

        articlesToGenerate = keywords.slice(0, Math.min(keywords.length, globalSettings.targetCount || 10, 20))
          .map(keyword => ({ type: 'keyword', content: keyword }));
        break;

      case 'TITLES':
        const titles = Array.isArray(inputContent) ? inputContent :
          (typeof inputContent === 'string' ? JSON.parse(inputContent) :
           inputContent.split('\n').map(t => t.trim()).filter(t => t.length > 0));

        articlesToGenerate = titles.map(title => ({ type: 'title', content: title }));
        break;

      case 'IDEAS':
        const targetCount = Math.min(globalSettings.targetCount || 5, 5);
        articlesToGenerate = Array.from({ length: targetCount }, (_, i) => ({
          type: 'idea',
          content: `Article ${i + 1} based on: ${inputContent}`
        }));
        break;
    }

    // Update total articles count
    await database.run(
      'UPDATE batch_tasks SET total_articles = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [articlesToGenerate.length, batchTaskId]
    );

    console.log(`📝 Will generate ${articlesToGenerate.length} articles`);

    // Process articles in batches to avoid overwhelming the system
    const batchSize = 3; // Process 3 articles at a time
    let completedCount = 0;
    let failedCount = 0;

    for (let i = 0; i < articlesToGenerate.length; i += batchSize) {
      const batch = articlesToGenerate.slice(i, i + batchSize);

      console.log(`📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(articlesToGenerate.length / batchSize)}`);

      // Process batch in parallel
      const batchResults = await Promise.allSettled(
        batch.map(async (articleData, batchIndex) => {
          const articleIndex = i + batchIndex + 1;
          console.log(`📝 Processing article ${articleIndex}/${articlesToGenerate.length}: "${articleData.content}"`);

          try {
            // Generate article using the same logic as the original batch generation
            const result = await generateSingleArticle(articleData, globalSettings, userId, batchTaskId);

            completedCount++;
            console.log(`✅ Article ${articleIndex} completed: "${result.name}"`);

            // Update progress
            const progress = Math.round((completedCount + failedCount) / articlesToGenerate.length * 100);
            await database.run(
              'UPDATE batch_tasks SET progress = ?, completed_articles = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
              [progress, completedCount, batchTaskId]
            );

            return result;
          } catch (error) {
            failedCount++;
            console.error(`❌ Error processing article ${articleIndex}: "${articleData.content}"`, error);

            // Update progress
            const progress = Math.round((completedCount + failedCount) / articlesToGenerate.length * 100);
            await database.run(
              'UPDATE batch_tasks SET progress = ?, failed_articles = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
              [progress, failedCount, batchTaskId]
            );

            throw error;
          }
        })
      );

      // Add delay between batches to avoid rate limiting
      if (i + batchSize < articlesToGenerate.length) {
        const delay = Math.min(2000 + (batchResults.length * 1000), 10000);
        console.log(`⏱️ Waiting ${delay}ms before next batch...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // Mark batch task as completed
    await database.run(
      'UPDATE batch_tasks SET status = ?, progress = 100, completed_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      ['completed', batchTaskId]
    );

    console.log(`🎉 Batch task completed: ${completedCount} successes, ${failedCount} errors`);

  } catch (error) {
    console.error(`💥 Batch processing failed for task ${batchTaskId}:`, error);
    throw error;
  }
}

// Generate a single article (extracted from the original batch generation logic)
async function generateSingleArticle(articleData, globalSettings, userId, batchTaskId) {
  const { v4: uuidv4 } = require('uuid');

  // Create task for this article
  const taskId = uuidv4();
  const taskName = `${articleData.content.substring(0, 50)}${articleData.content.length > 50 ? '...' : ''}`;

  // Create task record
  await database.run(
    `INSERT INTO tasks (
      id, user_id, name, status, current_step, batch_task_id,
      created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
    [taskId, userId, taskName, 'In Progress', 0, batchTaskId]
  );

  try {
    // Use the AI service to generate the article
    // This will use the same logic as the original advanced batch generation
    let keywords = [];

    if (articleData.type === 'keyword') {
      keywords = [articleData.content];
    } else if (articleData.type === 'title') {
      // Extract keywords from title
      keywords = articleData.content.split(' ').filter(word => word.length > 3).slice(0, 3);
    } else {
      // For ideas, generate keywords
      keywords = ['productivity', 'tips', 'guide']; // Default keywords
    }

    // Perform keyword research
    console.log(`🔍 Researching keyword: "${keywords[0]}"`);
    const keywordData = await aiServiceManager.enhancedKeywordResearch(keywords[0]);

    // Generate article using AI
    console.log(`🤖 Generating article for: "${articleData.content}"`);
    const articleResult = await aiServiceManager.generateArticle({
      keywords: keywords,
      keywordResearchData: keywordData,
      comprehensiveResources: [],
      productInfo: globalSettings.productInfo || {},
      eeatProfile: globalSettings.eeatProfile || {},
      outputParameters: globalSettings.outputParameters || {}
    });

    // Update task with generated article
    await database.run(
      `UPDATE tasks SET
        status = ?,
        current_step = 7,
        keywords = ?,
        keyword_research_data = ?,
        generated_article = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?`,
      [
        'Completed',
        JSON.stringify(keywords),
        JSON.stringify(keywordData),
        articleResult,
        taskId
      ]
    );

    return {
      id: taskId,
      name: taskName,
      status: 'Completed',
      article: articleResult
    };

  } catch (error) {
    // Update task with error status
    await database.run(
      'UPDATE tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      ['Failed', taskId]
    );

    throw error;
  }
}

module.exports = router;
