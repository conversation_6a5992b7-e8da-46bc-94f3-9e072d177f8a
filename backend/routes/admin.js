const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const aiServiceManager = require('../services/aiServiceManager');
const database = require('../config/database');
const AuditLogger = require('../services/auditLogger');

const router = express.Router();

// Middleware to check admin role
const requireAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Please log in to access this resource'
      });
    }

    // Check if user has admin role
    const user = await database.get(
      'SELECT role FROM users WHERE id = ?',
      [req.user.id]
    );

    if (!user || user.role !== 'admin') {
      return res.status(403).json({
        error: 'Admin access required',
        message: 'You do not have permission to access this resource'
      });
    }

    next();
  } catch (error) {
    console.error('Admin authorization error:', error);
    res.status(500).json({
      error: 'Authorization failed',
      message: 'An error occurred while checking permissions'
    });
  }
};

// All admin routes require authentication and admin role
router.use(authenticateToken);
router.use(requireAdmin);

// GET /api/admin/models - Get all AI models
router.get('/models', async (req, res) => {
  try {
    const models = await aiServiceManager.getAllModels();
    
    // Don't expose API keys in the response
    const safeModels = models.map(model => ({
      ...model,
      api_key: model.api_key ? '***HIDDEN***' : null,
      has_api_key: !!model.api_key
    }));

    res.json({
      models: safeModels
    });
  } catch (error) {
    console.error('Error fetching models:', error);
    res.status(500).json({
      error: 'Failed to fetch models',
      message: error.message
    });
  }
});

// PUT /api/admin/models/:modelName - Update AI model configuration
router.put('/models/:modelName', async (req, res) => {
  try {
    const { modelName } = req.params;
    const { api_key, model_version, is_active, is_default } = req.body;

    // Validate input
    if (typeof is_active !== 'boolean' || typeof is_default !== 'boolean') {
      return res.status(400).json({
        error: 'Invalid input',
        message: 'is_active and is_default must be boolean values'
      });
    }

    if (model_version && typeof model_version !== 'string') {
      return res.status(400).json({
        error: 'Invalid input',
        message: 'model_version must be a string'
      });
    }

    const updates = {
      model_version: model_version || '',
      is_active,
      is_default
    };

    // Only update API key if provided and not empty/hidden
    if (api_key && api_key !== '***HIDDEN***' && api_key.trim() !== '') {
      updates.api_key = api_key;
    }

    const updatedModel = await aiServiceManager.updateModel(modelName, updates);

    if (!updatedModel) {
      return res.status(404).json({
        error: 'Model not found',
        message: `AI model '${modelName}' not found`
      });
    }

    // Reinitialize the service if API key was updated
    if (updates.api_key && updates.api_key !== '***HIDDEN***') {
      try {
        await aiServiceManager.ensureServiceInitialized(modelName);
        console.log(`Service ${modelName} reinitialized with new API key`);
      } catch (initError) {
        console.warn(`Failed to reinitialize ${modelName} service:`, initError.message);
      }
    }

    // Don't expose API key in response
    const safeModel = {
      ...updatedModel,
      api_key: updatedModel.api_key ? '***HIDDEN***' : null,
      has_api_key: !!updatedModel.api_key
    };

    res.json({
      message: 'Model updated successfully',
      model: safeModel
    });
  } catch (error) {
    console.error('Error updating model:', error);
    res.status(500).json({
      error: 'Failed to update model',
      message: error.message
    });
  }
});

// Prompt management has been moved to code-based system
// Prompts are now managed in /config/prompts.js for better version control

// GET /api/admin/prompts - Deprecated: Prompts are now in code
router.get('/prompts', async (req, res) => {
  res.status(410).json({
    error: 'Feature deprecated',
    message: 'Prompt templates are now managed in code (config/prompts.js) for better version control. Please update prompts directly in the codebase.',
    migration: 'Edit /backend/config/prompts.js to modify prompts'
  });
});

// PUT /api/admin/prompts/:id - Deprecated: Prompts are now in code
router.put('/prompts/:id', async (req, res) => {
  res.status(410).json({
    error: 'Feature deprecated',
    message: 'Prompt templates are now managed in code (config/prompts.js) for better version control. Please update prompts directly in the codebase.',
    migration: 'Edit /backend/config/prompts.js to modify prompts'
  });
});

// GET /api/admin/active-model - Get currently active model
router.get('/active-model', async (req, res) => {
  try {
    const activeModelName = await aiServiceManager.getActiveModel();
    const modelConfig = await aiServiceManager.getModelConfig(activeModelName);

    if (!modelConfig) {
      return res.status(404).json({
        error: 'Active model not found',
        message: 'No active AI model configuration found'
      });
    }

    // Don't expose API key
    const safeModel = {
      ...modelConfig,
      api_key: modelConfig.api_key ? '***HIDDEN***' : null,
      has_api_key: !!modelConfig.api_key
    };

    res.json({
      activeModel: safeModel
    });
  } catch (error) {
    console.error('Error fetching active model:', error);
    res.status(500).json({
      error: 'Failed to fetch active model',
      message: error.message
    });
  }
});

// POST /api/admin/test-model/:modelName - Test AI model connection
router.post('/test-model/:modelName', async (req, res) => {
  try {
    const { modelName } = req.params;
    
    // Get model configuration
    const modelConfig = await aiServiceManager.getModelConfig(modelName);
    
    if (!modelConfig) {
      return res.status(404).json({
        error: 'Model not found',
        message: `AI model '${modelName}' not found`
      });
    }

    if (!modelConfig.api_key) {
      return res.status(400).json({
        error: 'API key missing',
        message: `API key not configured for model '${modelName}'`
      });
    }

    // Test the model with a simple topic generation
    const testKeywords = ['test', 'ai', 'connection'];
    
    try {
      // Initialize the specific service for testing
      await aiServiceManager.ensureServiceInitialized(modelName);

      // Get the service directly for testing
      const service = aiServiceManager.services[modelName];
      if (!service) {
        throw new Error(`Service not found for model: ${modelName}`);
      }

      // Test topic generation directly with the service
      const result = await service.generateTopicSuggestions(testKeywords);

      console.log(`✅ ${modelName} test successful:`, result);

      res.json({
        success: true,
        message: `Model '${modelName}' connection test successful`,
        testResult: {
          topicsGenerated: result.totalCount || 0,
          hasResponse: !!result.suggestions
        }
      });
    } catch (testError) {
      res.status(500).json({
        success: false,
        error: 'Model test failed',
        message: `Failed to connect to ${modelName}: ${testError.message}`
      });
    }
  } catch (error) {
    console.error('Error testing model:', error);
    res.status(500).json({
      error: 'Failed to test model',
      message: error.message
    });
  }
});

// GET /api/admin/stats - Get admin dashboard stats
router.get('/stats', async (req, res) => {
  try {
    const [totalUsers, totalTasks, activeModels] = await Promise.all([
      database.get('SELECT COUNT(*) as count FROM users'),
      database.get('SELECT COUNT(*) as count FROM tasks'),
      database.get('SELECT COUNT(*) as count FROM ai_models WHERE is_active = ?', [true])
    ]);

    const { getAvailablePrompts } = require('../config/prompts');
    const totalPrompts = getAvailablePrompts().length;

    res.json({
      stats: {
        totalUsers: totalUsers.count,
        totalTasks: totalTasks.count,
        activeModels: activeModels.count,
        totalPrompts // Now from code instead of database
      }
    });
  } catch (error) {
    console.error('Error fetching admin stats:', error);
    res.status(500).json({
      error: 'Failed to fetch stats',
      message: error.message
    });
  }
});

// User Management Routes

// GET /api/admin/users - Get all users with pagination and search
router.get('/users', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const search = req.query.search || '';
    const status = req.query.status || 'all';
    const role = req.query.role || 'all';
    const sortBy = req.query.sortBy || 'created_at';
    const sortOrder = req.query.sortOrder || 'DESC';
    
    const offset = (page - 1) * limit;
    
    // Build where conditions
    let whereConditions = [];
    let queryParams = [];
    
    if (search) {
      whereConditions.push('(email LIKE ? OR full_name LIKE ?)');
      queryParams.push(`%${search}%`, `%${search}%`);
    }
    
    if (status !== 'all') {
      if (status === 'active') {
        whereConditions.push('email_verified = ?');
        queryParams.push(true);
      } else if (status === 'inactive') {
        whereConditions.push('email_verified = ?');
        queryParams.push(false);
      }
    }
    
    if (role !== 'all') {
      whereConditions.push('role = ?');
      queryParams.push(role);
    }
    
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
    
    // Get total count
    const countQuery = `SELECT COUNT(*) as count FROM users ${whereClause}`;
    const totalCount = await database.get(countQuery, queryParams);
    
    // Get users with pagination
    const validSortColumns = ['created_at', 'email', 'full_name', 'last_login'];
    const validSortOrders = ['ASC', 'DESC'];
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'created_at';
    const sortDir = validSortOrders.includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';
    
    const usersQuery = `
      SELECT 
        id, email, full_name, role, plan_type, 
        email_verified, created_at, updated_at
      FROM users 
      ${whereClause}
      ORDER BY ${sortColumn} ${sortDir}
      LIMIT ? OFFSET ?
    `;
    
    const users = await database.all(usersQuery, [...queryParams, limit, offset]);
    
    // Get task counts for users
    const userIds = users.map(u => u.id);
    if (userIds.length > 0) {
      const taskCountsQuery = `
        SELECT user_id, COUNT(*) as task_count 
        FROM tasks 
        WHERE user_id IN (${userIds.map(() => '?').join(',')})
        GROUP BY user_id
      `;
      const taskCounts = await database.all(taskCountsQuery, userIds);
      const taskCountMap = {};
      taskCounts.forEach(tc => {
        taskCountMap[tc.user_id] = tc.task_count;
      });
      
      // Add task counts to users
      users.forEach(user => {
        user.task_count = taskCountMap[user.id] || 0;
      });
    }
    
    res.json({
      users,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        pages: Math.ceil(totalCount.count / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({
      error: 'Failed to fetch users',
      message: error.message
    });
  }
});

// GET /api/admin/users/:id - Get single user details
router.get('/users/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const user = await database.get(`
      SELECT 
        id, email, full_name, role, plan_type,
        email_verified, created_at, updated_at
      FROM users 
      WHERE id = ?
    `, [id]);
    
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: `User with ID ${id} not found`
      });
    }
    
    // Get user's tasks
    const tasks = await database.all(`
      SELECT id, name, status, created_at, updated_at
      FROM tasks 
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT 10
    `, [id]);
    
    // Get user's activity stats
    const [taskStats, lastActivity] = await Promise.all([
      database.get(`
        SELECT 
          COUNT(*) as total_tasks,
          COUNT(CASE WHEN status LIKE '%Completed%' THEN 1 END) as completed_tasks,
          COUNT(CASE WHEN created_at > datetime('now', '-7 days') THEN 1 END) as tasks_last_week
        FROM tasks 
        WHERE user_id = ?
      `, [id]),
      database.get(`
        SELECT MAX(created_at) as last_task_created
        FROM tasks 
        WHERE user_id = ?
      `, [id])
    ]);
    
    res.json({
      user,
      tasks,
      stats: {
        ...taskStats,
        last_activity: lastActivity.last_task_created
      }
    });
  } catch (error) {
    console.error('Error fetching user details:', error);
    res.status(500).json({
      error: 'Failed to fetch user details',
      message: error.message
    });
  }
});

// PUT /api/admin/users/:id - Update user
router.put('/users/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { role, email_verified, plan_type } = req.body;
    
    // Get current user data for audit logging
    const currentUser = await database.get(
      'SELECT id, email, full_name, role, plan_type, email_verified FROM users WHERE id = ?',
      [id]
    );
    
    if (!currentUser) {
      return res.status(404).json({
        error: 'User not found',
        message: `User with ID ${id} not found`
      });
    }
    
    // Validate input
    const validRoles = ['user', 'admin'];
    if (role && !validRoles.includes(role)) {
      return res.status(400).json({
        error: 'Invalid role',
        message: 'Role must be either "user" or "admin"'
      });
    }
    
    // Build update query
    const updates = [];
    const params = [];
    const changedFields = {};
    
    if (role !== undefined && role !== currentUser.role) {
      updates.push('role = ?');
      params.push(role);
      changedFields.role = { old: currentUser.role, new: role };
    }
    
    if (email_verified !== undefined && email_verified !== currentUser.email_verified) {
      updates.push('email_verified = ?');
      params.push(email_verified);
      changedFields.email_verified = { old: currentUser.email_verified, new: email_verified };
    }
    
    if (plan_type !== undefined && plan_type !== currentUser.plan_type) {
      updates.push('plan_type = ?');
      params.push(plan_type);
      changedFields.plan_type = { old: currentUser.plan_type, new: plan_type };
    }
    
    if (updates.length === 0) {
      return res.status(400).json({
        error: 'No updates provided',
        message: 'Please provide at least one field to update'
      });
    }
    
    updates.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);
    
    await database.run(
      `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
      params
    );
    
    // Get updated user
    const updatedUser = await database.get(
      'SELECT id, email, full_name, role, plan_type, email_verified FROM users WHERE id = ?',
      [id]
    );
    
    // Log the admin action
    const requestInfo = AuditLogger.extractRequestInfo(req);
    await AuditLogger.log({
      userId: req.user.id,
      userEmail: req.user.email,
      userRole: req.user.role,
      action: AuditLogger.ACTIONS.ADMIN_USER_UPDATE,
      entityType: 'user',
      entityId: id,
      entityDescription: `User: ${currentUser.email}`,
      changes: changedFields,
      ...requestInfo
    });
    
    res.json({
      message: 'User updated successfully',
      user: updatedUser
    });
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({
      error: 'Failed to update user',
      message: error.message
    });
  }
});

// POST /api/admin/users/:id/reset-password - Reset user password
router.post('/users/:id/reset-password', async (req, res) => {
  try {
    const { id } = req.params;
    const { newPassword } = req.body;
    
    if (!newPassword || newPassword.length < 8) {
      return res.status(400).json({
        error: 'Invalid password',
        message: 'Password must be at least 8 characters long'
      });
    }
    
    // Get user
    const user = await database.get('SELECT id, email FROM users WHERE id = ?', [id]);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: `User with ID ${id} not found`
      });
    }
    
    // Hash new password
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    // Update password
    await database.run(
      'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [hashedPassword, id]
    );
    
    res.json({
      message: 'Password reset successfully',
      email: user.email
    });
  } catch (error) {
    console.error('Error resetting password:', error);
    res.status(500).json({
      error: 'Failed to reset password',
      message: error.message
    });
  }
});

// DELETE /api/admin/users/:id - Delete user (soft delete by disabling)
router.delete('/users/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Don't allow deleting admin's own account
    if (id === req.user.id) {
      return res.status(400).json({
        error: 'Cannot delete own account',
        message: 'You cannot delete your own admin account'
      });
    }
    
    // Check if user exists
    const user = await database.get('SELECT id, email FROM users WHERE id = ?', [id]);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: `User with ID ${id} not found`
      });
    }
    
    // Soft delete by disabling account
    await database.run(
      'UPDATE users SET email_verified = false, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );
    
    res.json({
      message: 'User account disabled successfully',
      email: user.email
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({
      error: 'Failed to delete user',
      message: error.message
    });
  }
});


// Audit Log Routes

// GET /api/admin/audit-logs - Get audit logs with pagination and filtering
router.get('/audit-logs', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const action = req.query.action || '';
    const userId = req.query.userId || '';
    const entityType = req.query.entityType || '';
    const days = parseInt(req.query.days) || 30;
    
    const offset = (page - 1) * limit;
    
    // Build where conditions
    let whereConditions = ['created_at > datetime(\'now\', \'-' + days + ' days\')'];
    let queryParams = [];
    
    if (action) {
      whereConditions.push('action = ?');
      queryParams.push(action);
    }
    
    if (userId) {
      whereConditions.push('user_id = ?');
      queryParams.push(userId);
    }
    
    if (entityType) {
      whereConditions.push('entity_type = ?');
      queryParams.push(entityType);
    }
    
    const whereClause = whereConditions.join(' AND ');
    
    // Get total count
    const countQuery = `SELECT COUNT(*) as count FROM audit_logs WHERE ${whereClause}`;
    const totalCount = await database.get(countQuery, queryParams);
    
    // Get audit logs
    const logsQuery = `
      SELECT * FROM audit_logs 
      WHERE ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const logs = await database.all(logsQuery, [...queryParams, limit, offset]);
    
    // Parse changes JSON for each log
    const parsedLogs = logs.map(log => ({
      ...log,
      changes: log.changes ? JSON.parse(log.changes) : null
    }));
    
    res.json({
      logs: parsedLogs,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        pages: Math.ceil(totalCount.count / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    res.status(500).json({
      error: 'Failed to fetch audit logs',
      message: error.message
    });
  }
});

// GET /api/admin/audit-logs/summary - Get audit summary
router.get('/audit-logs/summary', async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 30;
    const summary = await AuditLogger.getAuditSummary(days);
    
    res.json({ summary });
  } catch (error) {
    console.error('Error fetching audit summary:', error);
    res.status(500).json({
      error: 'Failed to fetch audit summary',
      message: error.message
    });
  }
});

// GET /api/admin/audit-logs/user/:userId - Get user activity
router.get('/audit-logs/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const limit = parseInt(req.query.limit) || 50;
    
    const activity = await AuditLogger.getUserActivity(userId, limit);
    
    res.json({ activity });
  } catch (error) {
    console.error('Error fetching user activity:', error);
    res.status(500).json({
      error: 'Failed to fetch user activity',
      message: error.message
    });
  }
});

// GET /api/admin/tasks/export - Export complete tasks data as CSV
router.get('/tasks/export', async (req, res) => {
  try {
    // Get all tasks with user information
    const tasks = await database.all(`
      SELECT 
        t.id, 
        t.user_id,
        u.email,
        u.full_name,
        t.name,
        t.status,
        t.current_step,
        t.keywords,
        t.selected_topics,
        t.topic_suggestions,
        t.keyword_research_data,
        t.keyword_research_selections,
        t.sources,
        t.topic_sources,
        t.product_info,
        t.eeat_profile,
        t.output_parameters,
        t.generated_article,
        t.created_at,
        t.updated_at
      FROM tasks t
      LEFT JOIN users u ON t.user_id = u.id
      ORDER BY t.created_at DESC
    `);

    // Convert to CSV format with complete data
    const csvHeaders = [
      'Task ID',
      'User ID', 
      'Email',
      'Full Name',
      'Task Name',
      'Status',
      'Current Step',
      'Keywords (JSON)',
      'Selected Topics (JSON)',
      'Topic Suggestions (JSON)',
      'Keyword Research Data (JSON)',
      'Keyword Research Selections (JSON)',
      'Sources (JSON)',
      'Topic Sources (JSON)',
      'Product Info (JSON)',
      'EEAT Profile (JSON)',
      'Output Parameters (JSON)',
      'Generated Article',
      'Created At',
      'Updated At'
    ];

    const csvRows = tasks.map(task => {
      // Helper function to escape CSV fields
      const escapeCsvField = (field) => {
        const str = String(field || '');
        // Always wrap in quotes if field contains comma, quote, newline, or is empty
        if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r') || str === '') {
          return '"' + str.replace(/"/g, '""') + '"';
        }
        return str;
      };

      return [
        escapeCsvField(task.id),
        escapeCsvField(task.user_id),
        escapeCsvField(task.email || ''),
        escapeCsvField(task.full_name || ''),
        escapeCsvField(task.name),
        escapeCsvField(task.status),
        escapeCsvField(task.current_step),
        escapeCsvField(task.keywords || ''),
        escapeCsvField(task.selected_topics || ''),
        escapeCsvField(task.topic_suggestions || ''),
        escapeCsvField(task.keyword_research_data || ''),
        escapeCsvField(task.keyword_research_selections || ''),
        escapeCsvField(task.sources || ''),
        escapeCsvField(task.topic_sources || ''),
        escapeCsvField(task.product_info || ''),
        escapeCsvField(task.eeat_profile || ''),
        escapeCsvField(task.output_parameters || ''),
        escapeCsvField(task.generated_article || ''),
        escapeCsvField(task.created_at),
        escapeCsvField(task.updated_at)
      ];
    });

    // Combine headers and rows
    const csvContent = [csvHeaders.join(','), ...csvRows.map(row => row.join(','))].join('\n');

    // Set response headers for file download
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = `tasks_complete_export_${timestamp}.csv`;

    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache');

    // Add BOM for proper UTF-8 encoding in Excel
    const BOM = '\uFEFF';
    res.send(BOM + csvContent);
  } catch (error) {
    console.error('Error exporting tasks:', error);
    res.status(500).json({
      error: 'Failed to export tasks',
      message: error.message
    });
  }
});

// GET /api/admin/tasks - Get tasks with debug information
router.get('/tasks', async (req, res) => {
  try {
    const { page = 1, limit = 20, search = '', status = '' } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    let query = `
      SELECT t.*, u.email as user_email, u.full_name as user_name
      FROM tasks t
      LEFT JOIN users u ON t.user_id = u.id
      WHERE 1=1
    `;
    const params = [];
    
    // Add search filter
    if (search) {
      query += ` AND (t.name ILIKE ? OR u.email ILIKE ? OR u.full_name ILIKE ?)`;
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }
    
    // Add status filter
    if (status) {
      query += ` AND t.status = ?`;
      params.push(status);
    }
    
    // Add ordering and pagination
    query += ` ORDER BY t.updated_at DESC LIMIT ? OFFSET ?`;
    params.push(parseInt(limit), offset);
    
    const tasks = await database.all(query, params);
    
    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM tasks t
      LEFT JOIN users u ON t.user_id = u.id
      WHERE 1=1
    `;
    const countParams = [];
    
    if (search) {
      countQuery += ` AND (t.name ILIKE ? OR u.email ILIKE ? OR u.full_name ILIKE ?)`;
      const searchPattern = `%${search}%`;
      countParams.push(searchPattern, searchPattern, searchPattern);
    }
    
    if (status) {
      countQuery += ` AND t.status = ?`;
      countParams.push(status);
    }
    
    const { total } = await database.get(countQuery, countParams);
    
    // Format tasks for frontend
    const formattedTasks = tasks.map(task => ({
      id: task.id,
      name: task.name,
      status: task.status,
      current_step: task.current_step,
      user_email: task.user_email,
      user_name: task.user_name,
      has_debug_prompt: !!task.debug_prompt,
      debug_prompt_length: task.debug_prompt ? task.debug_prompt.length : 0,
      has_generated_article: !!task.generated_article,
      created_at: task.created_at,
      updated_at: task.updated_at,
      keywords: task.keywords ? (function() {
        try { return JSON.parse(task.keywords); } catch (e) { return []; }
      })() : [],
      selected_topics: task.selected_topics ? (function() {
        try { return JSON.parse(task.selected_topics); } catch (e) { return []; }
      })() : []
    }));
    
    res.json({
      tasks: formattedTasks,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching tasks:', error);
    res.status(500).json({
      error: 'Failed to fetch tasks',
      message: error.message
    });
  }
});

// GET /api/admin/tasks/:id/debug - Get debug prompt for a task
router.get('/tasks/:id/debug', async (req, res) => {
  try {
    const task = await database.get(
      `SELECT t.id, t.name, t.debug_prompt, t.generated_article, t.created_at, t.updated_at,
              u.email as user_email, u.full_name as user_name
       FROM tasks t
       LEFT JOIN users u ON t.user_id = u.id
       WHERE t.id = ?`,
      [req.params.id]
    );
    
    if (!task) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found'
      });
    }
    
    res.json({
      id: task.id,
      name: task.name,
      user_email: task.user_email,
      user_name: task.user_name,
      debug_prompt: task.debug_prompt,
      generated_article: task.generated_article, // Return full article content
      has_generated_article: !!task.generated_article,
      article_length: task.generated_article ? task.generated_article.length : 0,
      created_at: task.created_at,
      updated_at: task.updated_at
    });
  } catch (error) {
    console.error('Error fetching task debug info:', error);
    res.status(500).json({
      error: 'Failed to fetch task debug info',
      message: error.message
    });
  }
});

module.exports = router;
