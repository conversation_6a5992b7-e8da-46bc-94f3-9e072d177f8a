const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const paddleService = require('../services/paddleService');
const usageService = require('../services/usageService');
const database = require('../config/database');

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

/**
 * GET /api/subscription/status
 * Get user's current subscription status and usage
 */
router.get('/status', async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Get user subscription info
    const user = await database.get(`
      SELECT 
        plan_type, subscription_status, paddle_customer_id, paddle_subscription_id,
        subscription_current_period_start, subscription_current_period_end,
        trial_ends_at, created_at
      FROM users 
      WHERE id = ?
    `, [userId]);

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Get usage statistics
    const usage = await usageService.getUserUsage(userId);

    // Check if subscription is active
    const isActive = usageService.isSubscriptionActive(user);

    res.json({
      success: true,
      subscription: {
        plan_type: user.plan_type,
        status: user.subscription_status || 'free',
        is_active: isActive,
        paddle_customer_id: user.paddle_customer_id,
        paddle_subscription_id: user.paddle_subscription_id,
        current_period_start: user.subscription_current_period_start,
        current_period_end: user.subscription_current_period_end,
        trial_ends_at: user.trial_ends_at,
        member_since: user.created_at
      },
      usage
    });
  } catch (error) {
    console.error('❌ Error getting subscription status:', error);
    res.status(500).json({ 
      error: 'Failed to get subscription status',
      message: error.message 
    });
  }
});

/**
 * POST /api/subscription/checkout
 * Create checkout session for subscription
 */
router.post('/checkout', async (req, res) => {
  try {
    const userId = req.user.id;
    const { plan_type = 'PRO' } = req.body;

    if (!paddleService.isConfigured()) {
      return res.status(503).json({ 
        error: 'Payment service not configured',
        message: 'Paddle integration is not set up' 
      });
    }

    // Price IDs from Paddle dashboard
    const priceIds = {
      'PRO': process.env.PADDLE_PRO_PRICE_ID,
      'MAX': process.env.PADDLE_UNLIMITED_PRICE_ID
    };

    const priceId = priceIds[plan_type];
    if (!priceId) {
      return res.status(400).json({ 
        error: 'Invalid plan type',
        message: 'Supported plans: PRO, MAX' 
      });
    }

    // Create checkout session
    const checkout = await paddleService.createSubscriptionCheckout(
      userId, 
      priceId,
      { plan_type }
    );

    res.json({
      success: true,
      checkout_url: checkout.url,
      checkout_id: checkout.id
    });
  } catch (error) {
    console.error('❌ Error creating checkout:', error);
    res.status(500).json({ 
      error: 'Failed to create checkout session',
      message: error.message 
    });
  }
});

/**
 * POST /api/subscription/cancel
 * Smart subscription cancellation (trial vs paid)
 */
router.post('/cancel', async (req, res) => {
  try {
    const userId = req.user.id;
    const { reason = 'user_request' } = req.body;

    if (!paddleService.isConfigured()) {
      return res.status(503).json({ 
        error: 'Payment service not configured' 
      });
    }

    // Smart cancellation logic handled in paddleService
    const result = await paddleService.cancelSubscription(userId, reason);

    res.json({
      success: true,
      message: result.is_trial_cancellation 
        ? 'Trial subscription cancelled immediately. You will not be charged.'
        : 'Subscription cancelled. You will continue to have access until the end of your current billing period.',
      is_trial_cancellation: result.is_trial_cancellation,
      effective_date: result.effective_date
    });
  } catch (error) {
    console.error('❌ Error canceling subscription:', error);
    res.status(500).json({ 
      error: 'Failed to cancel subscription',
      message: error.message 
    });
  }
});

/**
 * GET /api/subscription/portal
 * Get customer portal URL for managing subscription
 */
router.get('/portal', async (req, res) => {
  try {
    const userId = req.user.id;

    const user = await database.get(
      'SELECT paddle_customer_id FROM users WHERE id = ?', 
      [userId]
    );

    if (!user?.paddle_customer_id) {
      return res.status(404).json({ 
        error: 'No customer record found' 
      });
    }

    if (!paddleService.isConfigured()) {
      return res.status(503).json({ 
        error: 'Payment service not configured' 
      });
    }

    const portalUrl = await paddleService.getCustomerPortalUrl(
      user.paddle_customer_id,
      `${process.env.FRONTEND_URL}/billing`
    );

    res.json({
      success: true,
      portal_url: portalUrl
    });
  } catch (error) {
    console.error('❌ Error getting portal URL:', error);
    res.status(500).json({ 
      error: 'Failed to get customer portal',
      message: error.message 
    });
  }
});

/**
 * GET /api/subscription/usage
 * Get detailed usage statistics
 */
router.get('/usage', async (req, res) => {
  try {
    const userId = req.user.id;
    const usage = await usageService.getUserUsage(userId);

    res.json({
      success: true,
      usage
    });
  } catch (error) {
    console.error('❌ Error getting usage:', error);
    res.status(500).json({ 
      error: 'Failed to get usage statistics',
      message: error.message 
    });
  }
});

/**
 * GET /api/subscription/plans
 * Get available subscription plans and their limits
 */
router.get('/plans', async (req, res) => {
  try {
    const plans = {
      'V1_DEFAULT_ACCESS': {
        name: 'Free',
        price: 0,
        billing_period: null,
        limits: usageService.getPlanLimits('V1_DEFAULT_ACCESS'),
        description: 'Perfect for trying out Writer J'
      },
      'PRO': {
        name: 'Pro',
        price: 29,
        billing_period: 'monthly',
        limits: usageService.getPlanLimits('PRO'),
        description: 'For serious content creators'
      },
      'ENTERPRISE': {
        name: 'Enterprise',
        price: 'Custom',
        billing_period: 'contact',
        limits: usageService.getPlanLimits('ENTERPRISE'),
        description: 'For teams and organizations'
      }
    };

    res.json({
      success: true,
      plans
    });
  } catch (error) {
    console.error('❌ Error getting plans:', error);
    res.status(500).json({ 
      error: 'Failed to get subscription plans',
      message: error.message 
    });
  }
});

/**
 * POST /api/subscription/webhook
 * Handle Paddle webhooks (public endpoint, no auth required)
 */
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    const signature = req.headers['paddle-signature'];
    const body = req.body;

    if (!signature) {
      console.warn('⚠️ Webhook received without signature');
      return res.status(400).json({ error: 'Missing signature' });
    }

    // Verify webhook signature
    const isValid = paddleService.verifyWebhookSignature(signature, body);
    if (!isValid) {
      console.warn('⚠️ Invalid webhook signature');
      return res.status(401).json({ error: 'Invalid signature' });
    }

    const event = JSON.parse(body.toString());
    const eventType = event.event_type;
    const eventData = event.data;

    console.log('🔔 Paddle webhook received:', eventType);

    // Process the webhook event
    await paddleService.processWebhookEvent(eventType, eventData);

    res.json({ success: true, message: 'Webhook processed' });
  } catch (error) {
    console.error('❌ Webhook processing error:', error);
    res.status(500).json({ 
      error: 'Webhook processing failed',
      message: error.message 
    });
  }
});

module.exports = router;