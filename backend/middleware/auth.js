const { verifyToken } = require('../utils/auth');
const database = require('../config/database');

// Middleware to authenticate JWT tokens
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'No token provided'
      });
    }

    // Verify the token
    const decoded = verifyToken(token);
    
    // Check if user still exists
    const user = await database.get(
      'SELECT id, email, full_name, plan_type, email_verified, role FROM users WHERE id = ?',
      [decoded.userId]
    );

    if (!user) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'User not found'
      });
    }

    // Check if email is verified (optional - can be disabled for development)
    if (process.env.REQUIRE_EMAIL_VERIFICATION === 'true' && !user.email_verified) {
      return res.status(403).json({
        error: 'Email not verified',
        message: 'Please verify your email address before accessing this resource'
      });
    }

    // Add user info to request object
    req.user = {
      id: user.id,
      email: user.email,
      fullName: user.full_name,
      planType: user.plan_type,
      emailVerified: user.email_verified,
      role: user.role || 'user'
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(403).json({
      error: 'Access denied',
      message: 'Invalid token'
    });
  }
};

// Middleware to check if user is authenticated (optional authentication)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = verifyToken(token);
    const user = await database.get(
      'SELECT id, email, full_name, plan_type, email_verified, role FROM users WHERE id = ?',
      [decoded.userId]
    );

    if (user) {
      req.user = {
        id: user.id,
        email: user.email,
        fullName: user.full_name,
        planType: user.plan_type,
        emailVerified: user.email_verified,
        role: user.role || 'user'
      };
    } else {
      req.user = null;
    }

    next();
  } catch (error) {
    req.user = null;
    next();
  }
};

// Middleware to check user plan permissions
const checkPlanPermission = (requiredPlan = 'V1_DEFAULT_ACCESS') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Please log in to access this feature'
      });
    }

    // For now, all users have the same access level
    // This can be expanded later for different subscription tiers
    if (req.user.planType === 'V1_DEFAULT_ACCESS' || req.user.planType === requiredPlan) {
      return next();
    }

    return res.status(403).json({
      error: 'Insufficient permissions',
      message: 'Your current plan does not include access to this feature'
    });
  };
};

// Middleware to check if user owns a resource
const checkResourceOwnership = (resourceType) => {
  return async (req, res, next) => {
    try {
      const resourceId = req.params.id || req.params.taskId || req.params.presetId;
      
      if (!resourceId) {
        return res.status(400).json({
          error: 'Bad request',
          message: 'Resource ID is required'
        });
      }

      let query;
      switch (resourceType) {
        case 'task':
          query = 'SELECT user_id FROM tasks WHERE id = ?';
          break;
        case 'preset':
          query = 'SELECT user_id FROM user_presets WHERE id = ?';
          break;
        default:
          return res.status(500).json({
            error: 'Server error',
            message: 'Invalid resource type'
          });
      }

      const resource = await database.get(query, [resourceId]);
      
      if (!resource) {
        return res.status(404).json({
          error: 'Not found',
          message: `${resourceType} not found`
        });
      }

      if (resource.user_id !== req.user.id) {
        return res.status(403).json({
          error: 'Access denied',
          message: `You don't have permission to access this ${resourceType}`
        });
      }

      next();
    } catch (error) {
      console.error('Resource ownership check error:', error);
      return res.status(500).json({
        error: 'Server error',
        message: 'Failed to verify resource ownership'
      });
    }
  };
};

// Middleware to require admin role
const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      error: 'Access denied',
      message: 'Authentication required'
    });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({
      error: 'Access denied',
      message: 'Admin privileges required'
    });
  }

  next();
};

module.exports = {
  authenticateToken,
  optionalAuth,
  checkPlanPermission,
  checkResourceOwnership,
  requireAdmin
};
