const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const ideationRoutes = require('./routes/ideation');
const sourcesRoutes = require('./routes/sources');
const generateRoutes = require('./routes/generate');
const authRoutes = require('./routes/auth');
const tasksRoutes = require('./routes/tasks');
const presetsRoutes = require('./routes/presets');
const resourcesRoutes = require('./routes/resources');
const adminRoutes = require('./routes/admin');
const redditRoutes = require('./routes/reddit');
const { router: redditOAuthRoutes } = require('./routes/reddit-oauth');
const subscriptionRoutes = require('./routes/subscription');
const { validateApiKey, sanitizeInput, validateRequestSize, validateContentType } = require('./middleware/security');
const database = require('./config/database');
const aiServiceManager = require('./services/aiServiceManager');

const app = express();
const PORT = process.env.PORT || 3001;

// Trust proxy for Railway deployment (fixes rate limiting issues)
app.set('trust proxy', 1);

// Set NODE_ENV to production if running on Railway (has PORT env var but no explicit NODE_ENV)
if (process.env.PORT && !process.env.NODE_ENV) {
  process.env.NODE_ENV = 'production';
}

// Debug logging for Railway (reduced in development)
if (process.env.NODE_ENV !== 'development') {
  console.log('=== SERVER STARTUP DEBUG ===');
  console.log('PORT environment variable:', process.env.PORT);
  console.log('Using PORT:', PORT);
  console.log('NODE_ENV:', process.env.NODE_ENV);
  console.log('Is production?', process.env.NODE_ENV === 'production');
  console.log('All environment variables:', Object.keys(process.env).filter(key => key.includes('PORT') || key.includes('HOST') || key.includes('NODE')));
  console.log('============================');
}

// Rate limiting - more generous limits for development
const isDevelopment = process.env.NODE_ENV === 'development';

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: isDevelopment ? 1000 : 100, // Higher limit for development
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: isDevelopment ? 500 : 50, // Higher limit for development
  message: {
    error: 'Too many API requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false, // Allow embedding for development
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));
// Simple CORS configuration - allow all Vercel domains
app.use(cors({
  origin: [
    'https://writer-j.com',
    'https://www.writer-j.com',
    'https://j-writer.vercel.app',
    /^https:\/\/.*\.vercel\.app$/,
    /^https:\/\/.*\.railway\.app$/,
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:3000',
    'http://localhost:3200'
  ],
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
}));
app.use(morgan('combined'));
app.use(limiter); // Apply general rate limiting to all requests
app.use(validateRequestSize); // Validate request size before parsing
app.use(express.json({ limit: '5mb' })); // Increased for batch generation
app.use(express.urlencoded({ extended: true, limit: '5mb' }));
app.use(sanitizeInput); // Sanitize all input data

// Routes with API key validation, rate limiting and content validation
app.use('/api/ideation', apiLimiter, validateApiKey, validateContentType, ideationRoutes);
app.use('/api/sources', apiLimiter, validateApiKey, validateContentType, sourcesRoutes);
app.use('/api/generate', apiLimiter, validateApiKey, validateContentType, generateRoutes);

// Authentication routes (no API key required for public auth endpoints)
app.use('/api/auth', apiLimiter, validateContentType, authRoutes);

// Protected routes (require authentication)
app.use('/api/tasks', apiLimiter, validateContentType, tasksRoutes);
app.use('/api/batch-tasks', apiLimiter, validateContentType, require('./routes/batch-tasks'));
app.use('/api/presets', apiLimiter, validateContentType, presetsRoutes);
app.use('/api/resources', apiLimiter, validateContentType, resourcesRoutes);
app.use('/api/reddit', apiLimiter, validateContentType, redditRoutes);
app.use('/api/reddit-oauth', apiLimiter, validateContentType, redditOAuthRoutes);
app.use('/api/subscription', apiLimiter, validateContentType, subscriptionRoutes);

// Blog routes (public access for reading, admin for writing)
app.use('/api/blog', apiLimiter, validateContentType, require('./routes/blog'));
app.use('/api/pillar-pages', apiLimiter, validateContentType, require('./routes/pillar-pages'));

// Admin routes (require authentication and admin role)
app.use('/api/admin', apiLimiter, validateContentType, adminRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'AI Article Generator API is running',
    endpoints: {
      health: '/health',
      ideation: '/api/ideation',
      sources: '/api/sources',
      generate: '/api/generate'
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'OK', message: 'AI Article Generator API is running' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Run database migrations (only in production or when explicitly requested)
const runMigrations = async () => {
  // Skip migration checks in development unless explicitly requested
  if (process.env.NODE_ENV === 'development' && !process.env.FORCE_MIGRATIONS) {
    console.log('⚡ Skipping migration checks in development mode');
    return;
  }

  console.log('🔄 Checking for database migrations...');

  try {
    // Check if role column exists
    await database.get('SELECT role FROM users LIMIT 1');
    console.log('✅ All migrations up to date');
  } catch (error) {
    if (error.message.includes('column "role" does not exist') || error.message.includes('no such column: role')) {
      console.log('📝 Running migration: Adding role column...');

      try {
        if (database.isPostgres) {
          await database.run('ALTER TABLE users ADD COLUMN role VARCHAR(20) DEFAULT \'user\'');
        } else {
          await database.run('ALTER TABLE users ADD COLUMN role TEXT DEFAULT \'user\'');
        }
        console.log('✅ Migration completed: Role column added');
      } catch (migrationError) {
        console.error('❌ Migration failed:', migrationError);
        throw migrationError;
      }
    } else {
      console.error('❌ Unexpected error during migration check:', error);
      throw error;
    }
  }
};

// Initialize database and start server
const startServer = async () => {
  try {
    // Connect to database and initialize tables
    await database.connect();
    console.log('Database initialized successfully');

    // Run any pending migrations
    await runMigrations();

    // Initialize AI Service Manager
    console.log('Initializing AI Service Manager...');
    await aiServiceManager.initialize();
    console.log('AI Service Manager initialized successfully');

    // Start the server
    console.log('About to start server on port:', PORT);
    console.log('Port type:', typeof PORT);
    
    const server = app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`);
      console.log(`Health check: http://localhost:${PORT}/health`);
      console.log('Environment:', process.env.NODE_ENV || 'development');
      
      // 验证服务器真的在监听
      const address = server.address();
      console.log('Server is listening on:', address);
      
      // 测试本地连接
      setTimeout(() => {
        console.log('Testing local connection...');
        require('http').get(`http://localhost:${PORT}/health`, (res) => {
          console.log('Local test successful:', res.statusCode);
        }).on('error', (err) => {
          console.log('Local test failed:', err.message);
        });
      }, 1000);
    }).on('error', (err) => {
      console.error('Server failed to start:', err);
      process.exit(1);
    });
    
    console.log('Listener attached');
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\nShutting down gracefully...');
  try {
    await database.close();
    console.log('Database connection closed');
    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  console.log('\nReceived SIGTERM, shutting down gracefully...');
  try {
    await database.close();
    console.log('Database connection closed');
    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
});

startServer();
