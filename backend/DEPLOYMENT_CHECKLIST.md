# Railway PostgreSQL Deployment Checklist

Follow this checklist to successfully deploy your J-Writer application with PostgreSQL on Railway.

## Pre-Deployment Setup

### ✅ 1. Railway Project Setup
- [ ] Create a new Railway project (if not already done)
- [ ] Add PostgreSQL service to your Railway project
- [ ] Note down the PostgreSQL connection details

### ✅ 2. Environment Variables Configuration

In your Railway project dashboard, set up these environment variables:

**Required Variables:**
- [ ] `DATABASE_URL` = `${{ Postgres.DATABASE_URL }}`
- [ ] `GEMINI_API_KEY` = `your_gemini_api_key_here`
- [ ] `API_KEY` = `your_system_api_key_here`
- [ ] `SERPER_API_KEY` = `your_serper_api_key_here`
- [ ] `JWT_SECRET` = `your_secure_jwt_secret_here`
- [ ] `NODE_ENV` = `production`
- [ ] `PORT` = `3001`

**Optional Variables:**
- [ ] `FRONTEND_URL` = `https://your-frontend-domain.vercel.app`

### ✅ 3. Code Verification

Ensure your codebase has these updates:
- [ ] PostgreSQL client (`pg`) is installed
- [ ] Database configuration supports both SQLite and PostgreSQL
- [ ] Environment variables are properly configured
- [ ] Railway.toml includes database initialization

## Deployment Steps

### ✅ 4. Deploy to Railway

1. **Connect Repository:**
   ```bash
   # If using Railway CLI
   railway login
   railway link
   ```

2. **Deploy:**
   ```bash
   # Push to your connected repository
   git add .
   git commit -m "Add PostgreSQL support for Railway deployment"
   git push origin main
   ```

3. **Or use Railway CLI:**
   ```bash
   railway up
   ```

### ✅ 5. Monitor Deployment

Watch the deployment logs for:
- [ ] "Connected to PostgreSQL database"
- [ ] "Database tables initialized successfully"
- [ ] "Database connection test passed"
- [ ] Server starts successfully on specified port

### ✅ 6. Verify Database Setup

1. **Check Railway Dashboard:**
   - [ ] PostgreSQL service is running
   - [ ] Database connection is active
   - [ ] No connection errors in logs

2. **Test API Endpoints:**
   - [ ] Health check endpoint responds
   - [ ] Authentication endpoints work
   - [ ] Database-dependent endpoints function correctly

## Post-Deployment Verification

### ✅ 7. Database Tables Verification

Connect to your PostgreSQL database and verify tables exist:

```sql
-- List all tables
\dt

-- Check users table structure
\d users

-- Check tasks table structure  
\d tasks

-- Verify indexes
\di
```

Expected tables:
- [ ] `users`
- [ ] `tasks`
- [ ] `user_presets`
- [ ] `blog_posts`
- [ ] `user_sessions`

### ✅ 8. Application Testing

Test core functionality:
- [ ] User registration works
- [ ] User login works
- [ ] Task creation works
- [ ] Article generation works
- [ ] Data persistence verified

### ✅ 9. Performance Check

- [ ] Database queries execute efficiently
- [ ] Connection pooling works correctly
- [ ] No memory leaks in database connections
- [ ] Response times are acceptable

## Troubleshooting Common Issues

### Database Connection Errors

**Error: "Connection refused"**
- Check if `DATABASE_URL` environment variable is set correctly
- Verify PostgreSQL service is running in Railway dashboard
- Ensure Railway project has PostgreSQL add-on

**Error: "SSL required"**
- This should be handled automatically by the configuration
- Check Railway PostgreSQL SSL settings

**Error: "Too many connections"**
- Check connection pool settings in database.js
- Monitor active connections in Railway dashboard

### Deployment Errors

**Error: "Module not found: pg"**
- Ensure `pg` is in package.json dependencies
- Run `npm install` locally and commit package-lock.json

**Error: "Database initialization failed"**
- Check Railway logs for specific database errors
- Verify environment variables are set correctly
- Ensure PostgreSQL service is fully provisioned

### Performance Issues

**Slow database queries:**
- Check if indexes are created properly
- Monitor query performance in Railway dashboard
- Consider optimizing frequently used queries

**High memory usage:**
- Monitor connection pool usage
- Check for connection leaks
- Adjust pool size if necessary

## Rollback Plan

If deployment fails:

1. **Immediate Rollback:**
   ```bash
   # Revert to previous working commit
   git revert HEAD
   git push origin main
   ```

2. **Database Rollback:**
   - Railway automatically handles database persistence
   - Data should remain intact during rollbacks
   - If needed, restore from Railway database backups

3. **Environment Rollback:**
   - Revert environment variables in Railway dashboard
   - Switch back to SQLite for local development if needed

## Success Criteria

Deployment is successful when:
- [ ] Application starts without errors
- [ ] Database connection is established
- [ ] All tables and indexes are created
- [ ] API endpoints respond correctly
- [ ] Frontend can communicate with backend
- [ ] User authentication works
- [ ] Article generation functionality works
- [ ] Data persists correctly in PostgreSQL

## Next Steps After Successful Deployment

1. **Monitor Application:**
   - Set up Railway monitoring and alerts
   - Monitor database performance metrics
   - Track application logs for errors

2. **Backup Strategy:**
   - Configure Railway database backups
   - Test backup restoration process
   - Document backup procedures

3. **Scaling Considerations:**
   - Monitor database connection usage
   - Plan for connection pool scaling
   - Consider read replicas if needed

4. **Security Review:**
   - Verify SSL connections are working
   - Review environment variable security
   - Ensure sensitive data is properly encrypted
