const geminiService = require('./geminiService');
const deepseekService = require('./deepseekService');
const database = require('../config/database');
const { getPrompt } = require('../config/prompts');

class AIServiceManager {
  constructor() {
    this.services = {
      'gemini': geminiService,
      'deepseek': deepseekService
    };
    this.defaultModel = 'gemini';
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;

    try {
      // Initialize default models if they don't exist
      await this.initializeDefaultModels();
      this.initialized = true;
      console.log('AI Service Manager initialized successfully (using code-based prompts)');
    } catch (error) {
      console.error('Failed to initialize AI Service Manager:', error);
      throw error;
    }
  }

  async initializeDefaultModels() {
    try {
      // Check if models already exist
      const existingModels = await database.all('SELECT * FROM ai_models');
      
      if (existingModels.length === 0) {
        console.log('Initializing default AI models...');
        
        // Insert Gemini model (with empty API key - to be configured via admin)
        const defaultGeminiVersion = process.env.GEMINI_DEFAULT_MODEL_VERSION || 'gemini-2.5-pro';
        await database.run(
          'INSERT INTO ai_models (model_name, provider, api_key, model_version, is_active, is_default) VALUES (?, ?, ?, ?, ?, ?)',
          ['gemini', 'google', process.env.GEMINI_API_KEY || '', defaultGeminiVersion, true, true]
        );

        // Insert DeepSeek model (with empty API key - to be configured via admin)
        await database.run(
          'INSERT INTO ai_models (model_name, provider, api_key, model_version, is_active, is_default) VALUES (?, ?, ?, ?, ?, ?)',
          ['deepseek', 'deepseek', process.env.DEEPSEEK_API_KEY || '', 'deepseek-reasoner', true, false]
        );

        console.log('Default AI models initialized');
      }
    } catch (error) {
      console.error('Error initializing default models:', error);
      throw error;
    }
  }

  // Prompts are now managed in code-based prompts.js file
  // This method is no longer needed but kept for compatibility

  async getPromptTemplate(templateName, variables = {}) {
    // Use code-based prompts - no fallback, let errors surface
    return getPrompt(templateName, variables);
  }

  // No fallback - let errors surface for better debugging

  async generateWithPrompt(prompt, context = '') {
    try {
      await this.initialize();
      
      // Get active model
      const activeModel = await this.getActiveModel();
      const service = this.services[activeModel.model_name];
      
      if (!service) {
        throw new Error(`Service for model ${activeModel.model_name} not found`);
      }

      // Generate content using the service
      const result = await service.generateContent(prompt);
      return result;
    } catch (error) {
      console.error('Error generating with prompt:', error);
      throw error;
    }
  }

  async getActiveModel() {
    try {
      const model = await database.get(
        'SELECT * FROM ai_models WHERE is_active = true ORDER BY is_default DESC, id ASC LIMIT 1'
      );
      
      if (!model) {
        throw new Error('No active AI model found');
      }
      
      return model;
    } catch (error) {
      console.error('Error getting active model:', error);
      // No active model found - this should not happen in production
      throw new Error('No active AI model found in database. Please configure via admin panel.');
    }
  }

  // Admin methods for managing models and prompts
  async getAllModels() {
    try {
      const models = await database.all('SELECT * FROM ai_models ORDER BY model_name');
      return models;
    } catch (error) {
      console.error('Error getting all models:', error);
      throw error;
    }
  }

  async updateModel(modelName, updates) {
    try {
      const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');
      const values = Object.values(updates);
      values.push(modelName);

      await database.run(
        `UPDATE ai_models SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE model_name = ?`,
        values
      );

      const updatedModel = await database.get(
        'SELECT * FROM ai_models WHERE model_name = ?',
        [modelName]
      );

      return updatedModel;
    } catch (error) {
      console.error('Error updating model:', error);
      throw error;
    }
  }

  // Prompt templates are now managed in code (prompts.js)
  // These methods are deprecated but kept for API compatibility
  async getAllPromptTemplates() {
    console.warn('getAllPromptTemplates is deprecated - prompts are now managed in code');
    return [];
  }

  async updatePromptTemplate(id, updates) {
    console.warn('updatePromptTemplate is deprecated - prompts are now managed in code');
    throw new Error('Prompt templates are now managed in code. Please update prompts.js file directly.');
  }

  async ensureServiceInitialized(modelName) {
    // This method is called by admin when API keys are updated
    // For now, just log - actual reinitialization would depend on service implementation
    console.log(`Service reinitialization requested for ${modelName}`);
    return true;
  }

  // Enhanced batch generation methods
  async parseIdeasToKeywords(ideas, targetCount = 10) {
    try {
      const prompt = await this.getPromptTemplate('IDEAS_TO_KEYWORDS', {
        ideas,
        targetCount
      });

      const response = await this.generateWithPrompt(prompt, 'IDEAS_TO_KEYWORDS');
      
      if (response && typeof response === 'string') {
        // Parse the response to extract keywords
        const keywords = response
          .split('\n')
          .map(line => line.trim())
          .filter(line => line.length > 0 && !line.startsWith('#') && !line.startsWith('*'))
          .slice(0, targetCount);
        
        return keywords.length > 0 ? keywords : [`ideas about ${ideas.substring(0, 50)}`];
      }
      
      return [`ideas about ${ideas.substring(0, 50)}`];
    } catch (error) {
      console.error('Error parsing ideas to keywords:', error);
      return [`ideas about ${ideas.substring(0, 50)}`];
    }
  }

  async enhancedKeywordResearch(keyword, useRealSerperAPI = false) {
    try {
      // Enhanced keyword research with multiple data sources
      const keywordData = {
        originalKeyword: keyword,
        autocomplete: [],
        relatedKeywords: [],
        peopleAlsoAsk: [],
        keyTerms: [],
        searchVolume: 'N/A',
        difficulty: 'Medium'
      };

      if (process.env.SERPER_API_KEY) {
        // Always use real Serper API for enhanced research
        const serperService = require('./serperService');
        const realData = await serperService.getKeywordResearch(keyword);

        // Transform serper data to match expected format
        keywordData.autocomplete = realData.relatedKeywords?.map(k => k.query) || [];
        keywordData.relatedKeywords = realData.relatedKeywords?.map(k => k.query) || [];
        keywordData.peopleAlsoAsk = realData.peopleAlsoAsk?.map(p => p.question) || [];
        keywordData.keyTerms = realData.wordCloudData?.words?.slice(0, 10).map(w => w.word) || [keyword];
      } else {
        console.error('❌ SERPER_API_KEY not found - cannot perform keyword research');
        throw new Error('SERPER_API_KEY is required for keyword research. Please configure your API key.');
      }

      return keywordData;
    } catch (error) {
      console.error('Error in enhanced keyword research:', error);
      throw new Error(`Failed to perform keyword research: ${error.message}`);
    }
  }

  async intelligentResourceIntegration(keyword, keywordData, globalSources = [], redditPosts = []) {
    try {
      // Prepare resource list for AI analysis
      const allResources = [];
      
      // Add global sources
      globalSources.forEach((source, index) => {
        allResources.push({
          index: index + 1,
          type: 'url',
          url: source,
          title: `External Source ${index + 1}`,
          description: `URL resource: ${source}`
        });
      });
      
      // Add Reddit posts
      redditPosts.forEach((post, index) => {
        allResources.push({
          index: globalSources.length + index + 1,
          type: 'reddit',
          url: post,
          title: `Reddit Discussion ${index + 1}`,
          description: `Community discussion: ${post}`
        });
      });

      if (allResources.length === 0) {
        console.log(`📋 No resources provided for keyword: ${keyword}`);
        return {
          combinedResources: [],
          totalSources: globalSources.length,
          totalRedditPosts: redditPosts.length,
          averageRelevance: 0,
          aiAnalysis: {
            strategy: 'No external resources available',
            recommendations: [`Focus on ${keyword} fundamentals`, `Include practical examples`, `Add expert insights`]
          },
          contentGaps: [`Basic ${keyword} concepts`, `Advanced techniques`, `Real-world applications`],
          resourcesSummary: {
            totalProcessed: 0,
            qualifiedResources: 0,
            averageRelevance: 0
          }
        };
      }

      // Use AI prompt for intelligent resource analysis
      const resourcesList = allResources.map(r => 
        `${r.index}. [${r.type.toUpperCase()}] ${r.title}\n   URL: ${r.url}\n   Description: ${r.description}`
      ).join('\n\n');

      const prompt = await this.getPromptTemplate('RESOURCE_INTEGRATION_ANALYSIS', {
        keyword: keywordData.originalKeyword,
        autocomplete: keywordData.autocomplete?.join(', ') || '',
        relatedKeywords: keywordData.relatedKeywords?.join(', ') || '',
        peopleAlsoAsk: keywordData.peopleAlsoAsk?.join(', ') || '',
        keyTerms: keywordData.keyTerms?.join(', ') || '',
        resourcesList: resourcesList
      });

      const response = await this.generateWithPrompt(prompt, 'RESOURCE_INTEGRATION_ANALYSIS');
      
      if (response && typeof response === 'string') {
        try {
          // Try to parse JSON response
          const analysisResult = JSON.parse(response);
          
          // Transform AI analysis back to expected format
          const relevantResources = analysisResult.analyzedResources?.map(resource => ({
            title: resource.title,
            url: resource.url,
            summary: resource.integrationSuggestions || 'AI-analyzed resource for article enhancement',
            relevanceScore: resource.relevanceScore,
            type: resource.contentType === 'community_discussion' ? 'community' : 'external',
            keyTopics: resource.keyTopics,
            valuableInsights: resource.valuableInsights
          })) || [];

          // Sort by relevance score
          relevantResources.sort((a, b) => b.relevanceScore - a.relevanceScore);

          // Apply relevance threshold (50+) and limit to top 3 resources
          const highQualityResources = relevantResources.filter(r => r.relevanceScore >= 50);
          
          return {
            combinedResources: highQualityResources.slice(0, 3), // Top 3 most relevant resources with 50+ score
            totalSources: globalSources.length,
            totalRedditPosts: redditPosts.length,
            averageRelevance: highQualityResources.length > 0 ? 
              highQualityResources.reduce((sum, r) => sum + r.relevanceScore, 0) / highQualityResources.length : 0,
            aiAnalysis: analysisResult.integrationStrategy,
            contentGaps: analysisResult.contentGaps
          };
        } catch (parseError) {
          console.error('Failed to parse AI resource analysis:', parseError);
          throw new Error(`Failed to parse AI resource analysis: ${parseError.message}`);
        }
      }

      // If AI analysis fails, throw error instead of fallback
      throw new Error('AI resource analysis failed to return valid results');
    } catch (error) {
      console.error('Error in intelligent resource integration:', error);
      throw new Error(`Failed to integrate resources intelligently: ${error.message}`);
    }
  }

  // Removed fallbackResourceIntegration - no fallbacks allowed

  async generateTopicAndOutline(keywordData, resources, settings) {
    try {
      const prompt = await this.getPromptTemplate('TOPIC_OUTLINE_GENERATION', {
        keyword: keywordData.originalKeyword,
        autocomplete: keywordData.autocomplete.join(', '),
        relatedKeywords: keywordData.relatedKeywords.join(', '),
        peopleAlsoAsk: keywordData.peopleAlsoAsk.join(', '),
        keyTerms: keywordData.keyTerms.join(', '),
        resources: resources.combinedResources.map(r => `- ${r.title}: ${r.summary}`).join('\n'),
        targetAudience: settings.targetAudience || 'general users',
        tonality: settings.tonality || 'informative',
        length: settings.length || 'medium_article'
      });

      const response = await this.generateWithPrompt(prompt, 'TOPIC_OUTLINE_GENERATION');

      if (response && typeof response === 'string' && response.length > 50) {
        return this.parseOutlineResponse(response, keywordData);
      }

      // No fallback - throw error to surface the issue
      throw new Error(`Invalid AI response for topic outline generation: ${typeof response}, length: ${response ? response.length : 'null'}`);
    } catch (error) {
      console.error('Error generating topic and outline:', error);
      throw new Error(`Failed to generate topic and outline: ${error.message}`);
    }
  }

  parseOutlineResponse(response, keywordData) {
    try {
      // Extract all sections from the enhanced AI response
      const lines = response.split('\n').filter(line => line.trim());
      
      let title = '';
      let outline = '';
      let contentStrategy = {};
      let resourceIntegrationPlan = '';
      let seoOptimizations = '';
      
      // Extract Article Title
      const titleLine = lines.find(line => 
        line.includes('**Article Title:**') || 
        line.includes('Title:') || 
        line.startsWith('# ')
      );
      
      if (titleLine) {
        title = titleLine.replace(/\*\*Article Title:\*\*|\*\*Title:\*\*|#/g, '').trim();
      } else {
        title = `Complete Guide to ${keywordData.originalKeyword}`;
      }
      
      // Extract Content Strategy Summary
      const strategyStartIndex = lines.findIndex(line => 
        line.includes('**Content Strategy Summary:**')
      );
      if (strategyStartIndex !== -1) {
        const strategyEndIndex = lines.findIndex((line, index) => 
          index > strategyStartIndex && line.includes('**Detailed Article Outline:**')
        );
        if (strategyEndIndex !== -1) {
          const strategyLines = lines.slice(strategyStartIndex + 1, strategyEndIndex);
          strategyLines.forEach(line => {
            if (line.includes('Primary Search Intent:')) {
              contentStrategy.searchIntent = line.split(':')[1]?.trim();
            } else if (line.includes('Unique Value Proposition:')) {
              contentStrategy.valueProposition = line.split(':')[1]?.trim();
            } else if (line.includes('Target User Journey Stage:')) {
              contentStrategy.userJourney = line.split(':')[1]?.trim();
            }
          });
        }
      }
      
      // Extract Detailed Article Outline
      const outlineStartIndex = lines.findIndex(line => 
        line.includes('**Detailed Article Outline:**') || 
        line.includes('**Article Outline:**') ||
        line.includes('## Introduction')
      );
      
      if (outlineStartIndex !== -1) {
        const outlineEndIndex = lines.findIndex((line, index) => 
          index > outlineStartIndex && line.includes('**Resource Integration Plan:**')
        );
        if (outlineEndIndex !== -1) {
          outline = lines.slice(outlineStartIndex + 1, outlineEndIndex).join('\n');
        } else {
          outline = lines.slice(outlineStartIndex + 1).join('\n');
        }
      } else {
        outline = this.generateBasicOutline(keywordData);
      }
      
      // Extract Resource Integration Plan
      const resourcePlanIndex = lines.findIndex(line => 
        line.includes('**Resource Integration Plan:**')
      );
      if (resourcePlanIndex !== -1) {
        const seoIndex = lines.findIndex((line, index) => 
          index > resourcePlanIndex && line.includes('**SEO Optimization Notes:**')
        );
        if (seoIndex !== -1) {
          resourceIntegrationPlan = lines.slice(resourcePlanIndex + 1, seoIndex).join('\n');
        }
      }
      
      // Extract SEO Optimization Notes
      const seoStartIndex = lines.findIndex(line => 
        line.includes('**SEO Optimization Notes:**')
      );
      if (seoStartIndex !== -1) {
        const seoEndIndex = lines.findIndex((line, index) => 
          index > seoStartIndex && line.includes('**Content Quality Assurance:**')
        );
        if (seoEndIndex !== -1) {
          seoOptimizations = lines.slice(seoStartIndex + 1, seoEndIndex).join('\n');
        }
      }
      
      return {
        title,
        outline,
        keyword: keywordData.originalKeyword,
        contentStrategy,
        resourceIntegrationPlan,
        seoOptimizations,
        rawResponse: response // 保留原始响应用于调试
      };
    } catch (error) {
      console.error('Error parsing enhanced outline response:', error);
      throw new Error(`Failed to parse outline response: ${error.message}`);
    }
  }

  // Removed generateFallbackOutline - no fallbacks allowed

  generateBasicOutline(keywordData) {
    const keyword = keywordData.originalKeyword;
    return `## 1. Introduction to ${keyword}
## 2. Understanding ${keyword}
## 3. How to Use ${keyword}
## 4. Benefits of ${keyword}
## 5. Common Questions About ${keyword}
## 6. Conclusion`;
  }

  async generateAdvancedArticle(articleData) {
    try {
      // 构建完整的文章输入数据
      const keyword = articleData.primaryKeywords?.[0] || articleData.keyword || 'topic';
      
      console.log(`🎯 ADVANCED ARTICLE GENERATION DEBUG`);
      console.log(`📝 Title: ${articleData.title}`);
      console.log(`🔑 Primary Keyword: ${keyword}`);
      console.log(`👥 Target Audience: ${articleData.targetAudience || 'general readers'}`);
      console.log(`🎭 Tonality: ${articleData.tonality || 'informative'}`);
      console.log(`📏 Length: ${articleData.length || 'medium_article'}`);
      
      // Format Reddit content if available
      let redditContentSection = '';
      if (articleData.redditContent && Array.isArray(articleData.redditContent) && articleData.redditContent.length > 0) {
        redditContentSection = `

**Reddit Community Insights:**
${articleData.redditContent.map((post, index) => 
  `${index + 1}. ${post.url || 'Reddit Post'}
   ${post.content || post.title || 'Community discussion content'}`
).join('\n\n')}`;
        console.log(`🔥 Reddit content sections: ${articleData.redditContent.length}`);
      }

      const article_inputs = `
**Title:** ${articleData.title}
**Primary Keyword:** ${keyword}
**Secondary Keywords:** ${articleData.secondaryKeywords?.join(', ') || 'N/A'}
**Target Audience:** ${articleData.targetAudience || 'general readers'}
**Tonality:** ${articleData.tonality || 'informative'}
**Length:** ${articleData.length || 'medium_article'}
**Author Name:** ${articleData.authorName || 'N/A'}
**Author Bio:** ${articleData.authorBio || 'N/A'}

**Article Outline:**
${articleData.outline || 'No outline provided'}

**Comprehensive Resources:**
${articleData.comprehensiveResources?.map(r => `- ${r.title}: ${r.summary || r.description || 'No description'}`).join('\n') || 'No resources provided'}${redditContentSection}

**Product Information:**
${JSON.stringify(articleData.productInfo || {}, null, 2)}

**Additional Context:**
- Content Strategy: ${JSON.stringify(articleData.contentStrategy || {}, null, 2)}
- Resource Integration Plan: ${articleData.resourceIntegrationPlan || 'N/A'}
- SEO Optimizations: ${articleData.seoOptimizations || 'N/A'}
`;

      console.log(`🔍 Getting prompt template: ADVANCED_ARTICLE_GENERATION`);
      const prompt = await this.getPromptTemplate('ADVANCED_ARTICLE_GENERATION', {
        article_inputs: article_inputs,
        keyword: keyword,
        title: articleData.title || 'Untitled',
        targetAudience: articleData.targetAudience || 'general readers',
        tonality: articleData.tonality || 'informative',
        length: articleData.length || 'medium_article',
        outline: articleData.outline || 'No outline provided',
        resources: articleData.comprehensiveResources?.map(r => `- ${r.title}: ${r.summary || r.description || 'No description'}`).join('\n') || 'No resources provided',
        authorName: articleData.authorName || '',
        authorBio: articleData.authorBio || '',
        productInfo: JSON.stringify(articleData.productInfo || {}, null, 2)
      });
      
      console.log(`🤖 Calling AI service for article generation...`);
      console.log(`📋 Prompt length: ${prompt.length} characters`);

      const response = await this.generateWithPrompt(prompt, 'ADVANCED_ARTICLE_GENERATION');
      
      console.log(`✅ AI Response received:`);
      console.log(`   - Type: ${typeof response}`);
      console.log(`   - Length: ${response ? response.length : 'null/undefined'} characters`);
      console.log(`   - Preview: ${response ? response.substring(0, 200) + '...' : 'NO CONTENT'}`);
      
      if (response && typeof response === 'string' && response.length > 100) {
        console.log(`🎉 Returning valid AI-generated article`);
        return response;
      } else {
        console.error(`❌ INVALID AI RESPONSE - No fallback allowed`);
        console.error(`   - Response type: ${typeof response}`);
        console.error(`   - Response length: ${response ? response.length : 'null/undefined'}`);
        console.error(`   - Response preview: ${response || 'NO CONTENT'}`);

        // No fallback - throw error to surface the issue
        throw new Error(`AI generated invalid response: ${typeof response}, length: ${response ? response.length : 'null'}`);
      }

    } catch (error) {
      console.error(`💥 CRITICAL ERROR in generateAdvancedArticle:`, error);
      console.error(`   - Error message: ${error.message}`);
      console.error(`   - Error stack: ${error.stack}`);

      // No fallback - throw error to surface the issue
      throw new Error(`Failed to generate advanced article: ${error.message}`);
    }
  }

  // Removed generateFallbackArticle - no fallbacks allowed

  calculateQualityMetrics(article, keywordData) {
    try {
      const wordCount = article.split(/\s+/).length;
      const keywordDensity = this.calculateKeywordDensity(article, keywordData.originalKeyword);
      
      // Simple quality scoring
      const seoScore = Math.min(100, Math.max(0, 
        (keywordDensity * 20) + // Keyword density factor
        (wordCount > 800 ? 30 : wordCount / 800 * 30) + // Length factor
        (article.includes('#') ? 20 : 0) + // Structure factor
        (article.length > 1000 ? 30 : article.length / 1000 * 30) // Content depth
      ));
      
      const readabilityScore = Math.min(100, Math.max(0,
        (article.split('.').length > 10 ? 30 : 20) + // Sentence structure
        (article.includes('- ') || article.includes('1. ') ? 25 : 15) + // Lists and formatting
        (wordCount > 500 && wordCount < 3000 ? 25 : 15) + // Optimal length
        (article.includes('## ') ? 20 : 10) // Clear sections
      ));
      
      const overallScore = Math.round((seoScore + readabilityScore) / 2);
      
      return {
        overallScore,
        seoScore: Math.round(seoScore),
        readabilityScore: Math.round(readabilityScore),
        keywordDensity: Math.round(keywordDensity * 100) / 100,
        wordCount
      };
    } catch (error) {
      console.error('Error calculating quality metrics:', error);
      return {
        overallScore: 75,
        seoScore: 75,
        readabilityScore: 75,
        keywordDensity: 2.5,
        wordCount: article.split(/\s+/).length
      };
    }
  }

  calculateKeywordDensity(text, keyword) {
    const words = text.toLowerCase().split(/\s+/);
    const keywordCount = words.filter(word => 
      word.includes(keyword.toLowerCase()) || 
      keyword.toLowerCase().includes(word)
    ).length;
    
    return (keywordCount / words.length) * 100;
  }

  async extractKeywordFromTitle(title) {
    try {
      // Simple keyword extraction logic - get the main topic from title
      // Remove common words and get the core keyword
      const stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'how', 'what', 'why', 'when', 'where', 'guide', 'tips', 'methods', 'complete', 'ultimate', 'best', 'top'];
      
      const words = title.toLowerCase()
        .replace(/[^\w\s]/g, '') // Remove punctuation
        .split(/\s+/)
        .filter(word => word.length > 2 && !stopWords.includes(word));
      
      // Take the first significant word as the primary keyword
      if (words.length > 0) {
        return words[0];
      }

      // If no significant words found, throw error
      throw new Error(`Unable to extract meaningful keyword from title: "${title}"`);
    } catch (error) {
      console.error('Error extracting keyword from title:', error);
      throw new Error(`Failed to extract keyword from title: ${error.message}`);
    }
  }

  // Standard generation methods
  async generateFromKeywords(keywords, settings = {}) {
    try {
      await this.initialize();
      
      const prompt = await this.getPromptTemplate('CLUSTERED_TOPICS', {
        keywords: Array.isArray(keywords) ? keywords.join(', ') : keywords
      });

      const result = await this.generateWithPrompt(prompt, 'keyword_research');
      return result;
    } catch (error) {
      console.error('Error generating from keywords:', error);
      throw error;
    }
  }

  async generateClusteredTopics(keywords, redditInsights = null) {
    try {
      await this.initialize();
      
      // Use CLUSTERED_TOPICS prompt for single article task topic generation
      const prompt = await this.getPromptTemplate('CLUSTERED_TOPICS', {
        keywords: Array.isArray(keywords) ? keywords.join(', ') : keywords
      });

      // Get active service
      const activeModel = await this.getActiveModel();
      const service = this.services[activeModel.model_name];
      
      if (!service) {
        throw new Error(`Service for model ${activeModel.model_name} not found`);
      }

      // Use generateCustomTopics which handles clustered response parsing
      const result = await service.generateCustomTopics(prompt, keywords);
      return result;
    } catch (error) {
      console.error('Error generating clustered topics:', error);
      throw error;
    }
  }

  async generateArticle(articleDataOrTitle, keyword = null, outline = null, settings = {}) {
    try {
      await this.initialize();
      
      // Handle both old signature (title, keyword, outline, settings) and new signature (articleData)
      if (typeof articleDataOrTitle === 'object' && articleDataOrTitle !== null) {
        // New signature: generateArticle(articleData)
        // This is used by single article tasks - use SINGLE_ARTICLE_TASK prompt
        // Don't pass article_inputs here - let it remain as {article_inputs} for later replacement
        const prompt = await this.getPromptTemplate('SINGLE_ARTICLE_TASK', {});

        // Get active service
        const activeModel = await this.getActiveModel();
        const service = this.services[activeModel.model_name];
        
        if (!service) {
          throw new Error(`Service for model ${activeModel.model_name} not found`);
        }

        // Use existing generateArticleWithCustomPrompt method
        const result = await service.generateArticleWithCustomPrompt(articleDataOrTitle, prompt);
        return result;
      } else {
        // Old signature: generateArticle(title, keyword, outline, settings)
        // This is used by legacy code - use ARTICLE_GENERATION prompt
        const prompt = await this.getPromptTemplate('ARTICLE_GENERATION', {
          title: articleDataOrTitle,
          keyword,
          targetAudience: settings.targetAudience || 'general readers',
          tonality: settings.tonality || 'informative',
          length: settings.length || 'medium_article',
          outline,
          resources: settings.resources || '',
          wordCountTarget: this.getWordCountTarget(settings.length),
          authorName: settings.authorName || '',
          authorBio: settings.authorBio || ''
        });

        const result = await this.generateWithPrompt(prompt, 'article_generation');
        return result;
      }
    } catch (error) {
      console.error('Error generating article:', error);
      throw error;
    }
  }

  getWordCountTarget(length) {
    const targets = {
      'short_article': '800-1200',
      'medium_article': '1200-2000', 
      'long_article': '2000+'
    };
    return targets[length] || targets['medium_article'];
  }
}

module.exports = new AIServiceManager();