const database = require('../config/database');

class AuditLogger {
  // Define action types
  static ACTIONS = {
    // User actions
    USER_LOGIN: 'USER_LOGIN',
    USER_LOGOUT: 'USER_LOGOUT',
    USER_REGISTER: 'USER_REGISTER',
    USER_UPDATE: 'USER_UPDATE',
    USER_DELETE: 'USER_DELETE',
    USER_PASSWORD_RESET: 'USER_PASSWORD_RESET',
    USER_ROLE_CHANGE: 'USER_ROLE_CHANGE',
    
    // Admin actions
    ADMIN_USER_UPDATE: 'ADMIN_USER_UPDATE',
    ADMIN_USER_DELETE: 'ADMIN_USER_DELETE',
    ADMIN_PASSWORD_RESET: 'ADMIN_PASSWORD_RESET',
    ADMIN_MODEL_UPDATE: 'ADMIN_MODEL_UPDATE',
    ADMIN_PROMPT_UPDATE: 'ADMIN_PROMPT_UPDATE',
    ADMIN_BLOG_CREATE: 'ADMIN_BLOG_CREATE',
    ADMIN_BLOG_UPDATE: 'ADMIN_BLOG_UPDATE',
    ADMIN_BLOG_DELETE: 'ADMIN_BLOG_DELETE',
    
    // Task actions
    TASK_CREATE: 'TASK_CREATE',
    TASK_UPDATE: 'TASK_UPDATE',
    TASK_DELETE: 'TASK_DELETE',
    TASK_COMPLETE: 'TASK_COMPLETE',
    
    // Article generation
    ARTICLE_GENERATE: 'ARTICLE_GENERATE',
    ARTICLE_EXPORT: 'ARTICLE_EXPORT',
    
    // Subscription actions
    SUBSCRIPTION_CREATE: 'SUBSCRIPTION_CREATE',
    SUBSCRIPTION_UPDATE: 'SUBSCRIPTION_UPDATE',
    SUBSCRIPTION_CANCEL: 'SUBSCRIPTION_CANCEL',
    PAYMENT_RECEIVED: 'PAYMENT_RECEIVED',
    PAYMENT_FAILED: 'PAYMENT_FAILED'
  };

  static async log({
    userId,
    userEmail,
    userRole,
    action,
    entityType,
    entityId = null,
    entityDescription = null,
    changes = null,
    ipAddress = null,
    userAgent = null
  }) {
    try {
      // Validate required fields
      if (!userId || !userEmail || !action || !entityType) {
        console.error('Missing required fields for audit log');
        return;
      }

      // Convert changes object to JSON string
      const changesJson = changes ? JSON.stringify(changes) : null;

      await database.run(`
        INSERT INTO audit_logs (
          user_id, user_email, user_role, action, entity_type,
          entity_id, entity_description, changes, ip_address, user_agent
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        userId,
        userEmail,
        userRole,
        action,
        entityType,
        entityId,
        entityDescription,
        changesJson,
        ipAddress,
        userAgent
      ]);
    } catch (error) {
      // Don't throw error to prevent disrupting main flow
      console.error('Failed to create audit log:', error);
    }
  }

  static async getUserActivity(userId, limit = 50) {
    try {
      return await database.all(`
        SELECT * FROM audit_logs 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT ?
      `, [userId, limit]);
    } catch (error) {
      console.error('Failed to fetch user activity:', error);
      return [];
    }
  }

  static async getEntityHistory(entityType, entityId, limit = 50) {
    try {
      return await database.all(`
        SELECT * FROM audit_logs 
        WHERE entity_type = ? AND entity_id = ? 
        ORDER BY created_at DESC 
        LIMIT ?
      `, [entityType, entityId, limit]);
    } catch (error) {
      console.error('Failed to fetch entity history:', error);
      return [];
    }
  }

  static async getRecentAdminActions(limit = 100) {
    try {
      return await database.all(`
        SELECT * FROM audit_logs 
        WHERE action LIKE 'ADMIN_%' 
        ORDER BY created_at DESC 
        LIMIT ?
      `, [limit]);
    } catch (error) {
      console.error('Failed to fetch admin actions:', error);
      return [];
    }
  }

  static async getActionsByType(actionType, days = 30, limit = 1000) {
    try {
      return await database.all(`
        SELECT * FROM audit_logs 
        WHERE action = ? 
          AND created_at > datetime('now', '-${days} days')
        ORDER BY created_at DESC 
        LIMIT ?
      `, [actionType, limit]);
    } catch (error) {
      console.error('Failed to fetch actions by type:', error);
      return [];
    }
  }

  static async getAuditSummary(days = 30) {
    try {
      const summary = await database.all(`
        SELECT 
          action,
          COUNT(*) as count,
          COUNT(DISTINCT user_id) as unique_users
        FROM audit_logs 
        WHERE created_at > datetime('now', '-${days} days')
        GROUP BY action
        ORDER BY count DESC
      `);

      const total = await database.get(`
        SELECT COUNT(*) as total_actions
        FROM audit_logs 
        WHERE created_at > datetime('now', '-${days} days')
      `);

      return {
        total_actions: total.total_actions,
        by_action: summary
      };
    } catch (error) {
      console.error('Failed to fetch audit summary:', error);
      return { total_actions: 0, by_action: [] };
    }
  }

  // Helper method to extract request info
  static extractRequestInfo(req) {
    return {
      ipAddress: req.ip || req.connection.remoteAddress || 'unknown',
      userAgent: req.headers['user-agent'] || 'unknown'
    };
  }

  // Helper to format changes for logging
  static formatChanges(oldValues, newValues) {
    const changes = {};
    
    if (oldValues && newValues) {
      Object.keys(newValues).forEach(key => {
        if (oldValues[key] !== newValues[key]) {
          changes[key] = {
            old: oldValues[key],
            new: newValues[key]
          };
        }
      });
    }
    
    return Object.keys(changes).length > 0 ? changes : null;
  }
}

module.exports = AuditLogger;