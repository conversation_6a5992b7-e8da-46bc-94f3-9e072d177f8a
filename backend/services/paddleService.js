const { Paddle } = require('@paddle/paddle-node-sdk');
const database = require('../config/database');

class PaddleService {
  constructor() {
    this.paddle = null;
    this.initialize();
  }

  initialize() {
    if (!process.env.PADDLE_API_KEY) {
      console.warn('⚠️  PADDLE_API_KEY not configured. Paddle integration disabled.');
      return;
    }

    try {
      this.paddle = new Paddle(process.env.PADDLE_API_KEY, {
        environment: process.env.PADDLE_ENVIRONMENT || 'sandbox' // 'sandbox' or 'production'
      });
      console.log('✅ Paddle service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize Paddle service:', error.message);
    }
  }

  isConfigured() {
    return this.paddle !== null;
  }

  /**
   * Create a Paddle customer
   */
  async createCustomer(userData) {
    if (!this.isConfigured()) {
      throw new Error('Paddle service not configured');
    }

    try {
      const customerData = {
        name: userData.full_name || userData.email.split('@')[0],
        email: userData.email,
        // Add any additional customer fields as needed
      };

      const customer = await this.paddle.customers.create(customerData);
      
      console.log('✅ Paddle customer created:', customer.id);
      return customer;
    } catch (error) {
      console.error('❌ Failed to create Paddle customer:', error.message);
      throw error;
    }
  }

  /**
   * Create a subscription checkout URL with trial period
   */
  async createSubscriptionCheckout(userId, priceId, customData = {}) {
    if (!this.isConfigured()) {
      throw new Error('Paddle service not configured');
    }

    try {
      // Get user data
      const user = await database.get('SELECT * FROM users WHERE id = ?', [userId]);
      if (!user) {
        throw new Error('User not found');
      }

      // Check if user already has an active subscription
      if (user.subscription_status === 'active' || user.subscription_status === 'trialing') {
        throw new Error('User already has an active subscription');
      }

      // Ensure customer exists in Paddle
      let paddleCustomerId = user.paddle_customer_id;
      if (!paddleCustomerId) {
        const customer = await this.createCustomer(user);
        paddleCustomerId = customer.id;
        
        // Update user with Paddle customer ID
        await database.run(
          'UPDATE users SET paddle_customer_id = ? WHERE id = ?',
          [paddleCustomerId, userId]
        );
      }

      // Create checkout session with 7-day trial
      const checkoutData = {
        items: [{ 
          price_id: priceId, 
          quantity: 1 
        }],
        customer_id: paddleCustomerId,
        return_url: `${process.env.FRONTEND_URL}/billing/success`,
        success_url: `${process.env.FRONTEND_URL}/billing/success`,
        custom_data: {
          user_id: userId.toString(),
          trial_days: 7,
          plan_type: customData.plan_type || 'PRO',
          ...customData
        },
        // 7天试用期配置
        subscription: {
          trial_period: {
            interval: 'day',
            frequency: 7
          }
        }
      };

      const checkout = await this.paddle.checkouts.create(checkoutData);
      
      console.log('✅ Checkout session created with trial:', checkout.id);
      return checkout;
    } catch (error) {
      console.error('❌ Failed to create checkout session:', error.message);
      throw error;
    }
  }

  /**
   * Get subscription details
   */
  async getSubscription(subscriptionId) {
    if (!this.isConfigured()) {
      throw new Error('Paddle service not configured');
    }

    try {
      const subscription = await this.paddle.subscriptions.get(subscriptionId);
      return subscription;
    } catch (error) {
      console.error('❌ Failed to get subscription:', error.message);
      throw error;
    }
  }

  /**
   * Smart subscription cancellation (trial vs paid)
   */
  async cancelSubscription(userId, reason = 'user_request') {
    if (!this.isConfigured()) {
      throw new Error('Paddle service not configured');
    }

    try {
      // Get user's current subscription details
      const user = await database.get(`
        SELECT paddle_subscription_id, subscription_status, trial_ends_at, 
               is_in_trial, current_period_end, plan_type
        FROM users WHERE id = ?
      `, [userId]);

      if (!user?.paddle_subscription_id) {
        throw new Error('No active subscription found');
      }

      const now = new Date();
      const isInTrial = user.is_in_trial && user.trial_ends_at && now < new Date(user.trial_ends_at);
      
      let cancelData;
      let userUpdateData;

      if (isInTrial) {
        // 试用期取消：立即终止，不收费
        cancelData = { effective_from: 'immediately' };
        userUpdateData = {
          subscription_status: 'cancelled',
          payment_status: 'cancelled',
          plan_type: 'V1_DEFAULT_ACCESS',
          cancelled_at: now.toISOString(),
          cancellation_effective_date: now.toISOString(),
          cancel_reason: reason,
          is_in_trial: false
        };
        
        console.log('🔄 Cancelling trial subscription immediately');
      } else {
        // 付费期取消：在当前计费周期结束时取消
        cancelData = { effective_from: 'next_billing_period' };
        const effectiveDate = user.current_period_end || now;
        
        userUpdateData = {
          subscription_status: 'cancelled',
          cancelled_at: now.toISOString(),
          cancellation_effective_date: new Date(effectiveDate).toISOString(),
          cancel_reason: reason
          // 注意：不立即更改plan_type和payment_status，让用户使用到期末
        };
        
        console.log('🔄 Cancelling paid subscription at period end');
      }

      // 在Paddle中取消订阅
      const result = await this.paddle.subscriptions.cancel(user.paddle_subscription_id, cancelData);

      // 更新数据库中的用户状态
      const updateFields = Object.keys(userUpdateData).map(key => `${key} = ?`).join(', ');
      const updateValues = [...Object.values(userUpdateData), userId];
      
      await database.run(
        `UPDATE users SET ${updateFields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        updateValues
      );

      // 记录订阅状态变更历史
      await this.logSubscriptionChange(userId, {
        from_status: user.subscription_status,
        to_status: 'cancelled',
        from_plan: user.plan_type,
        to_plan: isInTrial ? 'V1_DEFAULT_ACCESS' : user.plan_type,
        change_reason: isInTrial ? 'trial_cancel' : 'paid_cancel',
        change_data: JSON.stringify({ reason, effective_date: userUpdateData.cancellation_effective_date })
      });

      console.log('✅ Subscription cancelled:', user.paddle_subscription_id);
      return {
        ...result,
        is_trial_cancellation: isInTrial,
        effective_date: userUpdateData.cancellation_effective_date
      };
    } catch (error) {
      console.error('❌ Failed to cancel subscription:', error.message);
      throw error;
    }
  }

  /**
   * Update subscription
   */
  async updateSubscription(subscriptionId, updateData) {
    if (!this.isConfigured()) {
      throw new Error('Paddle service not configured');
    }

    try {
      const result = await this.paddle.subscriptions.update(subscriptionId, updateData);
      
      console.log('✅ Subscription updated:', subscriptionId);
      return result;
    } catch (error) {
      console.error('❌ Failed to update subscription:', error.message);
      throw error;
    }
  }

  /**
   * Get customer portal URL
   */
  async getCustomerPortalUrl(customerId, returnUrl = null) {
    if (!this.isConfigured()) {
      throw new Error('Paddle service not configured');
    }

    try {
      const portalData = {
        customer_id: customerId,
        return_url: returnUrl || `${process.env.FRONTEND_URL}/billing`
      };

      const portal = await this.paddle.customerPortal.generate(portalData);
      
      console.log('✅ Customer portal URL generated');
      return portal.url;
    } catch (error) {
      console.error('❌ Failed to generate customer portal URL:', error.message);
      throw error;
    }
  }

  /**
   * Verify webhook signature
   */
  verifyWebhookSignature(signature, body, secret = null) {
    const webhookSecret = secret || process.env.PADDLE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      console.warn('⚠️  PADDLE_WEBHOOK_SECRET not configured');
      return false;
    }

    try {
      const crypto = require('crypto');
      const expectedSignature = crypto
        .createHmac('sha256', webhookSecret)
        .update(body)
        .digest('hex');

      return crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(expectedSignature)
      );
    } catch (error) {
      console.error('❌ Webhook signature verification failed:', error.message);
      return false;
    }
  }

  /**
   * Process webhook event
   */
  async processWebhookEvent(eventType, eventData) {
    console.log('🔔 Processing Paddle webhook:', eventType);

    // 对于订阅相关事件，检查产品ID
    if (eventType.startsWith('subscription.') || eventType.startsWith('transaction.')) {
      const productId = eventData.subscription?.items?.[0]?.product_id || 
                       eventData.transaction?.items?.[0]?.product_id;
      
      if (productId && productId !== process.env.PADDLE_PRODUCT_ID) {
        console.log('🔕 Ignoring webhook for different product:', productId);
        return; // 忽略其他产品的webhook
      }
    }

    try {
      switch (eventType) {
        case 'subscription.created':
          await this.handleSubscriptionCreated(eventData);
          break;
        case 'subscription.activated':
          await this.handleSubscriptionActivated(eventData);
          break;
        case 'subscription.trial_ended':
          await this.handleTrialEnded(eventData);
          break;
        case 'subscription.updated':
          await this.handleSubscriptionUpdated(eventData);
          break;
        case 'subscription.cancelled':
          await this.handleSubscriptionCanceled(eventData);
          break;
        case 'subscription.expired':
          await this.handleSubscriptionExpired(eventData);
          break;
        case 'subscription.paused':
          await this.handleSubscriptionPaused(eventData);
          break;
        case 'subscription.resumed':
          await this.handleSubscriptionResumed(eventData);
          break;
        case 'transaction.completed':
          await this.handleTransactionCompleted(eventData);
          break;
        case 'transaction.payment_failed':
          await this.handlePaymentFailed(eventData);
          break;
        case 'transaction.payment_succeeded':
          await this.handlePaymentSucceeded(eventData);
          break;
        default:
          console.log('🔕 Unhandled webhook event:', eventType);
      }

      // Log the event
      await this.logWebhookEvent(eventType, eventData);
      
    } catch (error) {
      console.error('❌ Error processing webhook event:', error.message);
      throw error;
    }
  }

  async handleSubscriptionCreated(data) {
    const subscription = data.subscription;
    const customData = data.custom_data || {};
    const userId = customData.user_id;

    // 检查是否是Writer J产品
    const productId = subscription.items?.[0]?.product_id;
    if (productId !== process.env.PADDLE_PRODUCT_ID) {
      console.log('🔕 Ignoring event for different product:', productId);
      return; // 忽略其他产品的事件
    }

    if (!userId) {
      console.warn('⚠️  No user_id in subscription created event');
      return;
    }

    const now = new Date();
    const currentPeriodStart = new Date(subscription.current_billing_period?.starts_at);
    const currentPeriodEnd = new Date(subscription.current_billing_period?.ends_at);
    
    // 检查是否有试用期
    const hasTrialPeriod = subscription.status === 'trialing';
    const trialEndsAt = hasTrialPeriod ? new Date(subscription.trial_dates?.ends_at) : null;
    
    const planType = customData.plan_type === 'MAX' ? 'MAX' : 'PRO';

    await database.run(`
      UPDATE users SET 
        paddle_subscription_id = ?,
        paddle_product_id = ?,
        paddle_price_id = ?,
        subscription_status = ?,
        payment_status = ?,
        current_period_start = ?,
        current_period_end = ?,
        trial_starts_at = ?,
        trial_ends_at = ?,
        is_in_trial = ?,
        plan_type = ?,
        billing_interval = ?,
        last_paddle_event_id = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      subscription.id,
      subscription.items?.[0]?.product_id,
      subscription.items?.[0]?.price_id,
      subscription.status, // 'trialing' or 'active'
      hasTrialPeriod ? 'trial' : 'confirmed',
      currentPeriodStart.toISOString(),
      currentPeriodEnd.toISOString(),
      hasTrialPeriod ? now.toISOString() : null,
      trialEndsAt?.toISOString(),
      hasTrialPeriod,
      planType,
      subscription.billing_cycle?.interval || 'month',
      data.event_id || null,
      userId
    ]);

    // 记录订阅历史
    await this.logSubscriptionChange(userId, {
      from_status: 'free',
      to_status: subscription.status,
      from_plan: 'V1_DEFAULT_ACCESS',
      to_plan: planType,
      change_reason: hasTrialPeriod ? 'trial_start' : 'subscription_start',
      change_data: JSON.stringify({ trial_days: customData.trial_days })
    });

    console.log(`✅ Subscription created for user ${userId}: ${subscription.status} (trial: ${hasTrialPeriod})`);
  }

  async handleSubscriptionActivated(data) {
    const subscription = data.subscription;
    
    await database.run(`
      UPDATE users SET 
        subscription_status = 'active',
        payment_status = 'confirmed',
        updated_at = CURRENT_TIMESTAMP
      WHERE paddle_subscription_id = ?
    `, [subscription.id]);

    console.log('✅ Subscription activated:', subscription.id);
  }

  async handleTrialEnded(data) {
    const subscription = data.subscription;
    
    // 获取用户信息
    const user = await database.get('SELECT id, plan_type FROM users WHERE paddle_subscription_id = ?', [subscription.id]);
    
    if (subscription.status === 'active') {
      // 试用期结束，转为付费用户
      await database.run(`
        UPDATE users SET 
          subscription_status = 'active',
          payment_status = 'confirmed',
          is_in_trial = FALSE,
          updated_at = CURRENT_TIMESTAMP
        WHERE paddle_subscription_id = ?
      `, [subscription.id]);

      // 记录试用期转换
      await this.logSubscriptionChange(user.id, {
        from_status: 'trialing',
        to_status: 'active',
        from_plan: user.plan_type,
        to_plan: user.plan_type,
        change_reason: 'trial_conversion',
        change_data: JSON.stringify({ converted_at: new Date() })
      });

      console.log('✅ Trial converted to paid subscription:', subscription.id);
    } else {
      // 试用期结束，用户未付费
      await database.run(`
        UPDATE users SET 
          subscription_status = 'expired',
          payment_status = 'free',
          plan_type = 'V1_DEFAULT_ACCESS',
          is_in_trial = FALSE,
          updated_at = CURRENT_TIMESTAMP
        WHERE paddle_subscription_id = ?
      `, [subscription.id]);

      // 记录试用期过期
      await this.logSubscriptionChange(user.id, {
        from_status: 'trialing',
        to_status: 'expired',
        from_plan: user.plan_type,
        to_plan: 'V1_DEFAULT_ACCESS',
        change_reason: 'trial_expired',
        change_data: JSON.stringify({ expired_at: new Date() })
      });

      console.log('✅ Trial expired without conversion:', subscription.id);
    }
  }

  async handleSubscriptionUpdated(data) {
    const subscription = data.subscription;
    
    await database.run(`
      UPDATE users SET 
        subscription_status = ?,
        subscription_current_period_start = ?,
        subscription_current_period_end = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE paddle_subscription_id = ?
    `, [
      subscription.status,
      new Date(subscription.current_billing_period.starts_at),
      new Date(subscription.current_billing_period.ends_at),
      subscription.id
    ]);

    console.log('✅ Subscription updated:', subscription.id);
  }

  async handleSubscriptionCanceled(data) {
    const subscription = data.subscription;
    
    await database.run(`
      UPDATE users SET 
        subscription_status = 'canceled',
        plan_type = 'V1_DEFAULT_ACCESS',
        updated_at = CURRENT_TIMESTAMP
      WHERE paddle_subscription_id = ?
    `, [subscription.id]);

    console.log('✅ Subscription canceled:', subscription.id);
  }

  async handleSubscriptionPaused(data) {
    const subscription = data.subscription;
    
    await database.run(`
      UPDATE users SET 
        subscription_status = 'paused',
        updated_at = CURRENT_TIMESTAMP
      WHERE paddle_subscription_id = ?
    `, [subscription.id]);

    console.log('✅ Subscription paused:', subscription.id);
  }

  async handleSubscriptionResumed(data) {
    const subscription = data.subscription;
    
    await database.run(`
      UPDATE users SET 
        subscription_status = 'active',
        updated_at = CURRENT_TIMESTAMP
      WHERE paddle_subscription_id = ?
    `, [subscription.id]);

    console.log('✅ Subscription resumed:', subscription.id);
  }

  async handleTransactionCompleted(data) {
    const transaction = data.transaction;
    console.log('✅ Payment completed:', transaction.id);
    // Additional payment processing logic can be added here
  }

  async handlePaymentFailed(data) {
    const transaction = data.transaction;
    
    // Update subscription status to past_due if payment failed
    if (transaction.subscription_id) {
      await database.run(`
        UPDATE users SET 
          subscription_status = 'past_due',
          updated_at = CURRENT_TIMESTAMP
        WHERE paddle_subscription_id = ?
      `, [transaction.subscription_id]);
    }

    console.log('❌ Payment failed:', transaction.id);
  }

  async handlePaymentSucceeded(data) {
    const transaction = data.transaction;
    
    // 记录支付成功
    await this.recordPayment({
      user_id: data.custom_data?.user_id,
      subscription_id: transaction.subscription_id,
      paddle_transaction_id: transaction.id,
      paddle_invoice_id: transaction.invoice_id,
      paddle_receipt_url: transaction.receipt_url,
      amount: transaction.details?.totals?.grand_total,
      currency: transaction.currency_code,
      status: 'completed',
      period_start: transaction.billing_period?.starts_at,
      period_end: transaction.billing_period?.ends_at,
      payment_type: transaction.billing_period ? 'recurring' : 'one_time',
      tax_amount: transaction.details?.totals?.tax,
      net_amount: transaction.details?.totals?.subtotal,
      paddle_event_data: JSON.stringify(data)
    });

    console.log('✅ Payment succeeded:', transaction.id);
  }

  async recordPayment(paymentData) {
    try {
      await database.run(`
        INSERT INTO payments (
          user_id, subscription_id, paddle_transaction_id, paddle_invoice_id,
          paddle_receipt_url, amount, currency, status, period_start, period_end,
          payment_type, tax_amount, net_amount, paddle_event_data
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        paymentData.user_id,
        paymentData.subscription_id,
        paymentData.paddle_transaction_id,
        paymentData.paddle_invoice_id,
        paymentData.paddle_receipt_url,
        paymentData.amount,
        paymentData.currency,
        paymentData.status,
        paymentData.period_start,
        paymentData.period_end,
        paymentData.payment_type,
        paymentData.tax_amount,
        paymentData.net_amount,
        paymentData.paddle_event_data
      ]);
    } catch (error) {
      console.error('❌ Failed to record payment:', error.message);
    }
  }

  async logSubscriptionChange(userId, changeData) {
    try {
      await database.run(`
        INSERT INTO subscription_history (
          user_id, paddle_subscription_id, from_status, to_status,
          from_plan, to_plan, change_reason, change_data, effective_date
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        userId,
        changeData.paddle_subscription_id || null,
        changeData.from_status,
        changeData.to_status,
        changeData.from_plan,
        changeData.to_plan,
        changeData.change_reason,
        changeData.change_data,
        changeData.effective_date || new Date().toISOString()
      ]);
    } catch (error) {
      console.error('❌ Failed to log subscription change:', error.message);
    }
  }

  async logWebhookEvent(eventType, eventData) {
    try {
      await database.run(`
        INSERT INTO subscription_events (
          paddle_event_id, event_type, subscription_id, 
          customer_id, event_data, processed
        ) VALUES (?, ?, ?, ?, ?, ?)
      `, [
        eventData.event_id || Date.now().toString(),
        eventType,
        eventData.subscription?.id || null,
        eventData.customer?.id || null,
        JSON.stringify(eventData),
        true
      ]);
    } catch (error) {
      console.error('❌ Failed to log webhook event:', error.message);
    }
  }
}

module.exports = new PaddleService();