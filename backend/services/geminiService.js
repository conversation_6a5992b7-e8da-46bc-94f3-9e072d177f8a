const database = require('../config/database');

class GeminiService {
  constructor() {
    this.ai = null;
    this.initialized = false;
    this.currentApiKey = null;

    console.log('=== GEMINI SERVICE DEBUG ===');
    console.log('Gemini service initialized - will load API key from database');
    console.log('============================');
  }

  async getModelFromDatabase() {
    try {
      const model = await database.get(
        'SELECT api_key, model_version FROM ai_models WHERE model_name = $1 AND is_active = $2',
        ['gemini', true]
      );
      return model ? { 
        api_key: model.api_key, 
        model_version: model.model_version
      } : null;
    } catch (error) {
      console.error('Error getting Gemini model from database:', error);
      return null;
    }
  }

  // Keep legacy method for compatibility
  async getApiKeyFromDatabase() {
    const model = await this.getModelFromDatabase();
    return model ? model.api_key : null;
  }

  async initializeAI(apiKey = null) {
    try {
      // Get API key from database if not provided
      const dbApiKey = apiKey || await this.getApiKeyFromDatabase();

      if (!dbApiKey) {
        console.error('No Gemini API key found in database');
        this.initialized = false;
        return;
      }

      // Only reinitialize if API key changed
      if (this.currentApiKey === dbApiKey && this.ai) {
        this.initialized = true;
        return;
      }

      const { GoogleGenAI } = await import('@google/genai');
      
      // Configure proxy only in development environment
      const genaiConfig = {
        apiKey: dbApiKey,
      };
      
      
      this.ai = new GoogleGenAI(genaiConfig);
      this.currentApiKey = dbApiKey;
      this.initialized = true;
      console.log('Google GenAI initialized successfully with database API key');
    } catch (error) {
      console.error('Failed to initialize Google GenAI:', error);
      this.initialized = false;
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initializeAI();
    }
  }

  async generateCustomTopics(prompt, keywords) {
    await this.ensureInitialized();

    if (!this.ai) {
      throw new Error('Google GenAI not initialized. Please check your API key and network connection.');
    }

    try {
      // Get model version from database
      const modelInfo = await this.getModelFromDatabase();
      if (!modelInfo || !modelInfo.model_version) {
        throw new Error('No active Gemini model found in database. Please configure via admin panel.');
      }
      const modelVersion = modelInfo.model_version;

      const config = {
        responseMimeType: 'text/plain',
      };
      const model = modelVersion;
      const contents = [
        {
          role: 'user',
          parts: [
            {
              text: prompt,
            },
          ],
        },
      ];

      console.log('🔄 Attempting to generate content...');
      const response = await this.ai.models.generateContent({
        model,
        config,
        contents,
      });
      const text = response.text;

      // Parse the clustered Markdown response
      const lines = text.split('\n').filter(line => line.trim());
      let clusteredTopics = {};
      let allTopics = [];
      let currentCluster = null;

      for (const line of lines) {
        const trimmedLine = line.trim();

        // Check for H3 headers (cluster names) - be more flexible
        if (trimmedLine.startsWith('### ') || trimmedLine.startsWith('## ') || trimmedLine.match(/^#{1,3}\s+/)) {
          currentCluster = trimmedLine.replace(/^#{1,3}\s+/, '').trim();
          clusteredTopics[currentCluster] = [];
        }
        // Check for numbered list items
        else if (/^\d+\./.test(trimmedLine) && currentCluster) {
          const topic = trimmedLine.replace(/^\d+\.\s*/, '').trim();
          if (topic.length > 0) {
            clusteredTopics[currentCluster].push(topic);
            allTopics.push(topic);
          }
        }
        // Also check for bullet points as fallback
        else if (/^[-*]\s+/.test(trimmedLine) && currentCluster) {
          const topic = trimmedLine.replace(/^[-*]\s+/, '').trim();
          if (topic.length > 0) {
            clusteredTopics[currentCluster].push(topic);
            allTopics.push(topic);
          }
        }
      }

      // If no clusters found, fall back to simple numbered list parsing
      if (Object.keys(clusteredTopics).length === 0) {
        allTopics = lines
          .filter(line => /^\d+\./.test(line.trim()))
          .map(line => line.replace(/^\d+\.\s*/, '').trim())
          .filter(topic => topic.length > 0);
      }

      return {
        suggestions: allTopics,
        clustered: clusteredTopics,
        generatedFromKeywords: keywords,
        totalCount: allTopics.length,
        hasClusters: Object.keys(clusteredTopics).length > 0,
        rawResponse: text // Include raw response for debugging
      };
    } catch (error) {
      console.error('Error generating custom topics:', error);
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
      throw new Error(`Failed to generate custom topics: ${error.message}`);
    }
  }


  async generateArticleWithCustomPrompt(articleData, promptTemplate) {
    await this.ensureInitialized();

    if (!this.ai) {
      throw new Error('Google GenAI not initialized. Please check your API key and network connection.');
    }

    try {
      const {
        topics, // EXPECTS AN ARRAY of 1 to 5 strings
        sources,
        productInfo,
        tonality,
        length,
        format,
        authorName,
        authorBio,
        targetAudience,
        articleGoal,
        primaryKeywords,
        secondaryKeywords,
        rawRedditData,
        comprehensiveResources,
        taskId, // Add taskId for debug logging
      } = articleData;

      // Input Validation (Basic)
      if (!Array.isArray(topics) || topics.length === 0 || topics.length > 5) {
        throw new Error("Invalid input: 'topics' must be an array containing 1 to 5 topic strings.");
      }

      const wordCount = this.getWordCountFromLength(length);

      // --- Input Formatting for the Prompt ---
      const topicsText = `
### Core Topics to Synthesize (1 to ${topics.length}):
${topics.map((t, index) => `${index + 1}. ${t}`).join('\n')}`;


      const keywordsText = `
### Provided Keywords (Use these & derive others):
- Primary: ${primaryKeywords && primaryKeywords.length > 0 ? primaryKeywords.join(', ') : 'N/A'}
- Secondary/LSI: ${secondaryKeywords && secondaryKeywords.length > 0 ? secondaryKeywords.join(', ') : 'N/A'}`;

      const sourcesText = sources && sources.length > 0
        ? `\n\n### Reference Sources (Incorporate these facts/ideas):\n${sources.map((source, index) =>
          `${index + 1}. ${source.title || 'Source'}: ${source.content || source.url}`
        ).join('\n')}`
        : '';

      const productText = productInfo && productInfo.name
        ? `\n\n### Product Information (Integrate subtly as a solution/example):\n- Product: ${productInfo.name}\n- Description: ${productInfo.description}\n- Features: ${productInfo.features ? productInfo.features.join(', ') : 'N/A'}\n- Link: ${productInfo.link || 'N/A'}\n- **CRITICAL: Mention the product a maximum of 1-2 times.** If mentioned twice, the mentions should feel distinct and add unique value in each context.`
        : '';

      const eeatText = `
### E-E-A-T & Goal Focus:
- Author: ${authorName || 'N/A'}
- Expertise: ${authorBio || 'N/A'}
- Target Audience: ${targetAudience || 'General audience'}
- Article Goal: ${articleGoal || 'Educate, inform, and build trust'}`;

      // Format raw Reddit data if available for AI analysis
      const rawRedditText = rawRedditData && rawRedditData.length > 0 ? `

### Raw Reddit Data for AI Analysis:
${rawRedditData.map((post, index) => `
**Reddit Post ${index + 1}:**
- **Subreddit**: r/${post.subreddit}
- **Title**: "${post.title || 'No title'}"
- **Content**: ${post.content && post.content.trim() ? `"${post.content}"` : 'No text content (likely link post)'}
- **Author**: u/${post.author || 'unknown'}
- **Upvotes**: ${post.upvotes} | **Comments**: ${post.comments}
- **Engagement Score**: ${(post.upvotes || 0) + (post.comments || 0) * 2}
- **URL**: ${post.url}
${post.flair ? `- **Flair**: ${post.flair}` : ''}
`).join('\n')}

**AI ANALYSIS INSTRUCTIONS:** 
Analyze the Reddit posts above to extract:
1. **Real User Pain Points**: What problems and frustrations do users express?
2. **Trending Topics**: What themes and discussions are most common?
3. **User Language**: How do real users talk about these topics?
4. **Solution Gaps**: What solutions are users seeking but not finding?
5. **Community Insights**: What unique perspectives emerge from these communities?

Use these insights naturally throughout your article to make it more authentic and user-focused. Reference real user concerns and validate problems with actual community discussions.` : '';

      // Format comprehensive resources if available
      const comprehensiveResourcesText = comprehensiveResources && comprehensiveResources.length > 0 ? `

### Comprehensive Resources (Evaluate for Quality & Relevance):
${comprehensiveResources.map((resource, index) => {
  if (resource.type === 'url') {
    return `${index + 1}. **URL Resource**: ${resource.title}
   - URL: ${resource.url}
   - Description: ${resource.description || 'N/A'}
   - Content Preview: ${resource.content || ''}
   - Word Count: ${resource.wordCount} words`;
  } else if (resource.type === 'text') {
    return `${index + 1}. **Text Block**: ${resource.title}
   - Content: ${resource.content}
   - Word Count: ${resource.wordCount} words`;
  }
  return `${index + 1}. **Resource**: ${resource.title || 'Untitled'}`;
}).join('\n\n')}

**INTEGRATION INSTRUCTIONS:** Evaluate each resource above for:
1. **Relevance**: Does it support your central thesis? (High/Medium/Low)
2. **Quality**: Is the information credible and valuable? (High/Medium/Low) 
3. **Fit**: Can it be integrated naturally without forcing? (Good Fit/Forced/Poor Fit)

**ONLY USE** resources that score High/Medium relevance + High/Medium quality + Good Fit. Integrate them seamlessly as supporting evidence, expert insights, or validation for your arguments. Do not force integration if resources don't naturally enhance your narrative.` : '';

      // Build the article inputs section
      const articleInputs = `
### Key Concepts & Angles to Cover:
${topicsText}

${keywordsText}

### Article Properties:
- Target Word Count: ~${wordCount} words
- Tone: ${tonality}
- Format: ${format} (Use Markdown if 'markdown')
${eeatText}
${sourcesText}
${productText}
${rawRedditText}
${comprehensiveResourcesText}`;

      // Replace the placeholder in the prompt template
      const finalPrompt = promptTemplate.replace('{article_inputs}', articleInputs);

      // Step 7: ARTICLE GENERATION - Log complete prompt and save to database
      console.log('\n' + '='.repeat(80));
      console.log('🎯 STEP 7: FINAL ARTICLE GENERATION');
      console.log('='.repeat(80));
      console.log('📋 COMPLETE PROMPT SENT TO GEMINI:');
      console.log('-'.repeat(80));
      console.log(finalPrompt);
      console.log('-'.repeat(80));
      console.log('='.repeat(80));

      // Save debug prompt to database if taskId is provided
      if (taskId) {
        try {
          const database = require('../config/database');
          await database.run(
            'UPDATE tasks SET debug_prompt = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [finalPrompt, taskId]
          );
          console.log('✅ Debug prompt saved to database for task:', taskId);
        } catch (dbError) {
          console.error('⚠️ Failed to save debug prompt to database:', dbError.message);
        }
      }

      // Get model version from database
      const modelInfo = await this.getModelFromDatabase();
      if (!modelInfo || !modelInfo.model_version) {
        throw new Error('No active Gemini model found in database. Please configure via admin panel.');
      }
      const modelVersion = modelInfo.model_version;

      const config = {
        responseMimeType: 'text/plain',
      };
      const model = modelVersion;
      const contents = [
        {
          role: 'user',
          parts: [
            {
              text: finalPrompt,
            },
          ],
        },
      ];

      const response = await this.ai.models.generateContent({
        model,
        config,
        contents,
      });
      return response.text;
    } catch (error) {
      console.error('Error generating article with custom prompt:', error);
      throw new Error(`Failed to generate article: ${error.message}`);
    }
  }



  getWordCountFromLength(length) {
    const lengthMap = {
      'snippet': 500,
      'short_post': 800,       // Corresponds to 'Short Post'
      'medium_article': 1500,  // Corresponds to 'Medium Article'
      'long_guide': 2500,      // Corresponds to 'Long-Form Guide'
      'pillar_module': 700     // Corresponds to 'Pillar Page Module'
    };

    // More robust default value handling
    const selectedKey = typeof length === 'string' ? length.toLowerCase() : 'medium_article'; // Default to 'medium_article' if input is invalid or undefined


    return lengthMap[selectedKey] || 1500; // Also return a sensible default if the key isn't found in the map (though it shouldn't happen with the above default)
  }

  // Legacy method removed - AI now analyzes raw Reddit data directly

  extractListFromText(text, type) {
    const lines = text.split('\n');
    const items = [];

    for (const line of lines) {
      if (line.trim() && (line.includes('?') || line.includes(type))) {
        const cleaned = line.replace(/^\d+\.?\s*/, '').replace(/^[-*]\s*/, '').trim();
        if (cleaned && !items.includes(cleaned)) {
          items.push(cleaned);
        }
      }
    }

    return items.slice(0, 5);
  }

  /**
   * Simple generateContent method for AI Service Manager compatibility
   * This is the method called by aiServiceManager.generateWithPrompt()
   */
  async generateContent(prompt) {
    try {
      console.log(`🔥 GEMINI generateContent called`);
      console.log(`📝 Prompt length: ${prompt.length} characters`);
      console.log(`📋 Prompt preview: ${prompt.substring(0, 300)}...`);
      
      await this.initializeAI();
      
      if (!this.ai || !this.initialized) {
        throw new Error('Gemini AI not properly initialized. Check API key configuration.');
      }

      // Get model version from database
      const modelInfo = await this.getModelFromDatabase();
      if (!modelInfo || !modelInfo.model_version) {
        throw new Error('No active Gemini model found in database. Please configure via admin panel.');
      }
      const modelVersion = modelInfo.model_version;

      const config = {
        responseMimeType: 'text/plain',
      };

      const contents = [
        {
          role: 'user',
          parts: [
            {
              text: prompt,
            },
          ],
        },
      ];

      console.log(`🤖 Calling Gemini API with model: ${modelVersion}`);
      const response = await this.ai.models.generateContent({
        model: modelVersion,
        config,
        contents,
      });

      const result = response.text;
      console.log(`✅ Gemini response received: ${result.length} characters`);
      console.log(`📄 Response preview: ${result.substring(0, 300)}...`);
      
      return result;
      
    } catch (error) {
      console.error(`💥 Error in Gemini generateContent:`, error);
      console.error(`   - Error type: ${error.constructor.name}`);
      console.error(`   - Error message: ${error.message}`);
      if (error.response) {
        console.error(`   - API Response status: ${error.response.status}`);
        console.error(`   - API Response data:`, error.response.data);
      }
      throw new Error(`Gemini AI generation failed: ${error.message}`);
    }
  }
}


module.exports = new GeminiService();
