const fetch = require('node-fetch');

class SerperService {
  constructor() {
    this.apiKey = process.env.SERPER_API_KEY;
    this.baseUrl = 'https://google.serper.dev';

    if (!this.apiKey) {
      console.warn('SERPER_API_KEY not found. Serper functionality will be limited.');
    }
  }

  async searchGoogle(query, options = {}) {
    if (!this.apiKey) {
      throw new Error('Serper API key not configured');
    }

    try {
      const requestData = {
        q: query,
        ...options
      };

      const response = await fetch(`${this.baseUrl}/search`, {
        method: 'POST',
        headers: {
          'X-API-KEY': this.apiKey,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`Serper API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error calling Serper API:', error);
      throw error;
    }
  }

  async getKeywordResearch(keyword) {
    try {
      // Get comprehensive search results with more pages and results
      const [page1Data, page2Data] = await Promise.all([
        this.searchGoogle(keyword, { num: 20, page: 1 }),
        this.searchGoogle(keyword, { num: 20, page: 2 })
      ]);

      // Combine data from both pages for more comprehensive word cloud
      const combinedData = this.combineSearchData(page1Data, page2Data);

      // Extract and process the data
      const result = {
        keyword: keyword,
        relatedKeywords: this.extractRelatedKeywords(page1Data), // Use page 1 for related keywords
        peopleAlsoAsk: this.extractPeopleAlsoAsk(page1Data), // Use page 1 for PAA
        wordCloudData: this.extractWordCloudData(combinedData), // Use combined data for comprehensive word cloud
        knowledgeGraph: this.extractKnowledgeGraph(page1Data), // Use page 1 for knowledge graph
        organicResults: this.extractOrganicResults(page1Data) // Use page 1 for organic results
      };

      return result;
    } catch (error) {
      console.error('Error in keyword research:', error);

      // Return fallback data
      return {
        keyword: keyword,
        relatedKeywords: this.generateFallbackRelatedKeywords(keyword),
        peopleAlsoAsk: this.generateFallbackPeopleAlsoAsk(keyword),
        wordCloudData: this.generateFallbackWordCloud(keyword),
        knowledgeGraph: null,
        organicResults: []
      };
    }
  }

  combineSearchData(page1Data, page2Data) {
    // Combine organic results from both pages
    const combinedOrganic = [
      ...(page1Data.organic || []),
      ...(page2Data.organic || [])
    ];

    // Combine People Also Ask from both pages (if available)
    const combinedPeopleAlsoAsk = [
      ...(page1Data.peopleAlsoAsk || []),
      ...(page2Data.peopleAlsoAsk || [])
    ];

    // Use page 1 data as base and add combined results
    return {
      ...page1Data,
      organic: combinedOrganic,
      peopleAlsoAsk: combinedPeopleAlsoAsk
    };
  }

  extractRelatedKeywords(data) {
    if (!data.relatedSearches) return [];

    return data.relatedSearches.map(item => ({
      query: item.query,
      relevance: 'high' // Could be enhanced with scoring
    })).slice(0, 8); // Limit to 8 related keywords
  }

  extractPeopleAlsoAsk(data) {
    console.log('🔍 PAA Debug - Raw data.peopleAlsoAsk:', data.peopleAlsoAsk);
    console.log('🔍 PAA Debug - Full data keys:', Object.keys(data));
    
    if (!data.peopleAlsoAsk) {
      console.log('❌ PAA Debug - No peopleAlsoAsk in response');
      return [];
    }

    const result = data.peopleAlsoAsk.map(item => ({
      question: item.question,
      snippet: item.snippet,
      title: item.title,
      link: item.link
    })).slice(0, 6); // Limit to 6 questions
    
    console.log('✅ PAA Debug - Extracted PAA count:', result.length);
    return result;
  }

  extractWordCloudData(data) {
    const text = [];

    // Extract text from ALL organic results (now includes both pages)
    if (data.organic) {
      data.organic.forEach(result => {
        if (result.snippet) {
          text.push(result.snippet);
        }
        if (result.title) {
          text.push(result.title);
        }
        // Also extract from sitelinks if available
        if (result.sitelinks) {
          result.sitelinks.forEach(sitelink => {
            if (sitelink.title) text.push(sitelink.title);
            if (sitelink.snippet) text.push(sitelink.snippet);
          });
        }
      });
    }

    // Extract text from People Also Ask (now includes both pages)
    if (data.peopleAlsoAsk) {
      data.peopleAlsoAsk.forEach(item => {
        if (item.snippet) {
          text.push(item.snippet);
        }
        if (item.question) {
          text.push(item.question);
        }
        if (item.title) {
          text.push(item.title);
        }
      });
    }

    // Extract text from knowledge graph
    if (data.knowledgeGraph) {
      if (data.knowledgeGraph.description) {
        text.push(data.knowledgeGraph.description);
      }
      if (data.knowledgeGraph.title) {
        text.push(data.knowledgeGraph.title);
      }
      // Extract from attributes if available
      if (data.knowledgeGraph.attributes) {
        Object.values(data.knowledgeGraph.attributes).forEach(value => {
          if (typeof value === 'string') {
            text.push(value);
          }
        });
      }
    }

    // Extract from related searches
    if (data.relatedSearches) {
      data.relatedSearches.forEach(item => {
        if (item.query) {
          text.push(item.query);
        }
      });
    }

    // Process text for word cloud
    const combinedText = text.join(' ');
    const words = this.processTextForWordCloud(combinedText);

    return {
      text: combinedText,
      words: words,
      wordCount: words.length
    };
  }

  extractKnowledgeGraph(data) {
    if (!data.knowledgeGraph) return null;

    return {
      title: data.knowledgeGraph.title,
      type: data.knowledgeGraph.type,
      description: data.knowledgeGraph.description,
      website: data.knowledgeGraph.website,
      imageUrl: data.knowledgeGraph.imageUrl,
      attributes: data.knowledgeGraph.attributes || {}
    };
  }

  extractOrganicResults(data) {
    if (!data.organic) return [];

    return data.organic.slice(0, 5).map(result => ({
      title: result.title,
      link: result.link,
      snippet: result.snippet,
      position: result.position
    }));
  }

  processTextForWordCloud(text) {
    // Expanded stop words list for better filtering
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those',
      'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his',
      'her', 'its', 'our', 'their', 'mine', 'yours', 'hers', 'ours', 'theirs', 'what', 'when', 'where',
      'why', 'how', 'who', 'which', 'than', 'then', 'now', 'here', 'there', 'more', 'most', 'many',
      'much', 'some', 'any', 'all', 'each', 'every', 'both', 'either', 'neither', 'other', 'another',
      'such', 'only', 'own', 'same', 'so', 'just', 'very', 'too', 'also', 'well', 'back', 'through',
      'during', 'before', 'after', 'above', 'below', 'up', 'down', 'out', 'off', 'over', 'under',
      'again', 'further', 'then', 'once', 'get', 'got', 'make', 'made', 'take', 'took', 'come', 'came',
      'go', 'went', 'see', 'saw', 'know', 'knew', 'think', 'thought', 'say', 'said', 'tell', 'told',
      'become', 'became', 'find', 'found', 'give', 'gave', 'use', 'used', 'work', 'worked', 'call', 'called',
      'try', 'tried', 'ask', 'asked', 'need', 'needed', 'feel', 'felt', 'seem', 'seemed', 'leave', 'left',
      'put', 'keep', 'kept', 'let', 'begin', 'began', 'help', 'helped', 'show', 'showed', 'hear', 'heard',
      'play', 'played', 'run', 'ran', 'move', 'moved', 'live', 'lived', 'believe', 'believed', 'bring', 'brought',
      'happen', 'happened', 'write', 'wrote', 'provide', 'provided', 'sit', 'sat', 'stand', 'stood', 'lose', 'lost',
      'pay', 'paid', 'meet', 'met', 'include', 'included', 'continue', 'continued', 'set', 'follow', 'followed',
      'stop', 'stopped', 'create', 'created', 'speak', 'spoke', 'read', 'allow', 'allowed', 'add', 'added',
      'spend', 'spent', 'grow', 'grew', 'open', 'opened', 'walk', 'walked', 'win', 'won', 'offer', 'offered',
      'remember', 'remembered', 'love', 'loved', 'consider', 'considered', 'appear', 'appeared', 'buy', 'bought',
      'wait', 'waited', 'serve', 'served', 'die', 'died', 'send', 'sent', 'expect', 'expected', 'build', 'built',
      'stay', 'stayed', 'fall', 'fell', 'cut', 'reach', 'reached', 'kill', 'killed', 'remain', 'remained'
    ]);

    // Clean and split text with better processing
    const words = text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ') // Remove punctuation
      .replace(/\s+/g, ' ') // Normalize whitespace
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word))
      .filter(word => !/^\d+$/.test(word)) // Remove pure numbers
      .filter(word => !/^(https?|www|com|org|net|edu|gov)/.test(word)) // Remove URL parts
      .filter(word => word.length < 20); // Remove very long words (likely URLs or errors)

    // Count word frequency
    const wordCount = {};
    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });

    // Convert to array and sort by frequency, return more words
    const sortedWords = Object.entries(wordCount)
      .sort(([,a], [,b]) => b - a)
      .filter(([word, count]) => count >= 2) // Only include words that appear at least twice
      .slice(0, 100) // Top 100 words (increased from 50)
      .map(([word, count]) => ({ word, count }));

    return sortedWords;
  }

  // Fallback methods when API fails
  generateFallbackRelatedKeywords(keyword) {
    const prefixes = ['best', 'top', 'how to', 'what is', 'why', 'when'];
    const suffixes = ['tips', 'guide', 'tutorial', 'examples', 'benefits', 'strategies'];

    const related = [];
    prefixes.forEach(prefix => related.push({ query: `${prefix} ${keyword}`, relevance: 'medium' }));
    suffixes.forEach(suffix => related.push({ query: `${keyword} ${suffix}`, relevance: 'medium' }));

    return related.slice(0, 8);
  }

  generateFallbackPeopleAlsoAsk(keyword) {
    return [
      { question: `What is ${keyword}?`, snippet: `Learn about ${keyword} and its applications.`, title: `${keyword} Guide`, link: '#' },
      { question: `How to use ${keyword}?`, snippet: `Step-by-step guide on using ${keyword} effectively.`, title: `${keyword} Tutorial`, link: '#' },
      { question: `Why is ${keyword} important?`, snippet: `Discover the importance and benefits of ${keyword}.`, title: `${keyword} Benefits`, link: '#' },
      { question: `Best ${keyword} practices?`, snippet: `Learn the best practices for ${keyword}.`, title: `${keyword} Best Practices`, link: '#' }
    ];
  }

  generateFallbackWordCloud(keyword) {
    const words = [
      { word: keyword, count: 10 },
      { word: 'guide', count: 8 },
      { word: 'tips', count: 7 },
      { word: 'best', count: 6 },
      { word: 'how', count: 5 },
      { word: 'tutorial', count: 4 },
      { word: 'examples', count: 3 },
      { word: 'benefits', count: 3 }
    ];

    return {
      text: `${keyword} guide tips best practices tutorial examples benefits`,
      words: words,
      wordCount: words.length
    };
  }
}

module.exports = new SerperService();
