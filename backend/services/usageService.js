const database = require('../config/database');

class UsageService {
  constructor() {
    this.limits = {
      'V1_DEFAULT_ACCESS': {
        monthly_articles: 5,
        features: ['basic_workflow', 'keyword_research', 'source_integration', 'basic_profiles']
      },
      'PRO': {
        monthly_articles: 100,
        features: ['high_volume_articles', 'advanced_workflow', 'priority_support', 'preset_management']
      },
      'MAX': {
        monthly_articles: -1, // unlimited
        features: ['unlimited_articles', 'all_features', 'dedicated_support', 'custom_development', 'sla_guarantee']
      }
    };
  }

  /**
   * Check if user can perform an action
   */
  async canPerformAction(userId, actionType = 'article_generation') {
    try {
      const user = await database.get('SELECT * FROM users WHERE id = ?', [userId]);
      if (!user) {
        throw new Error('User not found');
      }

      // Check subscription status
      if (!this.isSubscriptionActive(user)) {
        // For inactive subscription, downgrade to free plan
        await this.downgradeToFree(userId);
        user.plan_type = 'V1_DEFAULT_ACCESS';
        user.subscription_status = 'free';
      }

      const userLimits = this.limits[user.plan_type] || this.limits['V1_DEFAULT_ACCESS'];

      // Check specific action limits
      switch (actionType) {
        case 'article_generation':
          return await this.checkArticleLimit(user, userLimits);
        case 'keyword_research':
          return await this.checkFeatureAccess(user, 'advanced_keywords');
        case 'premium_ai':
          return await this.checkFeatureAccess(user, 'premium_ai');
        default:
          return true;
      }
    } catch (error) {
      console.error('❌ Usage check failed:', error.message);
      return false;
    }
  }

  /**
   * Check if user has reached article generation limit
   */
  async checkArticleLimit(user, userLimits) {
    if (userLimits.monthly_articles === -1) {
      return { allowed: true, remaining: -1 }; // unlimited
    }

    // Reset monthly count if needed
    await this.resetMonthlyCountIfNeeded(user);

    // Get current month's usage
    const currentUser = await database.get('SELECT monthly_article_count FROM users WHERE id = ?', [user.id]);
    const currentCount = currentUser.monthly_article_count || 0;
    const limit = userLimits.monthly_articles;

    return {
      allowed: currentCount < limit,
      remaining: Math.max(0, limit - currentCount),
      used: currentCount,
      limit: limit
    };
  }

  /**
   * Check if user has access to a specific feature
   */
  async checkFeatureAccess(user, featureName) {
    const userLimits = this.limits[user.plan_type] || this.limits['V1_DEFAULT_ACCESS'];
    
    return {
      allowed: userLimits.features.includes(featureName) || userLimits.features.includes('all_features'),
      feature: featureName,
      plan: user.plan_type
    };
  }

  /**
   * Check if user can use a specific AI model
   */
  async checkAIModelAccess(user, modelName) {
    const userLimits = this.limits[user.plan_type] || this.limits['V1_DEFAULT_ACCESS'];
    
    // Enterprise users have access to all models
    if (userLimits.ai_models.includes('all_models')) {
      return { allowed: true, model: modelName, plan: user.plan_type };
    }
    
    // Check specific model access
    const hasAccess = userLimits.ai_models.includes(modelName) || 
                      userLimits.ai_models.includes('all_models');
    
    return {
      allowed: hasAccess,
      model: modelName,
      plan: user.plan_type,
      available_models: userLimits.ai_models
    };
  }

  /**
   * Record usage when user performs an action
   */
  async recordUsage(userId, actionType = 'article_generation', metadata = {}) {
    try {
      // Log the usage
      await database.run(`
        INSERT INTO usage_logs (user_id, action_type, resource_consumed, metadata)
        VALUES (?, ?, ?, ?)
      `, [userId, actionType, 1, JSON.stringify(metadata)]);

      // Update user's monthly count for article generation
      if (actionType === 'article_generation') {
        await database.run(`
          UPDATE users SET 
            monthly_article_count = monthly_article_count + 1,
            total_article_count = total_article_count + 1,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `, [userId]);
      }

      console.log(`✅ Usage recorded for user ${userId}: ${actionType}`);
    } catch (error) {
      console.error('❌ Failed to record usage:', error.message);
      throw error;
    }
  }

  /**
   * Get user's usage statistics
   */
  async getUserUsage(userId) {
    try {
      const user = await database.get(`
        SELECT 
          monthly_article_count, 
          total_article_count, 
          monthly_reset_date,
          plan_type,
          subscription_status,
          subscription_current_period_end
        FROM users 
        WHERE id = ?
      `, [userId]);

      if (!user) {
        throw new Error('User not found');
      }

      const userLimits = this.limits[user.plan_type] || this.limits['V1_DEFAULT_ACCESS'];
      
      // Get usage for current month
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const monthlyUsage = await database.all(`
        SELECT action_type, COUNT(*) as count
        FROM usage_logs 
        WHERE user_id = ? AND created_at >= ?
        GROUP BY action_type
      `, [userId, startOfMonth.toISOString()]);

      return {
        plan: user.plan_type,
        subscription_status: user.subscription_status,
        monthly_articles: {
          used: user.monthly_article_count || 0,
          limit: userLimits.monthly_articles,
          remaining: userLimits.monthly_articles === -1 ? -1 : Math.max(0, userLimits.monthly_articles - (user.monthly_article_count || 0))
        },
        total_articles: user.total_article_count || 0,
        monthly_reset_date: user.monthly_reset_date,
        subscription_end_date: user.subscription_current_period_end,
        monthly_usage: monthlyUsage.reduce((acc, item) => {
          acc[item.action_type] = item.count;
          return acc;
        }, {}),
        features: userLimits.features
      };
    } catch (error) {
      console.error('❌ Failed to get user usage:', error.message);
      throw error;
    }
  }

  /**
   * Reset monthly usage count if needed
   */
  async resetMonthlyCountIfNeeded(user) {
    const now = new Date();
    const resetDate = new Date(user.monthly_reset_date);
    
    // If it's been more than a month since last reset
    if (now.getTime() - resetDate.getTime() > 30 * 24 * 60 * 60 * 1000) {
      await database.run(`
        UPDATE users SET 
          monthly_article_count = 0,
          monthly_reset_date = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [user.id]);
      
      console.log(`✅ Monthly usage reset for user ${user.id}`);
    }
  }

  /**
   * Check if subscription is active (including trial period)
   */
  isSubscriptionActive(user) {
    if (!user.subscription_status) return false;
    
    const activeStatuses = ['active', 'trialing'];
    if (!activeStatuses.includes(user.subscription_status)) return false;
    
    const now = new Date();
    
    // 检查试用期
    if (user.is_in_trial && user.trial_ends_at) {
      const trialEnd = new Date(user.trial_ends_at);
      if (now > trialEnd) {
        return false; // 试用期已过期
      }
      return true; // 试用期内
    }
    
    // 检查付费订阅期
    if (user.current_period_end) {
      const endDate = new Date(user.current_period_end);
      if (now > endDate) return false;
    }
    
    return true;
  }

  /**
   * Get trial status for user
   */
  getTrialStatus(user) {
    if (!user.is_in_trial || !user.trial_ends_at) {
      return { in_trial: false };
    }

    const now = new Date();
    const trialEnd = new Date(user.trial_ends_at);
    const trialStart = user.trial_starts_at ? new Date(user.trial_starts_at) : null;
    
    const daysRemaining = Math.max(0, Math.ceil((trialEnd - now) / (1000 * 60 * 60 * 24)));
    const totalTrialDays = trialStart ? Math.ceil((trialEnd - trialStart) / (1000 * 60 * 60 * 24)) : 7;
    
    return {
      in_trial: now <= trialEnd,
      trial_ends_at: user.trial_ends_at,
      days_remaining: daysRemaining,
      total_trial_days: totalTrialDays,
      is_expired: now > trialEnd
    };
  }

  /**
   * Downgrade user to free plan
   */
  async downgradeToFree(userId) {
    try {
      await database.run(`
        UPDATE users SET 
          plan_type = 'V1_DEFAULT_ACCESS',
          subscription_status = 'free',
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [userId]);
      
      console.log(`✅ User ${userId} downgraded to free plan`);
    } catch (error) {
      console.error('❌ Failed to downgrade user:', error.message);
      throw error;
    }
  }

  /**
   * Get plan limits for display
   */
  getPlanLimits(planType = 'V1_DEFAULT_ACCESS') {
    return this.limits[planType] || this.limits['V1_DEFAULT_ACCESS'];
  }

  /**
   * Check if user needs to upgrade
   */
  async checkUpgradeNeeded(userId, requestedAction = 'article_generation') {
    const canPerform = await this.canPerformAction(userId, requestedAction);
    
    if (!canPerform.allowed) {
      const user = await database.get('SELECT plan_type FROM users WHERE id = ?', [userId]);
      const currentPlan = user?.plan_type || 'V1_DEFAULT_ACCESS';
      
      return {
        upgrade_needed: true,
        current_plan: currentPlan,
        suggested_plan: 'PRO',
        reason: this.getUpgradeReason(canPerform, requestedAction)
      };
    }
    
    return { upgrade_needed: false };
  }

  getUpgradeReason(canPerformResult, actionType) {
    if (actionType === 'article_generation') {
      return `You've reached your monthly limit of ${canPerformResult.limit} articles. Upgrade to Pro for unlimited articles.`;
    }
    return `This feature requires a Pro subscription. Upgrade to access advanced features.`;
  }
}

module.exports = new UsageService();