{"name": "ai-article-generator-backend", "version": "1.0.0", "description": "Backend API for Writer J", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "dev:local": "node -r dotenv/config server.js dotenv_config_path=.env.local", "dev:local:watch": "nodemon -r dotenv/config server.js dotenv_config_path=.env.local", "init-db": "node scripts/init-database.js", "init-db:local": "node scripts/init-db.js", "migrate": "node scripts/deploy-migration.js", "test-postgres": "node scripts/test-postgres-connection.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ai", "article", "generator", "api"], "author": "", "license": "ISC", "dependencies": {"@google/genai": "^1.1.0", "@paddle/paddle-node-sdk": "^2.7.3", "axios": "^1.6.8", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mime": "^4.0.7", "morgan": "^1.10.0", "node-cache": "^5.1.2", "node-fetch": "^2.7.0", "nodemailer": "^6.9.8", "openai": "^5.2.0", "pg": "^8.16.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.1.10"}}