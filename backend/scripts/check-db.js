// 加载环境变量
require('dotenv').config();

const database = require('../config/database');

async function checkDatabase() {
  console.log('🔗 正在连接数据库...');
  
  try {
    await database.connect();
    console.log('✅ 数据库连接成功\n');
    
    // 检查user_resources表是否存在
    console.log('📋 检查user_resources表...');
    const tableExists = await database.get(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'user_resources'
      );
    `);
    
    console.log('表是否存在:', tableExists.exists);
    
    if (tableExists.exists) {
      // 查询表结构
      console.log('\n📐 表结构:');
      const columns = await database.all(`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'user_resources'
        ORDER BY ordinal_position;
      `);
      
      columns.forEach(col => {
        console.log(`  ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
      });
      
      // 查询数据
      console.log('\n📊 数据统计:');
      const count = await database.get('SELECT COUNT(*) as count FROM user_resources');
      console.log(`总记录数: ${count.count}`);
      
      if (count.count > 0) {
        // 按类型统计
        const typeStats = await database.all(`
          SELECT resource_type, COUNT(*) as count 
          FROM user_resources 
          GROUP BY resource_type
        `);
        
        console.log('\n按类型统计:');
        typeStats.forEach(stat => {
          console.log(`  ${stat.resource_type}: ${stat.count} 个`);
        });
        
        // 查看前5条记录
        console.log('\n📝 前5条记录:');
        const samples = await database.all(`
          SELECT id, resource_name, resource_type, created_at 
          FROM user_resources 
          ORDER BY created_at DESC 
          LIMIT 5
        `);
        
        samples.forEach(sample => {
          console.log(`  ${sample.id}. [${sample.resource_type}] ${sample.resource_name}`);
          console.log(`     创建时间: ${sample.created_at}`);
        });
        
        // 检查迁移标签
        console.log('\n🏷️  检查迁移标签:');
        const migratedCount = await database.get(`
          SELECT COUNT(*) as count 
          FROM user_resources 
          WHERE tags LIKE '%migrated%'
        `);
        console.log(`包含"migrated"标签的资源: ${migratedCount.count} 个`);
        
      } else {
        console.log('⚠️  表存在但没有数据');
      }
    } else {
      console.log('❌ user_resources表不存在');
    }
    
  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error);
  } finally {
    if (database.pool) {
      await database.pool.end();
      console.log('\n🔐 数据库连接已关闭');
    }
  }
}

checkDatabase();