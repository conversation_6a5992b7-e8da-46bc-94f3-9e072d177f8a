// 加载环境变量
require('dotenv').config();

const database = require('../config/database');

async function clearResources() {
  console.log('🔗 正在连接数据库...');
  
  try {
    await database.connect();
    console.log('✅ 数据库连接成功\n');
    
    // 清空user_resources表
    console.log('🗑️  清空user_resources表...');
    await database.run('DELETE FROM user_resources');
    
    // 确认清空
    const count = await database.get('SELECT COUNT(*) as count FROM user_resources');
    console.log(`✅ 清空完成，当前记录数: ${count.count}`);
    
  } catch (error) {
    console.error('❌ 清空过程中发生错误:', error);
  } finally {
    if (database.pool) {
      await database.pool.end();
      console.log('\n🔐 数据库连接已关闭');
    }
  }
}

clearResources();