// 加载环境变量
require('dotenv').config();

const database = require('../config/database');

/**
 * 验证迁移结果的脚本
 */

class MigrationVerifier {
  constructor() {
    this.results = {
      totalUsers: 0,
      totalResources: 0,
      resourcesByType: {},
      migratedResources: 0,
      recentMigrations: [],
      orphanedTasks: 0,
      duplicateResources: 0
    };
  }

  // 验证用户资源统计
  async verifyUserResources() {
    console.log('📊 验证用户资源统计...\n');

    // 总用户数和资源数
    const userCount = await database.get('SELECT COUNT(DISTINCT user_id) as count FROM user_resources');
    const resourceCount = await database.get('SELECT COUNT(*) as count FROM user_resources');
    
    this.results.totalUsers = userCount.count;
    this.results.totalResources = resourceCount.count;

    console.log(`总用户数: ${this.results.totalUsers}`);
    console.log(`总资源数: ${this.results.totalResources}`);

    // 按类型统计资源
    const typeStats = await database.all(`
      SELECT resource_type, COUNT(*) as count 
      FROM user_resources 
      GROUP BY resource_type
      ORDER BY count DESC
    `);

    console.log('\n按类型统计资源:');
    for (const stat of typeStats) {
      this.results.resourcesByType[stat.resource_type] = stat.count;
      console.log(`  ${stat.resource_type}: ${stat.count} 个`);
    }

    // 迁移的资源数量
    const migratedCount = await database.get(`
      SELECT COUNT(*) as count 
      FROM user_resources 
      WHERE tags LIKE '%migrated%'
    `);
    
    this.results.migratedResources = migratedCount.count;
    console.log(`\n迁移的资源: ${this.results.migratedResources} 个`);
  }

  // 验证最近的迁移
  async verifyRecentMigrations() {
    console.log('\n📅 最近迁移的资源 (前10个):');

    const recentMigrations = await database.all(`
      SELECT resource_name, resource_type, created_at, user_id
      FROM user_resources 
      WHERE tags LIKE '%migrated%'
      ORDER BY created_at DESC 
      LIMIT 10
    `);

    this.results.recentMigrations = recentMigrations;

    if (recentMigrations.length === 0) {
      console.log('  没有发现迁移的资源');
    } else {
      recentMigrations.forEach((resource, index) => {
        console.log(`  ${index + 1}. [${resource.resource_type}] ${resource.resource_name}`);
        console.log(`     用户: ${resource.user_id} | 时间: ${resource.created_at}`);
      });
    }
  }

  // 检查潜在的重复资源
  async checkDuplicateResources() {
    console.log('\n🔍 检查潜在重复资源...');

    // 检查相同URL的资源
    const duplicateUrls = await database.all(`
      SELECT resource_url, COUNT(*) as count, user_id
      FROM user_resources 
      WHERE resource_url IS NOT NULL 
      GROUP BY resource_url, user_id
      HAVING COUNT(*) > 1
      ORDER BY count DESC
      LIMIT 10
    `);

    if (duplicateUrls.length > 0) {
      console.log(`\n⚠️  发现 ${duplicateUrls.length} 组重复URL资源:`);
      duplicateUrls.forEach((dup, index) => {
        console.log(`  ${index + 1}. 用户 ${dup.user_id}: ${dup.resource_url} (${dup.count} 次重复)`);
      });
      this.results.duplicateResources = duplicateUrls.length;
    } else {
      console.log('✅ 没有发现重复的URL资源');
    }

    // 检查相同标题的资源
    const duplicateTitles = await database.all(`
      SELECT resource_name, COUNT(*) as count, user_id
      FROM user_resources 
      WHERE resource_name != ''
      GROUP BY resource_name, user_id
      HAVING COUNT(*) > 1
      ORDER BY count DESC
      LIMIT 5
    `);

    if (duplicateTitles.length > 0) {
      console.log(`\n📝 相同标题的资源 (前5组):`);
      duplicateTitles.forEach((dup, index) => {
        console.log(`  ${index + 1}. 用户 ${dup.user_id}: "${dup.resource_name}" (${dup.count} 次)`);
      });
    }
  }

  // 检查任务数据完整性
  async checkTaskDataIntegrity() {
    console.log('\n🔗 检查任务数据完整性...');

    // 检查还有多少任务包含资源数据
    const tasksWithSources = await database.get(`
      SELECT COUNT(*) as count
      FROM tasks 
      WHERE reddit_sources IS NOT NULL 
         OR comprehensive_resources IS NOT NULL 
         OR sources IS NOT NULL
    `);

    console.log(`仍包含资源数据的任务: ${tasksWithSources.count} 个`);

    // 检查用户是否有对应的资源
    const usersWithTasks = await database.all(`
      SELECT DISTINCT user_id 
      FROM tasks 
      WHERE reddit_sources IS NOT NULL 
         OR comprehensive_resources IS NOT NULL 
         OR sources IS NOT NULL
    `);

    const usersWithResources = await database.all(`
      SELECT DISTINCT user_id 
      FROM user_resources
      WHERE tags LIKE '%migrated%'
    `);

    const taskUserIds = new Set(usersWithTasks.map(u => u.user_id));
    const resourceUserIds = new Set(usersWithResources.map(u => u.user_id));

    const orphanedUsers = [...taskUserIds].filter(id => !resourceUserIds.has(id));
    
    if (orphanedUsers.length > 0) {
      console.log(`⚠️  有任务数据但无迁移资源的用户: ${orphanedUsers.length} 个`);
      console.log(`   用户ID: ${orphanedUsers.join(', ')}`);
    } else {
      console.log('✅ 所有有任务资源的用户都有对应的迁移资源');
    }
  }

  // 验证数据质量
  async verifyDataQuality() {
    console.log('\n🎯 验证数据质量...');

    // 检查空内容的资源
    const emptyContent = await database.get(`
      SELECT COUNT(*) as count
      FROM user_resources 
      WHERE (resource_content IS NULL OR resource_content = '')
        AND (resource_url IS NULL OR resource_url = '')
    `);

    if (emptyContent.count > 0) {
      console.log(`⚠️  发现 ${emptyContent.count} 个既无内容又无URL的资源`);
    } else {
      console.log('✅ 所有资源都有内容或URL');
    }

    // 检查标签格式
    const invalidTags = await database.all(`
      SELECT id, resource_name, tags
      FROM user_resources 
      WHERE tags IS NOT NULL 
        AND tags != ''
        AND (tags NOT LIKE '[%' OR tags NOT LIKE '%]')
      LIMIT 5
    `);

    if (invalidTags.length > 0) {
      console.log(`⚠️  发现 ${invalidTags.length} 个标签格式可能有问题的资源`);
      invalidTags.forEach((resource, index) => {
        console.log(`  ${index + 1}. ID ${resource.id}: ${resource.resource_name}`);
        console.log(`     标签: ${resource.tags}`);
      });
    } else {
      console.log('✅ 所有标签格式正确');
    }

    // 检查使用统计
    const usageStats = await database.get(`
      SELECT 
        AVG(usage_count) as avg_usage,
        MAX(usage_count) as max_usage,
        COUNT(CASE WHEN usage_count > 0 THEN 1 END) as used_count
      FROM user_resources
    `);

    console.log(`\n📈 使用统计:`);
    console.log(`  平均使用次数: ${parseFloat(usageStats.avg_usage || 0).toFixed(2)}`);
    console.log(`  最大使用次数: ${usageStats.max_usage || 0}`);
    console.log(`  已使用的资源: ${usageStats.used_count} 个`);
  }

  // 生成验证报告
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 迁移验证报告');
    console.log('='.repeat(60));
    
    console.log(`总用户数: ${this.results.totalUsers}`);
    console.log(`总资源数: ${this.results.totalResources}`);
    console.log(`迁移的资源: ${this.results.migratedResources}`);
    
    console.log('\n资源类型分布:');
    for (const [type, count] of Object.entries(this.results.resourcesByType)) {
      const percentage = ((count / this.results.totalResources) * 100).toFixed(1);
      console.log(`  ${type}: ${count} 个 (${percentage}%)`);
    }

    if (this.results.duplicateResources > 0) {
      console.log(`\n⚠️  发现 ${this.results.duplicateResources} 组重复资源，建议手动检查`);
    }

    const migrationRate = ((this.results.migratedResources / this.results.totalResources) * 100).toFixed(1);
    console.log(`\n📊 迁移率: ${migrationRate}%`);

    if (this.results.migratedResources > 0) {
      console.log('✅ 迁移成功完成！');
    } else {
      console.log('❌ 没有发现迁移的资源，可能需要重新运行迁移脚本');
    }
  }

  // 主验证函数
  async verify() {
    console.log('🔍 开始验证迁移结果...\n');

    try {
      await this.verifyUserResources();
      await this.verifyRecentMigrations();
      await this.checkDuplicateResources();
      await this.checkTaskDataIntegrity();
      await this.verifyDataQuality();
      
      this.generateReport();

    } catch (error) {
      console.error('❌ 验证过程中发生错误:', error);
      throw error;
    }
  }
}

// 主函数
async function main() {
  console.log('🔗 正在连接数据库...');
  
  try {
    // 初始化数据库连接
    await database.connect();
    console.log('✅ 数据库连接成功\n');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }

  const verifier = new MigrationVerifier();

  try {
    await verifier.verify();
  } catch (error) {
    console.error('验证脚本执行失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    if (database.pool) {
      await database.pool.end();
      console.log('\n🔐 数据库连接已关闭');
    }
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = MigrationVerifier;