// 加载环境变量
require('dotenv').config();

const database = require('../config/database');

async function testInsert() {
  console.log('🔗 正在连接数据库...');
  
  try {
    await database.connect();
    console.log('✅ 数据库连接成功\n');
    
    // 测试插入一个资源
    console.log('📝 测试插入资源...');
    const query = `
      INSERT INTO user_resources (
        user_id, resource_name, resource_type, resource_url,
        resource_content, description, tags, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
    `;
    
    const params = [
      1, // user_id
      'Test Resource', // resource_name
      'url', // resource_type
      'https://example.com', // resource_url
      'Test content', // resource_content
      'Test description', // description
      '["test", "migrated"]' // tags
    ];

    await database.run(query, params);
    console.log('✅ 插入成功');
    
    // 查询确认
    console.log('\n📊 查询结果:');
    const result = await database.get('SELECT COUNT(*) as count FROM user_resources');
    console.log(`总记录数: ${result.count}`);
    
    const latest = await database.get(`
      SELECT resource_name, resource_type, created_at 
      FROM user_resources 
      ORDER BY created_at DESC 
      LIMIT 1
    `);
    
    if (latest) {
      console.log(`最新记录: ${latest.resource_name} (${latest.resource_type})`);
      console.log(`创建时间: ${latest.created_at}`);
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    if (database.pool) {
      await database.pool.end();
      console.log('\n🔐 数据库连接已关闭');
    }
  }
}

testInsert();