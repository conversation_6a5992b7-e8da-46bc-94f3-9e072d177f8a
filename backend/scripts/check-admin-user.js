const database = require('../config/database');

async function checkAdminUser() {
  console.log('🔍 Checking admin user...');
  
  try {
    // Connect to database
    await database.connect();
    
    // Check for admin user
    const adminUser = await database.get(
      'SELECT id, email, full_name, role, email_verified, created_at FROM users WHERE email = ?',
      ['<EMAIL>']
    );
    
    if (adminUser) {
      console.log('✅ Admin user found:');
      console.log('  ID:', adminUser.id);
      console.log('  Email:', adminUser.email);
      console.log('  Name:', adminUser.full_name);
      console.log('  Role:', adminUser.role);
      console.log('  Email Verified:', adminUser.email_verified);
      console.log('  Created:', adminUser.created_at);
    } else {
      console.log('❌ Admin user not found');
    }
    
    // Check all users with admin role
    const allAdmins = await database.all(
      'SELECT id, email, full_name, role FROM users WHERE role = ?',
      ['admin']
    );
    
    console.log(`\n👥 Found ${allAdmins.length} admin users:`);
    allAdmins.forEach(admin => {
      console.log(`  - ${admin.email} (ID: ${admin.id}, Role: ${admin.role})`);
    });
    
    // Check all users
    const allUsers = await database.all(
      'SELECT id, email, role FROM users ORDER BY created_at'
    );
    
    console.log(`\n📊 All users (${allUsers.length} total):`);
    allUsers.forEach(user => {
      console.log(`  - ${user.email} (Role: ${user.role || 'user'})`);
    });
    
    // Close database connection
    await database.close();
    console.log('\n🎉 Admin user check completed!');
    
  } catch (error) {
    console.error('❌ Error checking admin user:', error);
    process.exit(1);
  }
}

// Run the check
checkAdminUser();
