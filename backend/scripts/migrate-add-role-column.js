const database = require('../config/database');

async function migrateAddRoleColumn() {
  console.log('🔄 Running migration to add role column...');
  
  try {
    // Connect to database
    await database.connect();
    
    // Check if role column already exists
    try {
      await database.get('SELECT role FROM users LIMIT 1');
      console.log('✅ Role column already exists, no migration needed');
      return;
    } catch (error) {
      if (error.message.includes('no such column: role')) {
        console.log('📝 Role column not found, adding it...');
      } else {
        throw error;
      }
    }
    
    // Add role column to users table
    if (database.isPostgres) {
      await database.run('ALTER TABLE users ADD COLUMN role VARCHAR(20) DEFAULT \'user\'');
    } else {
      await database.run('ALTER TABLE users ADD COLUMN role TEXT DEFAULT \'user\'');
    }
    
    console.log('✅ Role column added successfully');
    
    // Close database connection
    await database.close();
    console.log('🎉 Migration completed!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
migrateAddRoleColumn();
