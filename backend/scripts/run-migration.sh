#!/bin/bash

# Source 迁移脚本执行器
# 提供安全的迁移执行流程

echo "🎯 Writer J - Source 迁移工具"
echo "================================"
echo ""

# 检查当前目录
if [ ! -f "scripts/migrate-sources.js" ]; then
    echo "❌ 错误: 请在 backend 目录下运行此脚本"
    echo "   正确路径: /path/to/backend/"
    exit 1
fi

# 检查环境变量
if [ ! -f ".env" ]; then
    echo "❌ 错误: 找不到 .env 文件"
    exit 1
fi

echo "📋 迁移前检查..."
echo ""

# 1. 首先运行干运行模式
echo "🔍 步骤 1: 分析现有数据..."
node scripts/migrate-sources.js

echo ""
echo "=========================================="
echo ""

# 2. 询问用户是否继续
echo "📊 以上是数据分析结果。"
echo ""
echo "⚠️  警告: 接下来将执行实际迁移，会修改数据库！"
echo ""
read -p "是否继续执行迁移? (y/N): " confirm

if [[ $confirm != [yY] && $confirm != [yY][eE][sS] ]]; then
    echo "❌ 迁移已取消"
    exit 0
fi

echo ""
echo "🚀 开始执行迁移..."
echo ""

# 3. 执行实际迁移
node scripts/migrate-sources.js --migrate

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 迁移完成！"
    echo ""
    echo "🔍 正在验证迁移结果..."
    
    # 4. 运行验证脚本
    node scripts/verify-migration.js
    
    echo ""
    echo "🎉 所有操作完成！"
    echo ""
    echo "💡 提示:"
    echo "   - 你可以在管理界面查看迁移的资源"
    echo "   - 可以再次运行验证脚本: node scripts/verify-migration.js"
    echo "   - 如有问题，请查看迁移日志"
else
    echo ""
    echo "❌ 迁移过程中发生错误，请检查日志"
    exit 1
fi