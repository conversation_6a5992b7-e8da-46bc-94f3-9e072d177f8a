# Source 迁移脚本使用说明

这个脚本用于将之前任务中使用过的 sources 迁移到新的资源管理系统中。

## 功能特性

- ✅ 从 tasks 表中提取所有资源数据
- ✅ 支持三种资源类型：`reddit_sources`、`comprehensive_resources`、`sources`
- ✅ 智能去重，避免重复资源
- ✅ 数据标准化和清洗
- ✅ 详细的迁移报告
- ✅ 干运行模式（分析不迁移）
- ✅ 错误处理和回滚安全

## 使用方法

### 1. 干运行模式（推荐先执行）

```bash
# 在 backend 目录下执行
cd /path/to/backend
node scripts/migrate-sources.js
```

这将分析现有数据并显示统计信息，**不会修改数据库**。

### 2. 实际迁移

```bash
# 执行实际迁移（会修改数据库）
node scripts/migrate-sources.js --migrate
```

⚠️ **注意**：实际迁移会修改数据库，请确保已备份数据！

## 脚本处理逻辑

### 数据来源

脚本会处理 `tasks` 表中以下 JSON 字段：

1. **`reddit_sources`** - Reddit 论坛资源
2. **`comprehensive_resources`** - 综合研究资源  
3. **`sources`** - 一般资源

### 去重策略

1. **内存去重**：同一次运行中避免重复处理
2. **数据库去重**：检查是否已存在相同 URL 或内容的资源
3. **优先级**：URL > 内容前100字符 > 标题

### 数据标准化

原始数据 → 标准化处理 → `user_resources` 表

- `resource_name`: 取 title/resource_name/name，默认为 "Imported Resource"
- `resource_type`: 自动识别为 url/reddit/manual
- `resource_url`: 统一URL字段
- `resource_content`: 内容字段
- `description`: 智能生成描述
- `tags`: 添加来源标签和 "migrated" 标签

## 输出报告

脚本执行完成后会显示详细统计：

```
📊 迁移完成统计报告
====================================
总任务数: 50
发现的资源总数: 234
成功迁移: 189
跳过重复: 45
错误数量: 0
```

## 常见问题

### Q: 如果脚本执行失败怎么办？
A: 脚本有完整的错误处理，单个资源失败不会影响整体迁移。所有错误都会记录在最终报告中。

### Q: 会不会覆盖现有的资源？
A: 不会。脚本会检查现有资源，跳过重复项。

### Q: 可以多次运行吗？
A: 可以。脚本设计为幂等性，多次运行只会处理新的资源。

### Q: 如何验证迁移结果？
A: 可以查询 `user_resources` 表，或在管理界面查看迁移的资源。

## 验证查询

迁移完成后，可以使用以下 SQL 查询验证结果：

```sql
-- 查看迁移的资源统计
SELECT 
  resource_type,
  COUNT(*) as count,
  COUNT(CASE WHEN tags LIKE '%migrated%' THEN 1 END) as migrated_count
FROM user_resources 
GROUP BY resource_type;

-- 查看最近迁移的资源
SELECT resource_name, resource_type, created_at 
FROM user_resources 
WHERE tags LIKE '%migrated%' 
ORDER BY created_at DESC 
LIMIT 10;
```

## 注意事项

1. **备份数据**：执行迁移前请备份数据库
2. **测试环境**：建议先在测试环境执行
3. **监控日志**：注意观察控制台输出的错误信息
4. **资源清理**：迁移完成后可以考虑清理 tasks 表中的冗余 JSON 数据（可选）