#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' });
const database = require('../config/database');

async function addDebugPromptColumn() {
  try {
    console.log('📋 Adding debug_prompt column to tasks table...');
    
    // Connect to database
    await database.connect();
    
    // Add the debug_prompt column
    await database.run(`
      ALTER TABLE tasks 
      ADD COLUMN IF NOT EXISTS debug_prompt TEXT
    `);
    
    console.log('✅ Successfully added debug_prompt column to tasks table');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Failed to add debug_prompt column:', error.message);
    process.exit(1);
  }
}

// Run the migration
addDebugPromptColumn();