// 加载环境变量
require('dotenv').config();

const database = require('../config/database');

async function debugCheck() {
  console.log('🔗 正在连接数据库...');
  
  try {
    await database.connect();
    console.log('✅ 数据库连接成功\n');
    
    // 模拟 checkExistingResource 的逻辑
    const testResource = {
      user_id: 1,
      resource_name: 'Test Resource',
      resource_url: 'https://example.com'
    };
    
    console.log('🔍 测试资源检查逻辑...');
    console.log('测试资源:', testResource);
    
    // 通过URL检查
    const query = 'SELECT id FROM user_resources WHERE user_id = $1 AND resource_url = $2';
    const params = [testResource.user_id, testResource.resource_url];
    
    console.log('查询SQL:', query);
    console.log('参数:', params);
    
    const existing = await database.get(query, params);
    console.log('查询结果:', existing);
    console.log('existing !== undefined:', existing !== undefined);
    console.log('existing !== null && existing !== undefined:', existing !== null && existing !== undefined);
    
    // 测试另一个不存在的资源
    console.log('\n🔍 测试不存在的资源...');
    const notExistResource = {
      user_id: 1,
      resource_name: 'Not Exist Resource',
      resource_url: 'https://notexist.com'
    };
    
    const query2 = 'SELECT id FROM user_resources WHERE user_id = $1 AND resource_url = $2';
    const params2 = [notExistResource.user_id, notExistResource.resource_url];
    
    console.log('查询SQL:', query2);
    console.log('参数:', params2);
    
    const existing2 = await database.get(query2, params2);
    console.log('查询结果:', existing2);
    console.log('existing2 !== undefined:', existing2 !== undefined);
    console.log('existing2 !== null && existing2 !== undefined:', existing2 !== null && existing2 !== undefined);
    
  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
  } finally {
    if (database.pool) {
      await database.pool.end();
      console.log('\n🔐 数据库连接已关闭');
    }
  }
}

debugCheck();