// 加载环境变量
require('dotenv').config();

const database = require('../config/database');

/**
 * 迁移脚本：将任务中的sources迁移到user_resources表
 * 处理 tasks 表中的以下JSON字段：
 * - reddit_sources
 * - comprehensive_resources  
 * - sources
 */

class SourceMigrator {
  constructor() {
    this.stats = {
      totalTasks: 0,
      totalSourcesFound: 0,
      duplicatesSkipped: 0,
      successfullyMigrated: 0,
      errors: []
    };
    this.seenResources = new Map(); // 用于去重
  }

  // 生成资源的唯一标识符
  generateResourceKey(resource) {
    // 优先使用URL，其次使用内容的前100个字符
    if (resource.url && resource.url.trim()) {
      return `url:${resource.url.trim().toLowerCase()}`;
    }
    if (resource.content && resource.content.trim()) {
      return `content:${resource.content.trim().substring(0, 100).toLowerCase()}`;
    }
    if (resource.title && resource.title.trim()) {
      return `title:${resource.title.trim().toLowerCase()}`;
    }
    return `id:${resource.id || Math.random()}`;
  }

  // 标准化资源数据
  normalizeResource(sourceData, sourceType, userId) {
    const normalized = {
      user_id: userId,
      resource_name: '',
      resource_type: 'manual', // 默认类型
      resource_url: null,
      resource_content: '',
      description: '',
      tags: []
    };

    // 确定资源名称
    normalized.resource_name = sourceData.title || 
                               sourceData.resource_name || 
                               sourceData.name ||
                               `Imported ${sourceType} Resource`;

    // 确定资源类型
    if (sourceData.type) {
      normalized.resource_type = sourceData.type;
    } else if (sourceType === 'reddit_sources') {
      normalized.resource_type = 'reddit';
    } else if (sourceData.url || sourceData.resource_url) {
      normalized.resource_type = 'url';
    }

    // 设置URL
    normalized.resource_url = sourceData.url || 
                              sourceData.resource_url || 
                              sourceData.link || 
                              null;

    // 设置内容
    normalized.resource_content = sourceData.content || 
                                  sourceData.resource_content || 
                                  sourceData.description ||
                                  '';

    // 设置描述
    if (sourceData.description && sourceData.description !== normalized.resource_content) {
      normalized.description = sourceData.description;
    } else if (normalized.resource_content && normalized.resource_content.length > 200) {
      normalized.description = normalized.resource_content.substring(0, 200) + '...';
    } else {
      normalized.description = `Migrated from task ${sourceType}`;
    }

    // 设置标签
    const tags = [];
    if (sourceData.tags && Array.isArray(sourceData.tags)) {
      tags.push(...sourceData.tags);
    }
    if (sourceType === 'reddit_sources') {
      tags.push('reddit');
    }
    tags.push('migrated');
    
    normalized.tags = JSON.stringify([...new Set(tags)]); // 去重

    return normalized;
  }

  // 处理单个任务的资源
  async processTaskSources(task) {
    const { id: taskId, user_id: userId } = task;
    let taskSourceCount = 0;

    console.log(`\n处理任务 ${taskId} (用户 ${userId}):`);

    // 处理 reddit_sources
    if (task.reddit_sources) {
      try {
        const redditSources = JSON.parse(task.reddit_sources);
        if (Array.isArray(redditSources)) {
          for (const source of redditSources) {
            await this.processSource(source, 'reddit_sources', userId);
            taskSourceCount++;
          }
          console.log(`  - Reddit sources: ${redditSources.length} 个`);
        }
      } catch (error) {
        console.error(`  ❌ 解析 reddit_sources 失败:`, error.message);
        this.stats.errors.push(`Task ${taskId}: reddit_sources解析错误 - ${error.message}`);
      }
    }

    // 处理 comprehensive_resources
    if (task.comprehensive_resources) {
      try {
        const comprehensiveResources = JSON.parse(task.comprehensive_resources);
        if (Array.isArray(comprehensiveResources)) {
          for (const source of comprehensiveResources) {
            await this.processSource(source, 'comprehensive_resources', userId);
            taskSourceCount++;
          }
          console.log(`  - Comprehensive resources: ${comprehensiveResources.length} 个`);
        }
      } catch (error) {
        console.error(`  ❌ 解析 comprehensive_resources 失败:`, error.message);
        this.stats.errors.push(`Task ${taskId}: comprehensive_resources解析错误 - ${error.message}`);
      }
    }

    // 处理 sources
    if (task.sources) {
      try {
        const sources = JSON.parse(task.sources);
        if (Array.isArray(sources)) {
          for (const source of sources) {
            await this.processSource(source, 'sources', userId);
            taskSourceCount++;
          }
          console.log(`  - General sources: ${sources.length} 个`);
        }
      } catch (error) {
        console.error(`  ❌ 解析 sources 失败:`, error.message);
        this.stats.errors.push(`Task ${taskId}: sources解析错误 - ${error.message}`);
      }
    }

    console.log(`  📊 任务总计: ${taskSourceCount} 个资源`);
    return taskSourceCount;
  }

  // 处理单个资源
  async processSource(sourceData, sourceType, userId) {
    this.stats.totalSourcesFound++;

    const resourceKey = this.generateResourceKey(sourceData);
    
    // 检查是否已经处理过
    if (this.seenResources.has(resourceKey)) {
      this.stats.duplicatesSkipped++;
      console.log(`    ⏭️  跳过重复资源: ${sourceData.title || sourceData.resource_name || '未命名'}`);
      return;
    }

    try {
      const normalizedResource = this.normalizeResource(sourceData, sourceType, userId);
      
      // 检查数据库中是否已存在相同资源
      const existingCheck = await this.checkExistingResource(normalizedResource);
      if (existingCheck) {
        this.stats.duplicatesSkipped++;
        console.log(`    ⏭️  数据库中已存在: ${normalizedResource.resource_name}`);
        this.seenResources.set(resourceKey, true);
        return;
      }

      // 插入到数据库
      await this.insertResource(normalizedResource);
      this.seenResources.set(resourceKey, true);
      this.stats.successfullyMigrated++;
      console.log(`    ✅ 已迁移: ${normalizedResource.resource_name}`);
      
    } catch (error) {
      console.error(`    ❌ 迁移失败:`, error.message);
      this.stats.errors.push(`Resource migration error: ${error.message}`);
    }
  }

  // 检查资源是否已存在
  async checkExistingResource(resource) {
    let query = '';
    let params = [];

    if (resource.resource_url) {
      // 通过URL检查
      query = 'SELECT id FROM user_resources WHERE user_id = $1 AND resource_url = $2';
      params = [resource.user_id, resource.resource_url];
    } else {
      // 通过名称和内容检查
      query = 'SELECT id FROM user_resources WHERE user_id = $1 AND resource_name = $2 AND resource_content = $3';
      params = [resource.user_id, resource.resource_name, resource.resource_content];
    }

    try {
      const existing = await database.get(query, params);
      return existing !== null && existing !== undefined;
    } catch (error) {
      console.error('检查现有资源时出错:', error);
      return false;
    }
  }

  // 插入资源到数据库
  async insertResource(resource) {
    const query = `
      INSERT INTO user_resources (
        user_id, resource_name, resource_type, resource_url,
        resource_content, description, tags, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
    `;
    
    const params = [
      resource.user_id,
      resource.resource_name,
      resource.resource_type,
      resource.resource_url,
      resource.resource_content,
      resource.description,
      resource.tags
    ];

    await database.run(query, params);
  }

  // 执行迁移
  async migrate() {
    console.log('🚀 开始迁移任务中的sources到资源管理系统...\n');

    try {
      // 获取所有包含资源数据的任务
      const query = `
        SELECT id, user_id, reddit_sources, comprehensive_resources, sources 
        FROM tasks 
        WHERE reddit_sources IS NOT NULL 
           OR comprehensive_resources IS NOT NULL 
           OR sources IS NOT NULL
        ORDER BY user_id, created_at
      `;
      
      const tasks = await database.all(query);
      this.stats.totalTasks = tasks.length;

      console.log(`📋 找到 ${tasks.length} 个包含资源数据的任务\n`);

      // 处理每个任务
      for (const task of tasks) {
        await this.processTaskSources(task);
      }

      // 打印最终统计
      this.printFinalStats();

    } catch (error) {
      console.error('❌ 迁移过程中发生错误:', error);
      throw error;
    }
  }

  // 打印最终统计
  printFinalStats() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 迁移完成统计报告');
    console.log('='.repeat(60));
    console.log(`总任务数: ${this.stats.totalTasks}`);
    console.log(`发现的资源总数: ${this.stats.totalSourcesFound}`);
    console.log(`成功迁移: ${this.stats.successfullyMigrated}`);
    console.log(`跳过重复: ${this.stats.duplicatesSkipped}`);
    console.log(`错误数量: ${this.stats.errors.length}`);
    
    if (this.stats.errors.length > 0) {
      console.log('\n❌ 错误详情:');
      this.stats.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }
    
    console.log('\n✅ 迁移完成！');
  }

  // 干运行模式 - 只分析不实际迁移
  async dryRun() {
    console.log('🔍 干运行模式：分析任务中的资源数据（不会实际迁移）\n');

    try {
      const query = `
        SELECT id, user_id, reddit_sources, comprehensive_resources, sources 
        FROM tasks 
        WHERE reddit_sources IS NOT NULL 
           OR comprehensive_resources IS NOT NULL 
           OR sources IS NOT NULL
        ORDER BY user_id, created_at
      `;
      
      const tasks = await database.all(query);
      console.log(`📋 找到 ${tasks.length} 个包含资源数据的任务\n`);

      for (const task of tasks) {
        console.log(`\n任务 ${task.id} (用户 ${task.user_id}):`);
        
        // 分析各种资源
        if (task.reddit_sources) {
          try {
            const redditSources = JSON.parse(task.reddit_sources);
            console.log(`  - Reddit sources: ${redditSources.length} 个`);
          } catch (e) {
            console.log(`  - Reddit sources: 解析错误`);
          }
        }

        if (task.comprehensive_resources) {
          try {
            const comprehensiveResources = JSON.parse(task.comprehensive_resources);
            console.log(`  - Comprehensive resources: ${comprehensiveResources.length} 个`);
          } catch (e) {
            console.log(`  - Comprehensive resources: 解析错误`);
          }
        }

        if (task.sources) {
          try {
            const sources = JSON.parse(task.sources);
            console.log(`  - General sources: ${sources.length} 个`);
          } catch (e) {
            console.log(`  - General sources: 解析错误`);
          }
        }
      }

      console.log('\n🔍 干运行完成 - 使用 --migrate 参数执行实际迁移');

    } catch (error) {
      console.error('❌ 干运行过程中发生错误:', error);
      throw error;
    }
  }
}

// 主函数
async function main() {
  console.log('🔗 正在连接数据库...');
  
  try {
    // 初始化数据库连接
    await database.connect();
    console.log('✅ 数据库连接成功\n');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }

  const migrator = new SourceMigrator();
  
  // 检查命令行参数
  const args = process.argv.slice(2);
  const isDryRun = !args.includes('--migrate');

  try {
    if (isDryRun) {
      await migrator.dryRun();
    } else {
      console.log('⚠️  这将会修改数据库！请确保已备份数据。');
      console.log('按 Ctrl+C 取消，或等待 5 秒后开始迁移...\n');
      
      // 等待5秒
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      await migrator.migrate();
    }
  } catch (error) {
    console.error('脚本执行失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    if (database.pool) {
      await database.pool.end();
      console.log('\n🔐 数据库连接已关闭');
    }
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = SourceMigrator;