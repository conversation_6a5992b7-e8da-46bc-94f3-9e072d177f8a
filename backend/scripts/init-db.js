#!/usr/bin/env node

const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/writer_j_dev';

async function initializeDatabase() {
  try {
    console.log('🔗 Connecting to PostgreSQL...');
    console.log(`📊 Database should already exist: writer_j_dev`);

    // Directly connect to the actual database and initialize tables
    console.log('📋 Initializing database tables...');
    const database = require('../config/database');
    await database.connect();
    console.log('✅ Database tables initialized successfully');

    // Initialize default data
    console.log('🔧 Setting up default configurations...');
    const aiServiceManager = require('../services/aiServiceManager');
    await aiServiceManager.initialize();
    console.log('✅ Default configurations set up successfully');

    console.log('\n🎉 Database initialization completed!');
    console.log('\nYou can now start the server with:');
    console.log('npm run dev');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    process.exit(1);
  }
}

// Run initialization
initializeDatabase();