# Local Development Setup Guide

This guide will help you set up Writer J locally using PostgreSQL.

## Prerequisites

✅ You have PostgreSQL installed locally  
✅ Node.js 18+ installed  
✅ npm or yarn package manager  

## Step 1: PostgreSQL Database Setup

### 1.1 Start PostgreSQL service
```bash
# On macOS (if using Homebrew)
brew services start postgresql

# On Ubuntu/Debian
sudo service postgresql start

# On Windows
# Use pgAdmin or start from Services
```

### 1.2 Create PostgreSQL user (if needed)
```bash
# Connect to PostgreSQL as superuser
sudo -u postgres psql

# Create user with password
CREATE USER postgres WITH PASSWORD 'password';
ALTER USER postgres CREATEDB;
\q
```

## Step 2: Backend Setup

### 2.1 Navigate to backend directory
```bash
cd j-writer/backend
```

### 2.2 Install dependencies
```bash
npm install
```

### 2.3 Configure environment variables
Edit `backend/.env.local` and update the following:

```env
# Update database URL with your actual PostgreSQL credentials
DATABASE_URL=postgresql://postgres:your_password@localhost:5432/writer_j_dev

# Add your AI API keys
GEMINI_API_KEY=your_actual_gemini_api_key
DEEPSEEK_API_KEY=your_actual_deepseek_api_key
SERPER_API_KEY=your_actual_serper_api_key

# JWT Secret (change this to something secure)
JWT_SECRET=your-super-secret-jwt-key-change-me-123456
```

### 2.4 Initialize the database
```bash
npm run init-db:local
```

This will:
- Create the `writer_j_dev` database
- Set up all required tables
- Initialize default AI models and prompt templates

### 2.5 Start the backend server
```bash
npm run dev:local:watch
```

The backend will be available at: http://localhost:3001

## Step 3: Frontend Setup

### 3.1 Navigate to frontend directory
```bash
cd ../ai-article-generator
```

### 3.2 Install dependencies
```bash
npm install
```

### 3.3 Start the frontend development server
```bash
npm run dev
```

The frontend will be available at: http://localhost:5173

## Step 4: Test the Setup

1. Open http://localhost:5173 in your browser
2. Register a new account or login
3. Try creating a new article or using the Advanced Batch Generator

## Troubleshooting

### Database Connection Issues
```bash
# Test PostgreSQL connection
psql -h localhost -U postgres -d postgres

# Check if PostgreSQL is running
sudo service postgresql status
```

### Backend Issues
```bash
# Check backend logs
npm run dev:local:watch

# Test database initialization
npm run init-db:local
```

### Frontend Issues
```bash
# Check if backend is running on port 3001
curl http://localhost:3001/health

# Clear npm cache and reinstall
rm -rf node_modules package-lock.json
npm install
```

## Development Workflow

### Backend Development
- Edit files in `backend/`
- Server auto-restarts with nodemon
- API available at http://localhost:3001

### Frontend Development  
- Edit files in `ai-article-generator/src/`
- Hot reload enabled via Vite
- App available at http://localhost:5173

### Database Management
- Use pgAdmin, DBeaver, or psql command line
- Database: `writer_j_dev`
- All tables are auto-created

## API Endpoints

- Health check: `GET /health`
- Authentication: `POST /api/auth/login`, `POST /api/auth/register`
- Tasks: `GET /api/tasks`, `POST /api/tasks`
- Advanced Batch: `POST /api/tasks/advanced-batch-generate`
- Admin: `GET /api/admin/*` (requires admin role)

## Next Steps

1. Add your actual AI API keys to `.env.local`
2. Test article generation functionality
3. Explore the Advanced Batch Generator
4. Check out the admin panel (create admin user first)

## Tips

- Use PostgreSQL for local development (matches production)
- Keep `.env.local` secure and never commit it
- Check browser console for frontend errors
- Check terminal for backend API errors
- Use database GUI tools for debugging