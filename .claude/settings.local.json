{"permissions": {"allow": ["Bash(git add:*)", "Bash(git push:*)", "Bash(grep:*)", "<PERSON><PERSON>(sudo:*)", "Bash(createuser:*)", "<PERSON><PERSON>(createdb:*)", "Bash(psql:*)", "Bash(npm install)", "Bash(npm run init-db:local:*)", "Bash(npm run dev:local:watch:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(env)", "<PERSON><PERSON>(cat:*)", "Bash(ls:*)", "<PERSON><PERSON>(chmod:*)", "Bash(npm run dev:*)", "Bash(git commit:*)", "Bash(./start-dev.sh:*)", "Bash(rm:*)", "Bash(PGPASSWORD=hiruben123 psql -h localhost -U hiruben -d writer_j_db -c \"SELECT name, description, content FROM prompt_templates WHERE category = 'content_creation' ORDER BY name;\")", "Bash(PGPASSWORD=Cl8479737 psql -h localhost -U hiruben -d writer_j_dev -c \"SELECT name, description FROM prompt_templates WHERE category = 'content_creation' ORDER BY name;\")", "Bash(PGPASSWORD=Cl8479737 psql -h localhost -U hiruben -d writer_j_dev -c \"\\d prompt_templates;\")", "Bash(rg:*)", "Bash(find:*)"], "deny": []}}